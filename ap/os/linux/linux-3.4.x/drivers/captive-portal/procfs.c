#include <linux/module.h>
#include <linux/kernel.h>
#include <linux/init.h>
#include <linux/proc_fs.h>
#include <linux/seq_file.h>
#include <linux/jiffies.h>
#include <asm/uaccess.h>
#include "speedlimit.h"

#define MODULE_NAME "cjportal"
#define NETRATE_LEN 8
#define PROTAL_LEN 8
#define AUTHMAC_LEN 128
#define WHITELIST_LEN 256

#ifdef CONFIG_DNS_REDIRECT
#define AUTH_IPV6_LEN 4096
#endif

static struct proc_dir_entry *rbMaster_dir,*authClient_file,*protalConf_file,*whitelist_file;
char authMac_data[AUTHMAC_LEN];
char protalConf_data[PROTAL_LEN];
char whitelist_data[WHITELIST_LEN];

#ifdef CONFIG_DNS_REDIRECT
static struct proc_dir_entry *auth_ipv6_file;
char auth_ipv6_data[AUTH_IPV6_LEN];
#endif
int portalLifetime = 60000*60;
//int portalLifetime = 60*60;  //default use disable portal
//extern struct mutex clientMutex;
extern atomic_t clientMutex;

extern void register_rbmaster_hook(void);
extern void unregister_rbmaster_hook(void);
extern void clientAuthorized(MACADR mac_adr);
extern void deleteAuthClient(MACADR mac_adr);

// 白名单管理相关的外部函数
extern __be32 whitelist_ipv4[MAX_WHITELIST_IP];
extern int whitelist_ipv4_count;
extern int add_ipv4_whitelist(const char *ip_str);
extern void clear_ipv4_whitelist(void);

#ifdef CONFIG_IPV6_PORTAL
extern struct in6_addr whitelist_v6[MAX_WHITELIST_IP];
extern int whitelist_v6_count;
extern int add_ipv6_whitelist_raw(u32 addr0, u32 addr1, u32 addr2, u32 addr3);
extern void clear_ipv6_whitelist(void);
#endif


static ssize_t proc_write_authMac(struct file *file, const char __user *buffer, size_t count, loff_t *ppos)
{
    int len;
    int mac[6];
    MACADR mac_adr;
    char command[10] = {0};

    if (count > AUTHMAC_LEN)
        len = AUTHMAC_LEN;
    else
        len = count;

    if (copy_from_user(authMac_data, buffer, len))
        return -EFAULT;

    printk("Received command: %s\n", authMac_data);

    // 澶勭悊 clear 鍛戒护
    if (strncmp(authMac_data, "clear", 5) == 0) {
        printk("Clearing all authenticated MACs\n");
        clearAuthList();
        goto cleanup;
    }

    // 澶勭悊 del 鍛戒护
    if (sscanf(authMac_data, "%9s %02x:%02x:%02x:%02x:%02x:%02x", 
              command, &mac[0], &mac[1], &mac[2], 
              &mac[3], &mac[4], &mac[5]) == 7) {
        
        if (strcmp(command, "del") != 0) {
            printk("Invalid command: %s\n", command);
            goto cleanup;
        }

        // 濉�鍏� MAC 缁撴瀯浣�
        mac_adr.mac_char.mac[0] = (u8)mac[0];
        mac_adr.mac_char.mac[1] = (u8)mac[1];
        mac_adr.mac_char.mac[2] = (u8)mac[2];
        mac_adr.mac_char.mac[3] = (u8)mac[3];
        mac_adr.mac_char.mac[4] = (u8)mac[4];
        mac_adr.mac_char.mac[5] = (u8)mac[5];

        printk("Deleting MAC: %02x:%02x:%02x:%02x:%02x:%02x\n",
               mac_adr.mac_char.mac[0], mac_adr.mac_char.mac[1],
               mac_adr.mac_char.mac[2], mac_adr.mac_char.mac[3],
               mac_adr.mac_char.mac[4], mac_adr.mac_char.mac[5]);

        deleteAuthClient(mac_adr); 

        goto cleanup;
    }

    // 榛樿�ゅ�勭悊娣诲姞 MAC 鐨勬祦绋�
    if (sscanf(authMac_data, "%02x:%02x:%02x:%02x:%02x:%02x",
               &mac[0], &mac[1], &mac[2], 
               &mac[3], &mac[4], &mac[5]) == 6) {

        mac_adr.mac_char.mac[0] = (u8)mac[0];
        mac_adr.mac_char.mac[1] = (u8)mac[1];
        mac_adr.mac_char.mac[2] = (u8)mac[2];
        mac_adr.mac_char.mac[3] = (u8)mac[3];
        mac_adr.mac_char.mac[4] = (u8)mac[4];
        mac_adr.mac_char.mac[5] = (u8)mac[5];

        clientAuthorized(mac_adr);
        goto cleanup;
    }

    printk("Invalid command format\n");
    len = -EINVAL;

cleanup:
    memset(authMac_data, 0, AUTHMAC_LEN);
    return len;
}

static int proc_show_auth_mac(struct seq_file *m, void *v)
{
    CLIENT_LIST *currentClient;
    
    seq_printf(m, "=== Authenticated MACs ===\n");
    
    // Lock the client list
    atomic_inc(&clientMutex);
    
    // Print all authenticated MACs
    currentClient = authClient;
    if (currentClient == NULL) {
        seq_printf(m, "No authenticated clients\n");
    } else {
        while (currentClient != NULL) {
            seq_printf(m, "MAC: %02x:%02x:%02x:%02x:%02x:%02x  IP: %d.%d.%d.%d  Time: %ld\n",
                currentClient->client.mac.mac_char.mac[0], currentClient->client.mac.mac_char.mac[1],
                currentClient->client.mac.mac_char.mac[2], currentClient->client.mac.mac_char.mac[3],
                currentClient->client.mac.mac_char.mac[4], currentClient->client.mac.mac_char.mac[5],
                NIPQUAD(currentClient->client.ip.int_ip), currentClient->client.time);
            currentClient = currentClient->next;
        }
    }
    
    seq_printf(m, "\n=== Unauthenticated MACs ===\n");
    
    // Print all unauthenticated MACs
    currentClient = newClient;
    if (currentClient == NULL) {
        seq_printf(m, "No unauthenticated clients\n");
    } else {
        while (currentClient != NULL) {
            seq_printf(m, "MAC: %02x:%02x:%02x:%02x:%02x:%02x  IP: %d.%d.%d.%d  Time: %ld\n",
                currentClient->client.mac.mac_char.mac[0], currentClient->client.mac.mac_char.mac[1],
                currentClient->client.mac.mac_char.mac[2], currentClient->client.mac.mac_char.mac[3],
                currentClient->client.mac.mac_char.mac[4], currentClient->client.mac.mac_char.mac[5],
                NIPQUAD(currentClient->client.ip.int_ip), currentClient->client.time);
            currentClient = currentClient->next;
        }
    }
    
    // Unlock the client list
    atomic_dec(&clientMutex);
    
    return 0;
}


static int proc_auth_open (struct inode *inode, struct file *file)
{
    return single_open(file, proc_show_auth_mac, inode->i_private);
}

static const struct file_operations auth_fops = {
	.owner = THIS_MODULE,
	.open = proc_auth_open,
	.read = seq_read,
	.write = proc_write_authMac,
	.llseek = seq_lseek,
	.release = single_release,
	
};

#ifdef CONFIG_DNS_REDIRECT
// IPv6-MAC映射管理函数
static ssize_t proc_write_auth_ipv6(struct file *file, const char __user *buffer, size_t count, loff_t *ppos)
{
    int len;

    if (count > AUTH_IPV6_LEN - 1)
        len = AUTH_IPV6_LEN - 1;
    else
        len = count;

    if (copy_from_user(auth_ipv6_data, buffer, len))
        return -EFAULT;

    auth_ipv6_data[len] = '\0';
    printk("IPv6-MAC mapping updated: %s\n", auth_ipv6_data);

    return len;
}

static int proc_show_auth_ipv6(struct seq_file *m, void *v)
{
    seq_printf(m, "%s", auth_ipv6_data);
    return 0;
}

static int proc_auth_ipv6_open(struct inode *inode, struct file *file)
{
    return single_open(file, proc_show_auth_ipv6, inode->i_private);
}

static const struct file_operations auth_ipv6_fops = {
    .owner = THIS_MODULE,
    .open = proc_auth_ipv6_open,
    .read = seq_read,
    .write = proc_write_auth_ipv6,
    .llseek = seq_lseek,
    .release = single_release,
};

// 保存IPv6-MAC映射的函数
void save_ipv6_mac_mapping(const char *entry)
{
    int current_len = strlen(auth_ipv6_data);
    int entry_len = strlen(entry);

    // 检查是否有足够空间
    if (current_len + entry_len < AUTH_IPV6_LEN - 1) {
        strcat(auth_ipv6_data, entry);
        printk("IPv6-MAC mapping saved: %s", entry);
    } else {
        printk("IPv6-MAC mapping buffer full, cannot save: %s", entry);
    }
}
#endif

static ssize_t proc_write_protalConf(struct file *file, const char __user *buffer, size_t count, loff_t *ppos)
{
    int len, portal = 0;
    if (count  > PROTAL_LEN)
        len = PROTAL_LEN;
    else
        len = count;

    if (copy_from_user(&protalConf_data,buffer,len))
        return -EFAULT;
    sscanf(protalConf_data,"%d",&portal);
    printk("cjportal read from input %d\n",portal);
    if(portal>5*60) { // packets not 0, enable rate restrict
        portalLifetime = portal;
//        register_rbmaster_hook();
    }
    else { // packets 0, don't restrict net rate
        portalLifetime = 60*60;
//	unregister_rbmaster_hook();
    }
    return len;
}

static int proc_show_portal_mac(struct seq_file *m, void *v)
{
	//seq_printf(m, "salvikie proc_show_portal_mac \n");
    seq_printf(m, "%d\n", portalLifetime);
	return 0;
}

static int proc_portal_open (struct inode *inode, struct file *file)
{
    return single_open(file, proc_show_portal_mac, inode->i_private);
}

static const struct file_operations portal_fops = {
	.owner = THIS_MODULE,
	.open = proc_portal_open,
	.read = seq_read,
	.write = proc_write_protalConf,
	.llseek = seq_lseek,
	.release = single_release,
};

// 白名单管理接口
static ssize_t proc_write_whitelist(struct file *file, const char __user *buffer, size_t count, loff_t *ppos)
{
    int len;
    char command[20] = {0};
    char ip_str[64] = {0};

    if (count > WHITELIST_LEN)
        len = WHITELIST_LEN;
    else
        len = count;

    if (copy_from_user(whitelist_data, buffer, len))
        return -EFAULT;

    whitelist_data[len] = '\0';
    printk("Whitelist command received: %s\n", whitelist_data);

    // 解析命令格式：add_ipv4 *********** 或 add_ipv6 2001:db8::1 或 clear
    if (sscanf(whitelist_data, "%19s %63s", command, ip_str) >= 1) {
        if (strcmp(command, "clear") == 0) {
            // 清空所有白名单
            clear_ipv4_whitelist();
#ifdef CONFIG_IPV6_PORTAL
            clear_ipv6_whitelist();
#endif
            printk("All whitelists cleared\n");
        } else if (strcmp(command, "add_ipv4") == 0) {
            // 添加 IPv4 地址到白名单
            if (strlen(ip_str) > 0) {
                if (add_ipv4_whitelist(ip_str) == 0) {
                    printk("Added IPv4 %s to whitelist\n", ip_str);
                } else {
                    printk("Failed to add IPv4 %s to whitelist\n", ip_str);
                }
            } else {
                printk("Invalid IPv4 address format\n");
            }
        } else if (strcmp(command, "clear_ipv4") == 0) {
            // 清空 IPv4 白名单
            clear_ipv4_whitelist();
            printk("IPv4 whitelist cleared\n");
#ifdef CONFIG_IPV6_PORTAL
        } else if (strcmp(command, "add_ipv6") == 0) {
            // 添加 IPv6 地址到白名单（简化版本，需要手动转换）
            u32 addr[4];
            if (sscanf(ip_str, "%x:%x:%x:%x", &addr[0], &addr[1], &addr[2], &addr[3]) == 4) {
                if (add_ipv6_whitelist_raw(addr[0], addr[1], addr[2], addr[3]) == 0) {
                    printk("Added IPv6 %s to whitelist\n", ip_str);
                } else {
                    printk("Failed to add IPv6 %s to whitelist\n", ip_str);
                }
            } else {
                printk("Invalid IPv6 address format (use hex format: 20014860:48600000:00000000:00008888)\n");
            }
        } else if (strcmp(command, "clear_ipv6") == 0) {
            // 清空 IPv6 白名单
            clear_ipv6_whitelist();
            printk("IPv6 whitelist cleared\n");
#endif
        } else {
            printk("Unknown whitelist command: %s\n", command);
            printk("Usage:\n");
            printk("  add_ipv4 <ip>     - Add IPv4 address\n");
            printk("  clear_ipv4        - Clear IPv4 whitelist\n");
#ifdef CONFIG_IPV6_PORTAL
            printk("  add_ipv6 <hex>    - Add IPv6 address (hex format)\n");
            printk("  clear_ipv6        - Clear IPv6 whitelist\n");
#endif
            printk("  clear             - Clear all whitelists\n");
        }
    } else {
        printk("Invalid command format\n");
    }

    return len;
}

static int proc_show_whitelist(struct seq_file *m, void *v)
{
    int i;

    seq_printf(m, "=== IPv4 Whitelist (%d entries) ===\n", whitelist_ipv4_count);
    for (i = 0; i < whitelist_ipv4_count; i++) {
        seq_printf(m, "%pI4\n", &whitelist_ipv4[i]);
    }

#ifdef CONFIG_IPV6_PORTAL
    seq_printf(m, "\n=== IPv6 Whitelist (%d entries) ===\n", whitelist_v6_count);
    for (i = 0; i < whitelist_v6_count; i++) {
        seq_printf(m, "%pI6\n", &whitelist_v6[i]);
    }
#endif

    seq_printf(m, "\n=== Usage ===\n");
    seq_printf(m, "echo 'add_ipv4 ***********' > /proc/cjportal/whitelist\n");
    seq_printf(m, "echo 'clear_ipv4' > /proc/cjportal/whitelist\n");
#ifdef CONFIG_IPV6_PORTAL
    seq_printf(m, "echo 'add_ipv6 20014860:48600000:00000000:00008888' > /proc/cjportal/whitelist\n");
    seq_printf(m, "echo 'clear_ipv6' > /proc/cjportal/whitelist\n");
#endif
    seq_printf(m, "echo 'clear' > /proc/cjportal/whitelist\n");

    return 0;
}

static int proc_whitelist_open(struct inode *inode, struct file *file)
{
    return single_open(file, proc_show_whitelist, inode->i_private);
}

static const struct file_operations whitelist_fops = {
    .owner = THIS_MODULE,
    .open = proc_whitelist_open,
    .read = seq_read,
    .write = proc_write_whitelist,
    .llseek = seq_lseek,
    .release = single_release,
};

int init_procfs_cjPortal(void)
{
    int rv = 0;

    rbMaster_dir = proc_mkdir (MODULE_NAME,NULL);
    if (rbMaster_dir == NULL)
    {
        rv = -ENOMEM;
        goto out;
    }

    authClient_file = proc_create ("auth",0644,rbMaster_dir, &auth_fops);
    if (authClient_file == NULL)
    {
        rv = -ENOMEM;
        goto out;  //should remove above file first ??
    }
    //strcpy(authMac_data,"0");
    //authClient_file->data = authMac_data;


    protalConf_file = proc_create ("portal",0644,rbMaster_dir, &portal_fops);
    if (protalConf_file == NULL)
    {
        rv = -ENOMEM;
        goto out;  //should remove above file first ??
    }
    //strcpy(protalConf_data,"0");
    //protalConf_file->data = protalConf_data;

    // 创建白名单管理接口
    whitelist_file = proc_create("whitelist", 0644, rbMaster_dir, &whitelist_fops);
    if (whitelist_file == NULL)
    {
        rv = -ENOMEM;
        goto out;
    }

#ifdef CONFIG_DNS_REDIRECT
    // 创建IPv6-MAC映射管理接口
    auth_ipv6_file = proc_create("auth_ipv6", 0644, rbMaster_dir, &auth_ipv6_fops);
    if (auth_ipv6_file == NULL)
    {
        rv = -ENOMEM;
        goto out;
    }
    // 初始化IPv6-MAC映射数据
    memset(auth_ipv6_data, 0, AUTH_IPV6_LEN);
#endif

    return 0;

out:
    return rv;
}
EXPORT_SYMBOL(init_procfs_cjPortal);

void cleanup_procfs_cjportal (void)
{
    remove_proc_entry(MODULE_NAME,NULL);
}
EXPORT_SYMBOL(cleanup_procfs_cjportal);
