#!/bin/sh

# OneLink MAC Authentication Control Script
# Usage: one_link_authenticated.sh <action> <mac_address>
# Actions: block - 禁用MAC地址网络访问
#         allow - 放行MAC地址网络访问

# 检查参数
if [ $# -ne 2 ]; then
    echo "Usage: $0 <block|allow> <mac_address>"
    echo "  block - 禁用MAC地址网络访问"
    echo "  allow - 放行MAC地址网络访问"
    exit 1
fi

ACTION="$1"
MAC="$2"

# 日志函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') [OneLink] $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [OneLink] $1" >> /tmp/onelink_auth.log
}

# 验证MAC地址格式
is_valid_mac() {
    local mac="$1"
    # 检查MAC地址格式 (xx:xx:xx:xx:xx:xx)
    echo "$mac" | grep -E '^([0-9a-fA-F]{2}:){5}[0-9a-fA-F]{2}$' > /dev/null
    return $?
}

# 获取网关地址
get_gateway_ip() {
    nv get lan_ipaddr 2>/dev/null || echo "*************"
}

# 检查iptables规则是否存在（不使用-C选项）
rule_exists() {
    local table="$1"
    local chain="$2"
    local rule="$3"

    if [ "$table" = "filter" ]; then
        iptables -t filter -L "$chain" -n 2>/dev/null | grep -q "$rule"
    elif [ "$table" = "nat" ]; then
        iptables -t nat -L "$chain" -n 2>/dev/null | grep -q "$rule"
    else
        return 1
    fi
}

# 禁用MAC地址网络访问
block_mac() {
    local mac="$1"
    local gateway_ip=$(get_gateway_ip)

    log_message "开始禁用MAC地址: $mac"

    # 首先清理该MAC的所有现有规则，确保只有一条记录
    cleanup_mac_rules "$mac"

    # 允许DNS解析（53端口）- 用于域名解析
    if ! rule_exists "filter" "FORWARD" "mac.*$mac.*udp.*dpt:53.*ACCEPT"; then
        iptables -I FORWARD -m mac --mac-source "$mac" -p udp --dport 53 -j ACCEPT
        log_message "添加DNS UDP允许规则: $mac"
    fi

    if ! rule_exists "filter" "FORWARD" "mac.*$mac.*tcp.*dpt:53.*ACCEPT"; then
        iptables -I FORWARD -m mac --mac-source "$mac" -p tcp --dport 53 -j ACCEPT
        log_message "添加DNS TCP允许规则: $mac"
    fi

    # 允许访问网关地址的所有流量（重要：确保能访问认证页面）
    if ! rule_exists "filter" "FORWARD" "mac.*$mac.*$gateway_ip.*ACCEPT"; then
        iptables -I FORWARD -m mac --mac-source "$mac" -d "$gateway_ip" -j ACCEPT
        log_message "添加网关访问允许规则: $mac -> $gateway_ip"
    fi

    # 允许访问本地web界面（HTTP 80端口到本机）
    if ! rule_exists "filter" "INPUT" "mac.*$mac.*tcp.*dpt:80.*ACCEPT"; then
        iptables -I INPUT -m mac --mac-source "$mac" -p tcp --dport 80 -j ACCEPT
        log_message "添加HTTP访问允许规则: $mac"
    fi

    # 允许访问本地web界面（HTTPS 443端口到本机）
    if ! rule_exists "filter" "INPUT" "mac.*$mac.*tcp.*dpt:443.*ACCEPT"; then
        iptables -I INPUT -m mac --mac-source "$mac" -p tcp --dport 443 -j ACCEPT
        log_message "添加HTTPS访问允许规则: $mac"
    fi

    # 阻止该MAC地址访问外网的所有流量（FORWARD链）- 确保只有一条DROP规则
    if ! rule_exists "filter" "FORWARD" "mac.*$mac.*DROP"; then
        iptables -A FORWARD -m mac --mac-source "$mac" -j DROP
        log_message "添加IPv4外网阻断规则: $mac"
    fi

    # 阻止该MAC地址IPv6访问外网的所有流量（FORWARD链）- 对应add_sta_mac中的IPv6阻断
    # 放行IPv6规则，因为add_sta_mac中禁用了IPv6，这里需要在放行时恢复
    if command -v ip6tables >/dev/null 2>&1; then
        if ! ip6tables -L FORWARD -n 2>/dev/null | grep -q "mac.*$mac.*DROP"; then
            ip6tables -I FORWARD -m mac --mac-source "$mac" -j DROP
            log_message "添加IPv6外网阻断规则: $mac"
        fi
    fi

    log_message "MAC地址 $mac 已被禁用网络访问"
}

# 清理MAC地址的所有iptables规则
cleanup_mac_rules() {
    local mac="$1"
    local gateway_ip=$(get_gateway_ip)
    local max_attempts=20
    local i

    log_message "开始清理MAC地址规则: $mac"

    # 删除IPv4 DNS UDP允许规则
    for i in $(seq 1 $max_attempts); do
        iptables -D FORWARD -m mac --mac-source "$mac" -p udp --dport 53 -j ACCEPT 2>/dev/null || break
    done

    # 删除IPv4 DNS TCP允许规则
    for i in $(seq 1 $max_attempts); do
        iptables -D FORWARD -m mac --mac-source "$mac" -p tcp --dport 53 -j ACCEPT 2>/dev/null || break
    done

    # 删除IPv4 网关访问允许规则
    for i in $(seq 1 $max_attempts); do
        iptables -D FORWARD -m mac --mac-source "$mac" -d "$gateway_ip" -j ACCEPT 2>/dev/null || break
    done

    # 删除IPv4 HTTP允许规则
    for i in $(seq 1 $max_attempts); do
        iptables -D INPUT -m mac --mac-source "$mac" -p tcp --dport 80 -j ACCEPT 2>/dev/null || break
    done

    # 删除IPv4 HTTPS允许规则
    for i in $(seq 1 $max_attempts); do
        iptables -D INPUT -m mac --mac-source "$mac" -p tcp --dport 443 -j ACCEPT 2>/dev/null || break
    done

    # 删除IPv4 DROP规则
    for i in $(seq 1 $max_attempts); do
        iptables -D FORWARD -m mac --mac-source "$mac" -j DROP 2>/dev/null || break
    done

    # 删除IPv6 DROP规则（对应add_sta_mac中的IPv6阻断）
    if command -v ip6tables >/dev/null 2>&1; then
        for i in $(seq 1 $max_attempts); do
            ip6tables -D FORWARD -m mac --mac-source "$mac" -j DROP 2>/dev/null || break
        done

        # 恢复IPv6正常访问（放行IPv6流量）
        for i in $(seq 1 $max_attempts); do
            ip6tables -D FORWARD -m mac --mac-source "$mac" -j ACCEPT 2>/dev/null || break
        done
    fi

    log_message "MAC地址 $mac 的所有iptables和ip6tables规则已清理完成"
}

# 放行MAC地址网络访问
allow_mac() {
    local mac="$1"

    log_message "开始放行MAC地址: $mac"

    # 清理该MAC的所有限制规则
    cleanup_mac_rules "$mac"

    # 恢复IPv6正常访问（如果之前被阻断）
    if command -v ip6tables >/dev/null 2>&1; then
        # 确保IPv6流量可以正常通过
        if ! ip6tables -L FORWARD -n 2>/dev/null | grep -q "mac.*$mac.*ACCEPT"; then
            ip6tables -I FORWARD -m mac --mac-source "$mac" -j ACCEPT 2>/dev/null
            log_message "恢复IPv6访问权限: $mac"
        fi
    fi

    log_message "MAC地址 $mac 已被放行，可正常访问网络"
}

# 主逻辑
main() {
    # 验证MAC地址格式
    if ! is_valid_mac "$MAC"; then
        log_message "错误: 无效的MAC地址格式: $MAC"
        exit 1
    fi

    case "$ACTION" in
        "block")
            block_mac "$MAC"
            ;;
        "allow")
            allow_mac "$MAC"
            ;;
        *)
            log_message "错误: 未知的操作: $ACTION"
            echo "Usage: $0 <block|allow> <mac_address>"
            exit 1
            ;;
    esac
}

# 执行主逻辑
main