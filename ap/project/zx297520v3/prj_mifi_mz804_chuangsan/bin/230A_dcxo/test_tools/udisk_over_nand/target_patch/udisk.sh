#!/bin/sh

ubiblock_create() {
    ubiattach  /dev/ubi_ctrl  -m  8
    if [ $? -ne 0 ]; then
        echo "[error] mtd8 ubiattach fail"
        return 1
    else
        echo "ubiattach mtd8 success"
    fi

    #ubirmvol /dev/ubi0 -n 0 > /dev/null 2>&1
    #ubirmvol /dev/ubi0 -n 1 > /dev/null 2>&1

    if [ ! -e /dev/ubi0_0 ]; then
        echo "ubi0_0 NOT exist and add a vol"
        ubimkvol  /dev/ubi0  -N  vol_udisk -t dynamic -s  52512768
        if [ $? -eq 0 ]; then
            echo "ubimkvol success"
        else
            echo "[error] ubimkvol fail"
            return 2
        fi
    fi

    ubiblock  -c /dev/ubi0_0
    if [ $? -eq 0 ]; then
        echo "ubiblock success and /dev/ubiblock0_0 is ready"
    else
        echo "[error] ubiblock fail"
        return 4
    fi
    return 0
}

ubiblock_create
if [ $? -ne 0 ]; then
    ubiformat /dev/mtd8 -y -s 512
    if [ $? -ne 0 ]; then
        echo "[error] ubiformat fail"
        exit 1
    fi
    ubiblock_create
    if [ $? -ne 0 ]; then
        echo "[error] try ubiblock_create again fail"
        exit 2
    fi
    mkfs.vfat -F 32  /dev/ubiblock0_0
    if [ $? -ne 0 ]; then
        echo "[error] mkfs.vfat /dev/ubiblock0_0 fail"
        exit 3
    else
        echo "mkfs.vfat /dev/ubiblock0_0 success"
    fi
fi

