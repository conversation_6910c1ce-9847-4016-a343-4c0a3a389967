# WiFiDog 和 QRZL 应用优化更改说明

## 概述
本文档描述了对 WiFiDog 网关和 QRZL 应用程序所做的三个主要优化更改，以提高网络检测准确性和白名单处理能力。

## 更改 1: iptables_fw_access 函数优化

### 文件位置
`ap/app/wifidog/wifidog-gateway-1.3.0/src/fw_iptables.c`

### 更改内容
- **原始宏**: `#ifdef QRZL_WIFIDOG_ONELINK_NV_SUPPORT`
- **新宏**: `#ifdef ENABLE_QRZL_APP`

### 功能说明
在 `iptables_fw_access` 函数的 `FW_ACCESS_ALLOW` 分支中，当允许客户端访问时，会将该客户端的 MAC 地址添加到 `CHAIN_TRUSTED` 链中，实现绕过认证的功能。

### 代码变更
```c
#ifdef ENABLE_QRZL_APP
    /* Add MAC address to trusted chain for bypass authentication */
    debug(LOG_DEBUG, "Adding MAC %s to trusted chain for IP %s", mac, ip);
    iptables_do_command("-t mangle -A " CHAIN_TRUSTED " -m mac --mac-source %s -j MARK --set-mark %d", mac, FW_MARK_KNOWN);
#endif
```

### 影响
- 使用统一的 `ENABLE_QRZL_APP` 宏控制 QRZL 相关功能
- 确保认证通过的设备能够正确添加到信任链中

## 更改 2: check_network 函数优化

### 文件位置
`ap/app/qrzl_app/qrzl_utils.c`

### 问题描述
原始的网络检测函数在大流量下载时会出现误判，因为 ping 检测可能因为网络繁忙而失败，但实际网络是可用的。

### 解决方案
优化网络检测逻辑，通过直接读取网络接口流量统计来判断网络可用性：

1. **流量检测优先**: 首先检查 `/sys/class/net/wan1/statistics/rx_bytes` 和 `/sys/class/net/wan1/statistics/tx_bytes`
2. **时间间隔检测**: 检查前后3秒的流量变化
3. **智能判断**: 如果有流量变化，说明网络可用，直接返回成功
4. **降级处理**: 只有在流量检测失败或无流量活动时，才进行 ping 检测

### 代码逻辑
```c
// 读取检查前的流量统计
if (read_interface_traffic("wan1", &rx_bytes_before, &tx_bytes_before) == 0) {
    sleep(3);  // 等待3秒
    
    // 读取检查后的流量统计
    if (read_interface_traffic("wan1", &rx_bytes_after, &tx_bytes_after) == 0) {
        // 检查是否有流量变化
        if (rx_bytes_after != rx_bytes_before || tx_bytes_after != tx_bytes_before) {
            // 有流量活动，网络可用
            return 0;
        }
    }
}
// 降级到 ping 检测
```

### 优势
- **准确性提高**: 大流量下载时不会误判网络不可用
- **响应速度**: 有流量活动时能快速返回结果
- **兼容性**: 保持原有 ping 检测作为备用方案

## 更改 3: http_callback_404 白名单处理优化

### 文件位置
`ap/app/wifidog/wifidog-gateway-1.3.0/src/http.c`

### 问题描述
1. `ONE_LINK_customers_get_token_url` 配置的是完整 URL（如 `http://boss.miyilink.com`），需要提取域名
2. `r->request.host` 可能包含端口号（如 `*************:2060`），需要正确处理

### 解决方案

#### 域名提取功能
从完整 URL 中提取域名：
```c
// 从 http://boss.miyilink.com/path -> boss.miyilink.com
char *domain_start = strstr(customers_auth_page_url, "://");
if (domain_start) {
    domain_start += 3; // 跳过 "://"
    char *domain_end = strchr(domain_start, '/');
    if (domain_end) {
        // 提取域名部分
        strncpy(customers_domain, domain_start, domain_end - domain_start);
    }
}
```

#### 端口号处理
去除请求主机名中的端口号：
```c
// *************:2060 -> *************
strncpy(request_host_no_port, r->request.host, sizeof(request_host_no_port) - 1);
colon_pos = strchr(request_host_no_port, ':');
if (colon_pos) {
    *colon_pos = '\0';
}
```

#### 智能白名单匹配
```c
if ((strlen(lan_ipaddr) > 0 && strstr(request_host_no_port, lan_ipaddr) != NULL) || 
    strstr(request_host_no_port, "*************") != NULL || 
    (strlen(one_link_origin_auth_page_url) > 0 && strstr(request_host_no_port, one_link_origin_auth_page_url) != NULL) ||
    (strlen(customers_domain) > 0 && strstr(request_host_no_port, customers_domain) != NULL)) {
    
    // 允许访问
    fw_allow_host(request_host_no_port);
    if (strcmp(r->request.host, request_host_no_port) != 0) {
        fw_allow_host(r->request.host);  // 也允许带端口的版本
    }
}
```

### 优势
- **灵活性**: 支持完整 URL 配置，自动提取域名
- **兼容性**: 正确处理带端口号的请求
- **准确性**: 避免端口号干扰白名单匹配
- **完整性**: 同时允许带端口和不带端口的主机名

## 编译和部署

### 编译要求
确保在编译时启用了 `ENABLE_QRZL_APP` 宏：
```makefile
CUSTOM_MACRO += -DENABLE_QRZL_APP
```

### 测试建议
1. **网络检测测试**: 在大流量下载时测试网络检测功能
2. **白名单测试**: 测试带端口号的请求和域名提取功能
3. **MAC 信任链测试**: 验证认证通过的设备能正确添加到信任链

## 注意事项
- 所有更改都向后兼容
- 保持了原有的错误处理和日志记录
- 优化了性能和准确性，但不改变核心功能逻辑
