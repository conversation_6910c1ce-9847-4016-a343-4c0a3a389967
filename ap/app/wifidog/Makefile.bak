
# WiFiDog Makefile - 优化版本
# 参考iptables的构建方式，使用构建系统变量

# 引入公共构建配置
include $(COMMON_MK)

# 源码目录
SRC_DIR = wifidog-gateway-1.3.0

# 添加编译优化选项
CFLAGS += -O1

# 设置PATH以包含交叉编译工具
PATH := $(PATH):$(CROSS_ROOT)/usr/bin

# 配置选项
CONFOPTS = --prefix=/usr --sysconfdir=/etc --localstatedir=/var
CONFOPTS += --host=arm-buildroot-linux-uclibcgnueabi
CONFOPTS += --build=x86_64-linux-gnu
CONFOPTS += --target=arm-buildroot-linux-uclibcgnueabi

# 设置交叉编译工具链 - 使用绝对路径确保找到正确的编译器
TOOLCHAIN_PATH = /home/<USER>/work/Code-u28/build/compiler/gcc-4.9.4_thumb_linux/usr/bin
CROSS_COMPILE = $(TOOLCHAIN_PATH)/arm-buildroot-linux-uclibcgnueabi-
WIFIDOG_CC = $(CROSS_COMPILE)gcc
WIFIDOG_CXX = $(CROSS_COMPILE)g++
WIFIDOG_AR = $(CROSS_COMPILE)ar
WIFIDOG_RANLIB = $(CROSS_COMPILE)ranlib
WIFIDOG_STRIP = $(CROSS_COMPILE)strip

# WiFiDog特定的宏定义
WIFIDOG_CFLAGS = $(CFLAGS)

# 检查是否在全局配置中已经定义了QRZL_WIFIDOG_ONELINK
# 如果全局已定义，则使用全局设置；否则根据QRZL_WIFIDOG_ONELINK_ENABLE控制
ifeq ($(findstring -DQRZL_WIFIDOG_ONELINK,$(CFLAGS)),)
# 全局未定义QRZL_WIFIDOG_ONELINK，使用本地条件编译控制
ifdef QRZL_WIFIDOG_ONELINK_ENABLE
WIFIDOG_CFLAGS += -DQRZL_WIFIDOG_ONELINK
WIFIDOG_CFLAGS += -DQRZL_ONE_LINK_CUSTOMER_MY
else
WIFIDOG_CFLAGS += -DQRZL_WIFIDOG_ONELINK_NO
WIFIDOG_CFLAGS += -DQRZL_ONE_LINK_CUSTOMER_MY_NO
endif
else
# 全局已定义QRZL_WIFIDOG_ONELINK，添加相关宏
WIFIDOG_CFLAGS += -DQRZL_ONE_LINK_CUSTOMER_MY
endif

# 构建目标
all: build/build
	$(MAKE) -C build am__configure_deps= am__aclocal_m4_deps=

# 配置构建目录
build/build: $(SRC_DIR)/configure
	@echo "Cleaning source directory to avoid configuration conflicts..."
	@cd $(SRC_DIR) && \
	if [ -f Makefile ]; then $(MAKE) distclean 2>/dev/null || true; fi && \
	rm -f config.status config.log Makefile config.h
	@mkdir -p build
	@echo "Configuring WiFiDog in build directory..."
	cd build && \
	../$(SRC_DIR)/configure $(CONFOPTS) \
		CC="$(WIFIDOG_CC)" \
		CXX="$(WIFIDOG_CXX)" \
		CPP="$(WIFIDOG_CC) -E" \
		CXXCPP="$(WIFIDOG_CXX) -E" \
		AR="$(WIFIDOG_AR)" \
		RANLIB="$(WIFIDOG_RANLIB)" \
		STRIP="$(WIFIDOG_STRIP)" \
		CFLAGS="$(WIFIDOG_CFLAGS) -idirafter $(STAGEDIR)/include -isystem $(STAGEDIR)/uClibc/usr/include -I$(zte_lib_path)/libnvram" \
		CPPFLAGS="-idirafter $(STAGEDIR)/include -isystem $(STAGEDIR)/uClibc/usr/include -I$(zte_lib_path)/libnvram" \
		CXXFLAGS="$(CFLAGS) -idirafter $(STAGEDIR)/include -isystem $(STAGEDIR)/uClibc/usr/include" \
		LDFLAGS="$(LDFLAGS) -L$(STAGEDIR)/lib -L$(STAGEDIR)/uClibc/lib -L$(zte_lib_path)/libnvram -lnvram" \
		ac_cv_func_malloc_0_nonnull=yes \
		ac_cv_func_realloc_0_nonnull=yes \
		cross_compiling=yes
	@touch $@

# Generate configure script if it doesn't exist
$(SRC_DIR)/configure: $(SRC_DIR)/configure.in
	cd $(SRC_DIR) && \
	unset CC CXX AR RANLIB STRIP CFLAGS CPPFLAGS LDFLAGS && \
	if [ -f autogen.sh ]; then \
		./autogen.sh; \
	else \
		autoreconf -fiv; \
	fi

# 清理构建文件
clean:
	-$(MAKE) -C build clean 2>/dev/null || true
	-rm -rf build

# 完全清理，包括配置文件
distclean: clean
	-rm -f $(SRC_DIR)/configure $(SRC_DIR)/Makefile.in $(SRC_DIR)/src/Makefile.in
	-rm -rf $(SRC_DIR)/autom4te.cache

# 安装到rootfs
romfs:
	if [ -f build/src/wifidog ]; then \
		cp build/src/wifidog build/src/wifidog.elf; \
		$(ROMFSINST) build/src/wifidog /bin/wifidog; \
	fi
	if [ -f build/src/wdctl ]; then \
		$(ROMFSINST) build/src/wdctl /bin/wdctl; \
	fi
	# Install configuration files
	if [ -f wifidog_onelink.conf ]; then \
		$(ROMFSINST) wifidog_onelink.conf /etc_ro/wifidog_onelink.conf; \
	fi
	if [ -f $(SRC_DIR)/wifidog-msg.html ]; then \
		$(ROMFSINST) $(SRC_DIR)/wifidog-msg.html /etc_ro/wifidog-msg.html; \
	fi

.PHONY: all clean distclean romfs
