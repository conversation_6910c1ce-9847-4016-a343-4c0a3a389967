# Carrier-Specific MAC Authentication Implementation

## Overview

This implementation adds carrier-specific authenticated MAC address support to the WiFiDog firewall initialization. The system automatically detects the current carrier based on the SIM card's IMSI and loads the appropriate authenticated MAC addresses into the iptables trusted chain.

## Implementation Details

### Files Modified
- `ap/app/wifidog/wifidog-gateway-1.3.0/src/fw_iptables.c`

### Key Features

1. **Carrier-Specific MAC Authentication** (QRZL_WIFIDOG_ONELINK)
2. **Dynamic MAC Trust Management** (ENABLE_QRZL_APP)

### Key Changes

1. **Added External Function Declarations**
   ```c
   #ifdef QRZL_WIFIDOG_ONELINK
   extern int cfg_get_item(const char *name, char *value, int len);
   extern int cfg_set(const char *name, const char *value);
   extern int get_isp_by_imsi(const char *imsi);
   #endif
   ```

2. **Added Helper Function Declaration**
   ```c
   static void iptables_add_carrier_authed_macs(void);
   ```

3. **Replaced Problematic Code**
   - Removed hardcoded variables `one_link_authed_mac` and `wifi_mac`
   - Replaced with call to `iptables_add_carrier_authed_macs()`

4. **Implemented Carrier Detection Logic**
   - Gets current SIM IMSI from NV storage
   - Uses `get_isp_by_imsi()` to determine carrier:
     - `1` = China Mobile (中国移动) → uses `one_link_authed_mac` NV
     - `2` = China Unicom (中国联通) → uses `uninet_authed_mac` NV  
     - `3` = China Telecom (中国电信) → uses `cmp_authed_mac` NV

5. **MAC Address Processing**
   - Parses semicolon-separated MAC address list
   - Validates MAC address format (xx:xx:xx:xx:xx:xx)
   - Adds each valid MAC to iptables trusted chain
   - Logs processing details for debugging

## NV Storage Format

The authenticated MAC addresses are stored in NV as semicolon-separated strings:
```
ba:a1:9d:2c:08:96;b2:4c:1e:53:72:fc;52:d1:d3:4f:09:94
```

## Carrier Mapping

| Carrier | ISP Code | NV Name | Description |
|---------|----------|---------|-------------|
| China Mobile | 1 | `one_link_authed_mac` | 移动认证MAC列表 |
| China Unicom | 2 | `uninet_authed_mac` | 联通认证MAC列表 |
| China Telecom | 3 | `cmp_authed_mac` | 电信认证MAC列表 |

## Function Flow

1. `iptables_fw_init()` calls `iptables_add_carrier_authed_macs()`
2. Get current SIM IMSI from `sim_imsi` NV
3. Determine carrier using `get_isp_by_imsi(imsi)`
4. Select appropriate NV name based on carrier
5. Load authenticated MAC list from selected NV
6. Parse and validate each MAC address
7. Add valid MACs to iptables trusted chain with `FW_MARK_KNOWN`

## Error Handling

- Gracefully handles missing IMSI
- Skips unknown carriers
- Validates MAC address format
- Logs warnings for invalid MACs
- Continues processing even if some MACs are invalid

## Compilation

The code is conditionally compiled with `#ifdef QRZL_WIFIDOG_ONELINK` to ensure it only builds when the OneLink feature is enabled.

## Testing

To test the implementation:
1. Set up different carrier SIM cards
2. Populate the appropriate NV with test MAC addresses
3. Initialize WiFiDog and check iptables rules
4. Verify authenticated MACs are added to trusted chain

## New Feature: Dynamic MAC Trust Management

### iptables_fw_access Enhancement

Added functionality to `iptables_fw_access()` function under `ENABLE_QRZL_APP` macro:

**FW_ACCESS_ALLOW**: Adds MAC to trusted chain
```c
#ifdef ENABLE_QRZL_APP
/* Add MAC address to trusted chain for bypass authentication */
debug(LOG_DEBUG, "Adding MAC %s to trusted chain for IP %s", mac, ip);
iptables_do_command("-t mangle -A " CHAIN_TRUSTED " -m mac --mac-source %s -j MARK --set-mark %d", mac, FW_MARK_KNOWN);
#endif
```

**FW_ACCESS_DENY**: Removes MAC from trusted chain
```c
#ifdef ENABLE_QRZL_APP
/* Remove MAC address from trusted chain */
debug(LOG_DEBUG, "Removing MAC %s from trusted chain for IP %s", mac, ip);
iptables_do_command("-t mangle -D " CHAIN_TRUSTED " -m mac --mac-source %s -j MARK --set-mark %d", mac, FW_MARK_KNOWN);
#endif
```

### Benefits
- **Dynamic Management**: MACs are added/removed from trusted chain in real-time
- **Automatic Cleanup**: When clients disconnect, their MACs are removed from trusted chain
- **Authentication Bypass**: Trusted MACs bypass WiFiDog authentication
- **Conditional Compilation**: Only active when `ENABLE_QRZL_APP` is defined

## Example iptables Output

After successful initialization, you should see rules like:
```bash
iptables -t mangle -L WiFiDog_br0_Trusted -v
# Should show rules for each authenticated MAC:
# -A WiFiDog_br0_Trusted -m mac --mac-source ba:a1:9d:2c:08:96 -j MARK --set-mark 0x2
# -A WiFiDog_br0_Trusted -m mac --mac-source aa:bb:cc:dd:ee:ff -j MARK --set-mark 0x2 (from fw_access)
```
