#!/bin/bash

# Test script for carrier-specific MAC authentication
# This script demonstrates how the carrier detection and MAC loading works

echo "=== Carrier-Specific MAC Authentication Test ==="

# Function to simulate cfg_get_item
cfg_get_item() {
    local key="$1"
    local value_var="$2"
    
    case "$key" in
        "sim_imsi")
            # Simulate different carrier IMSI values
            echo "46000123456789"  # China Mobile IMSI
            ;;
        "one_link_authed_mac")
            # China Mobile authenticated MACs
            echo "ba:a1:9d:2c:08:96;b2:4c:1e:53:72:fc;52:d1:d3:4f:09:94"
            ;;
        "cmp_authed_mac")
            # China Telecom authenticated MACs  
            echo "aa:bb:cc:dd:ee:ff;11:22:33:44:55:66"
            ;;
        "uninet_authed_mac")
            # China Unicom authenticated MACs
            echo "12:34:56:78:9a:bc;de:f0:12:34:56:78"
            ;;
        *)
            echo ""
            return 1
            ;;
    esac
    return 0
}

# Function to simulate get_isp_by_imsi
get_isp_by_imsi() {
    local imsi="$1"
    
    # Check IMSI prefix to determine carrier
    if [[ "$imsi" =~ ^46000|^46002|^46004|^46007|^46008|^46013|^46024 ]]; then
        echo "1"  # China Mobile
    elif [[ "$imsi" =~ ^46001|^46006|^46009|^46010 ]]; then
        echo "2"  # China Unicom
    elif [[ "$imsi" =~ ^46003|^46005|^46011|^46012 ]]; then
        echo "3"  # China Telecom
    else
        echo "0"  # Unknown
    fi
}

# Function to simulate iptables command
iptables_do_command() {
    echo "IPTABLES: $*"
}

# Main test function
test_carrier_mac_auth() {
    echo "1. Getting SIM IMSI..."
    local sim_imsi=$(cfg_get_item "sim_imsi")
    echo "   IMSI: $sim_imsi"
    
    echo "2. Determining carrier..."
    local isp=$(get_isp_by_imsi "$sim_imsi")
    echo "   ISP Code: $isp"
    
    local nv_name=""
    local carrier_name=""
    
    case "$isp" in
        1)
            nv_name="one_link_authed_mac"
            carrier_name="China Mobile"
            ;;
        2)
            nv_name="uninet_authed_mac"
            carrier_name="China Unicom"
            ;;
        3)
            nv_name="cmp_authed_mac"
            carrier_name="China Telecom"
            ;;
        *)
            echo "   Unknown carrier, skipping"
            return
            ;;
    esac
    
    echo "   Carrier: $carrier_name"
    echo "   NV Name: $nv_name"
    
    echo "3. Loading authenticated MAC list..."
    local authed_mac_list=$(cfg_get_item "$nv_name")
    echo "   MAC List: $authed_mac_list"
    
    if [ -z "$authed_mac_list" ]; then
        echo "   No authenticated MACs found"
        return
    fi
    
    echo "4. Processing MAC addresses..."
    local mac_count=0
    
    # Split MAC list by semicolon
    IFS=';' read -ra MACS <<< "$authed_mac_list"
    for mac in "${MACS[@]}"; do
        # Trim whitespace
        mac=$(echo "$mac" | xargs)
        
        # Skip empty MACs
        if [ -z "$mac" ]; then
            continue
        fi
        
        # Basic MAC format validation
        if [[ "$mac" =~ ^([0-9a-fA-F]{2}:){5}[0-9a-fA-F]{2}$ ]]; then
            echo "   Adding MAC: $mac"
            iptables_do_command "-t mangle -A WiFiDog_br0_Trusted -m mac --mac-source $mac -j MARK --set-mark 2"
            ((mac_count++))
        else
            echo "   Invalid MAC format: $mac"
        fi
    done
    
    echo "5. Summary:"
    echo "   Carrier: $carrier_name"
    echo "   MACs processed: $mac_count"
    echo "   NV source: $nv_name"
}

# Test different carriers
echo -e "\n--- Testing China Mobile ---"
test_carrier_mac_auth

echo -e "\n--- Testing China Telecom ---"
# Override IMSI for China Telecom
cfg_get_item() {
    local key="$1"
    case "$key" in
        "sim_imsi")
            echo "46003123456789"  # China Telecom IMSI
            ;;
        *)
            # Call original function for other keys
            case "$key" in
                "one_link_authed_mac")
                    echo "ba:a1:9d:2c:08:96;b2:4c:1e:53:72:fc;52:d1:d3:4f:09:94"
                    ;;
                "cmp_authed_mac")
                    echo "aa:bb:cc:dd:ee:ff;11:22:33:44:55:66"
                    ;;
                "uninet_authed_mac")
                    echo "12:34:56:78:9a:bc;de:f0:12:34:56:78"
                    ;;
                *)
                    echo ""
                    return 1
                    ;;
            esac
            ;;
    esac
    return 0
}
test_carrier_mac_auth

echo -e "\n=== Test Complete ==="
