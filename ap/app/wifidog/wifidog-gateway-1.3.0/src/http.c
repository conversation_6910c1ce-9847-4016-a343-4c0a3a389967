/* vim: set et sw=4 ts=4 sts=4 : */
/********************************************************************\
 * This program is free software; you can redistribute it and/or    *
 * modify it under the terms of the GNU General Public License as   *
 * published by the Free Software Foundation; either version 2 of   *
 * the License, or (at your option) any later version.              *
 *                                                                  *
 * This program is distributed in the hope that it will be useful,  *
 * but WITHOUT ANY WARRANTY; without even the implied warranty of   *
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the    *
 * GNU General Public License for more details.                     *
 *                                                                  *
 * You should have received a copy of the GNU General Public License*
 * along with this program; if not, contact:                        *
 *                                                                  *
 * Free Software Foundation           Voice:  +1-617-542-5942       *
 * 59 Temple Place - Suite 330        Fax:    +1-617-542-2652       *
 * Boston, MA  02111-1307,  USA       <EMAIL>                   *
 *                                                                  *
 \********************************************************************/

/* $Id$ */
/** @file http.c
  @brief HTTP IO functions
  <AUTHOR> (C) 2004 Philippe April <<EMAIL>>
  <AUTHOR> (C) 2007 Benoit Grégoire
  <AUTHOR> (C) 2007 David Bird <<EMAIL>>

 */
/* Note that libcs other than GLIBC also use this macro to enable vasprintf */
#define _GNU_SOURCE

#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <syslog.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <errno.h>
#include <ctype.h>
#include <time.h>

#include "httpd.h"

#include "safe.h"
#include "debug.h"
#include "conf.h"
#include "auth.h"
#include "firewall.h"
#include "http.h"
#include "client_list.h"
#include "common.h"
#include "centralserver.h"
#include "util.h"
#include "wd_util.h"

#include "../config.h"

#ifdef QRZL_WIFIDOG_ONELINK
/* External configuration functions */
extern int cfg_get_item(const char *name, char *value, int len);
extern int cfg_set(const char *name, const char *value);
#endif


/** The 404 handler is also responsible for redirecting to the auth server */
void
http_callback_404(httpd * webserver, request * r, int error_code)
{
    char tmp_url[MAX_BUF], *url, *mac;
    s_config *config = config_get_config();
    t_auth_serv *auth_server = get_auth_server();

    memset(tmp_url, 0, sizeof(tmp_url));
    /* 
     * XXX Note the code below assumes that the client's request is a plain
     * http request to a standard port. At any rate, this handler is called only
     * if the internet/auth server is down so it's not a huge loss, but still.
     */
    snprintf(tmp_url, (sizeof(tmp_url) - 1), "http://%s%s%s%s",
             r->request.host, r->request.path, r->request.query[0] ? "?" : "", r->request.query);
    url = httpdUrlEncode(tmp_url);

#ifdef QRZL_WIFIDOG_ONELINK
//#if 0
    {
        /* Re-direct them to auth server */
        char *urlFragment;

        if (!(mac = arp_get(r->clientAddr))) {
            /* We could not get their MAC address */
            debug(LOG_INFO, "Failed to retrieve MAC address for ip %s, so not putting in the login request",
                  r->clientAddr);
            safe_asprintf(&urlFragment, "%sgw_address=%s&gw_port=%d&gw_id=%s&ip=%s&url=%s",
                          auth_server->authserv_login_script_path_fragment, config->gw_address, config->gw_port,
                          config->gw_id, r->clientAddr, url);
        } else {
            debug(LOG_INFO, "Got client MAC address for ip %s: %s", r->clientAddr, mac);
            safe_asprintf(&urlFragment, "%sgw_address=%s&gw_port=%d&gw_id=%s&ip=%s&mac=%s&url=%s",
                          auth_server->authserv_login_script_path_fragment,
                          config->gw_address, config->gw_port, config->gw_id, r->clientAddr, mac, url);
            free(mac);
        }

        // if host is not in whitelist, maybe not in conf or domain'IP changed, it will go to here.
        debug(LOG_INFO, "Check v2 host %s is in whitelist or not", r->request.host);       // e.g. www.example.com
        t_firewall_rule *rule;
        //e.g. example.com is in whitelist
        // if request http://www.example.com/, it's not equal example.com.
        for (rule = get_ruleset("global"); rule != NULL; rule = rule->next) {
            debug(LOG_INFO, "rule mask %s", rule->mask);
            if (strstr(r->request.host, rule->mask) == NULL) {
                debug(LOG_INFO, "host %s is not in %s, continue", r->request.host, rule->mask);
                continue;
            }
            int host_length = strlen(r->request.host);
            int mask_length = strlen(rule->mask);
            if (host_length != mask_length) {
                char prefix[1024] = { 0 };
                // must be *.example.com, if not have ".", maybe Phishing. e.g. phishingexample.com
                strncpy(prefix, r->request.host, host_length - mask_length - 1);        // e.g. www
                strcat(prefix, ".");    // www.
                strcat(prefix, rule->mask);     // www.example.com
                if (strcasecmp(r->request.host, prefix) == 0) {
                    debug(LOG_INFO, "allow subdomain");
                    fw_allow_host(r->request.host);
                    http_send_redirect(r, tmp_url, "allow subdomain");
                    free(url);
                    free(urlFragment);
                    return;
                }
            } else {
                // e.g. "example.com" is in conf, so it had been parse to IP and added into "iptables allow" when wifidog start. but then its' A record(IP) changed, it will go to here.
                debug(LOG_INFO, "allow domain again, because IP changed");
                fw_allow_host(r->request.host);
                http_send_redirect(r, tmp_url, "allow domain");
                free(url);
                free(urlFragment);
                return;
            }
        }

        // if (strstr(r->request.host, "*************") != NULL) {
        //     debug(LOG_INFO, "allow subdomain");
        //     fw_allow_host(r->request.host);
        //     http_send_redirect(r, tmp_url, "allow subdomain");
        //     free(url);
        //     free(urlFragment);
        //     return;
        // }
        

        debug(LOG_INFO, "Captured %s requesting [%s] and re-directing them to login page", r->clientAddr, url);
        http_send_redirect_to_auth(r, urlFragment, "Redirect to login page");
        free(urlFragment);
    }
#else
    if (!is_online()) {
        /* The internet connection is down at the moment  - apologize and do not redirect anywhere */
        char *buf;
        safe_asprintf(&buf,
                      "<p>We apologize, but it seems that the internet connection that powers this hotspot is temporarily unavailable.</p>"
                      "<p>If at all possible, please notify the owners of this hotspot that the internet connection is out of service.</p>"
                      "<p>The maintainers of this network are aware of this disruption.  We hope that this situation will be resolved soon.</p>"
                      "<p>In a while please <a href='%s'>click here</a> to try your request again.</p>", tmp_url);

        send_http_page(r, "Uh oh! Internet access unavailable!", buf);
        free(buf);
        debug(LOG_INFO, "Sent %s an apology since I am not online - no point sending them to auth server",
              r->clientAddr);
    } else if (!is_auth_online()) {
        /* The auth server is down at the moment - apologize and do not redirect anywhere */
        char *buf;
        safe_asprintf(&buf,
                      "<p>We apologize, but it seems that we are currently unable to re-direct you to the login screen.</p>"
                      "<p>The maintainers of this network are aware of this disruption.  We hope that this situation will be resolved soon.</p>"
                      "<p>In a couple of minutes please <a href='%s'>click here</a> to try your request again.</p>",
                      tmp_url);

        send_http_page(r, "Uh oh! Login screen unavailable!", buf);
        free(buf);
        debug(LOG_INFO, "Sent %s an apology since auth server not online - no point sending them to auth server",
              r->clientAddr);
    } else {
        /* Re-direct them to auth server */
        char *urlFragment;

        if (!(mac = arp_get(r->clientAddr))) {
            /* We could not get their MAC address */
            debug(LOG_INFO, "Failed to retrieve MAC address for ip %s, so not putting in the login request",
                  r->clientAddr);
            safe_asprintf(&urlFragment, "%sgw_address=%s&gw_port=%d&gw_id=%s&ip=%s&url=%s",
                          auth_server->authserv_login_script_path_fragment, config->gw_address, config->gw_port,
                          config->gw_id, r->clientAddr, url);
        } else {
            debug(LOG_INFO, "Got client MAC address for ip %s: %s", r->clientAddr, mac);
            safe_asprintf(&urlFragment, "%sgw_address=%s&gw_port=%d&gw_id=%s&ip=%s&mac=%s&url=%s",
                          auth_server->authserv_login_script_path_fragment,
                          config->gw_address, config->gw_port, config->gw_id, r->clientAddr, mac, url);
            free(mac);
        }

        // if host is not in whitelist, maybe not in conf or domain'IP changed, it will go to here.
        debug(LOG_INFO, "Check v3 host %s is in whitelist or not", r->request.host);       // e.g. www.example.com
        t_firewall_rule *rule;
        //e.g. example.com is in whitelist
        // if request http://www.example.com/, it's not equal example.com.
        for (rule = get_ruleset("global"); rule != NULL; rule = rule->next) {
            debug(LOG_INFO, "rule mask %s", rule->mask);
            if (strstr(r->request.host, rule->mask) == NULL) {
                debug(LOG_INFO, "host %s is not in %s, continue", r->request.host, rule->mask);
                continue;
            }
            int host_length = strlen(r->request.host);
            int mask_length = strlen(rule->mask);
            if (host_length != mask_length) {
                char prefix[1024] = { 0 };
                // must be *.example.com, if not have ".", maybe Phishing. e.g. phishingexample.com
                strncpy(prefix, r->request.host, host_length - mask_length - 1);        // e.g. www
                strcat(prefix, ".");    // www.
                strcat(prefix, rule->mask);     // www.example.com
                if (strcasecmp(r->request.host, prefix) == 0) {
                    debug(LOG_INFO, "allow subdomain");
                    fw_allow_host(r->request.host);
                    http_send_redirect(r, tmp_url, "allow subdomain");
                    free(url);
                    free(urlFragment);
                    return;
                }
            } else {
                // e.g. "example.com" is in conf, so it had been parse to IP and added into "iptables allow" when wifidog start. but then its' A record(IP) changed, it will go to here.
                debug(LOG_INFO, "allow domain again, because IP changed");
                fw_allow_host(r->request.host);
                http_send_redirect(r, tmp_url, "allow domain");
                free(url);
                free(urlFragment);
                return;
            }
        }

    #ifdef QRZL_WIFIDOG_ONELINK_NV_SUPPORT
        // check if url is allowed
        char lan_ipaddr[64] = {0};
        char one_link_origin_auth_page_url[512] = {0};
        char customers_auth_page_url[512] = {0};
        char customers_domain[256] = {0};
        char *colon_pos = NULL;
        char request_host_no_port[256] = {0};

        if (cfg_get_item("lan_ipaddr", lan_ipaddr, sizeof(lan_ipaddr)) == 0) {
            debug(LOG_DEBUG, "cfg_get_item lan_ipaddr %s as the host whitelist ", lan_ipaddr);
        }

        if (cfg_get_item("ONE_LINK_auth_http_url", one_link_origin_auth_page_url, sizeof(one_link_origin_auth_page_url)) == 0) {
            debug(LOG_DEBUG, "cfg_get_item ONE_LINK_auth_http_url %s as the host whitelist ", one_link_origin_auth_page_url);
        }

        if (cfg_get_item("ONE_LINK_customers_get_token_url", customers_auth_page_url, sizeof(customers_auth_page_url)) == 0) {
            debug(LOG_DEBUG, "cfg_get_item ONE_LINK_customers_get_token_url %s as the host whitelist ", customers_auth_page_url);

            // 从URL中提取域名 (例如: http://boss.miyilink.com -> boss.miyilink.com)
            char *domain_start = strstr(customers_auth_page_url, "://");
            if (domain_start) {
                domain_start += 3; // 跳过 "://"
                char *domain_end = strchr(domain_start, '/');
                if (domain_end) {
                    int domain_len = domain_end - domain_start;
                    if (domain_len > 0 && domain_len < sizeof(customers_domain) - 1) {
                        strncpy(customers_domain, domain_start, domain_len);
                        customers_domain[domain_len] = '\0';
                        debug(LOG_DEBUG, "Extracted domain from customers_get_token_url: %s", customers_domain);
                    }
                } else {
                    // 没有路径部分，整个就是域名
                    strncpy(customers_domain, domain_start, sizeof(customers_domain) - 1);
                    customers_domain[sizeof(customers_domain) - 1] = '\0';
                    debug(LOG_DEBUG, "Extracted domain (no path) from customers_get_token_url: %s", customers_domain);
                }
            }
        }

        // 处理请求主机名，去除端口号 (例如: *************:2060 -> *************)
        strncpy(request_host_no_port, r->request.host, sizeof(request_host_no_port) - 1);
        request_host_no_port[sizeof(request_host_no_port) - 1] = '\0';
        colon_pos = strchr(request_host_no_port, ':');
        if (colon_pos) {
            *colon_pos = '\0';
            debug(LOG_DEBUG, "Removed port from request host: %s -> %s", r->request.host, request_host_no_port);
        }

        // 检查是否匹配白名单
        if ((strlen(lan_ipaddr) > 0 && strstr(request_host_no_port, lan_ipaddr) != NULL) ||
            strstr(request_host_no_port, "*************") != NULL ||
            (strlen(one_link_origin_auth_page_url) > 0 && strstr(request_host_no_port, one_link_origin_auth_page_url) != NULL) ||
            (strlen(customers_domain) > 0 && strstr(request_host_no_port, customers_domain) != NULL)) {

            debug(LOG_INFO, "allow this url %s (host: %s, no_port: %s)", tmp_url, r->request.host, request_host_no_port);

            // 允许原始主机名和去除端口的主机名
            fw_allow_host(request_host_no_port);
            if (strcmp(r->request.host, request_host_no_port) != 0) {
                fw_allow_host(r->request.host);
            }

            http_send_redirect(r, tmp_url, "allow domain");
            free(url);
            free(urlFragment);
            return;
        }
    #endif

        debug(LOG_INFO, "Captured %s requesting [%s] and re-directing them to login page", r->clientAddr, url);
        http_send_redirect_to_auth(r, urlFragment, "Redirect to login page");
        free(urlFragment);
    }
#endif
    free(url);
}

void
http_callback_wifidog(httpd * webserver, request * r)
{
    send_http_page(r, "WiFiDog", "Please use the menu to navigate the features of this WiFiDog installation.");
}

void
http_callback_about(httpd * webserver, request * r)
{
    send_http_page(r, "About WiFiDog", "This is WiFiDog version <strong>" VERSION "</strong>");
}

void
http_callback_status(httpd * webserver, request * r)
{
    const s_config *config = config_get_config();
    char *status = NULL;
    char *buf;

    if (config->httpdusername &&
        (strcmp(config->httpdusername, r->request.authUser) ||
         strcmp(config->httpdpassword, r->request.authPassword))) {
        debug(LOG_INFO, "Status page requested, forcing authentication");
        httpdForceAuthenticate(r, config->httpdrealm);
        return;
    }

    status = get_status_text();
    safe_asprintf(&buf, "<pre>%s</pre>", status);
    send_http_page(r, "WiFiDog Status", buf);
    free(buf);
    free(status);
}

#ifdef QRZL_WIFIDOG_ONELINK
/*****************************************************************************************************
 * @brief 将任意格式的MAC地址（可能含有':'或'-'或不含）标准化为指定格式
 *
 * @param input         输入MAC地址字符串（如 "3C:67:FD:74:D2:FB", "3c-67-fd-74-d2-fb", "3c67fd74d2fb"）
 * @param output        输出结果缓冲区
 * @param out_size      输出缓冲区大小，至少应为 18
 * @param format_char   输出格式：':' 表示冒号分隔，'-' 表示短横线分隔，'\0' 表示无分隔
 *
 * @return int 0 表示成功，-1 表示失败（如非法输入）
 *****************************************************************************************************/
int convert_mac_format(const char *input, char *output, size_t out_size, char format_char) {
    char temp[13] = {0}; // 临时存储12个16进制字符 + 结尾0
    int len = 0;

    if (!input || !output || out_size < 13 || (format_char && out_size < 18)) {
        return -1;
    }

    // 1. 提取16进制字符（忽略':' 或 '-'）
    int i;
    for ( i = 0; input[i] && len < 12; ++i) {
        if (isxdigit((unsigned char)input[i])) {
            temp[len++] = tolower((unsigned char)input[i]);
        }
    }

    if (len != 12) {
        return -1; // 不是合法MAC地址
    }

    // 2. 格式化输出
    if (format_char == '\0') {
        // 不带分隔符
        snprintf(output, out_size, "%s", temp);
    } else {
        // 带分隔符，格式 xx:xx:xx:xx:xx:xx
        // 17字节+1结尾0，确保缓冲区至少18
        snprintf(output, out_size,
            "%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c",
            temp[0], temp[1], format_char,
            temp[2], temp[3], format_char,
            temp[4], temp[5], format_char,
            temp[6], temp[7], format_char,
            temp[8], temp[9], format_char,
            temp[10], temp[11]);
    }

    return 0;
}
#endif

/** @brief Convenience function to redirect the web browser to the auth server
 * @param r The request
 * @param urlFragment The end of the auth server URL to redirect to (the part after path)
 * @param text The text to include in the redirect header ant the mnual redirect title */
void
http_send_redirect_to_auth(request * r, const char *urlFragment, const char *text)
{
    char *protocol = NULL;
    int port = 80;
    t_auth_serv *auth_server = get_auth_server();

    if (auth_server->authserv_use_ssl) {
        protocol = "https";
        port = auth_server->authserv_ssl_port;
    } else {
        protocol = "http";
        port = auth_server->authserv_http_port;
    }

    char *url = NULL;
#if defined(QRZL_WIFIDOG_ONELINK) && defined(QRZL_ONE_LINK_CUSTOMER_MY)
    // Check if we have a local redirect URL configured to avoid external redirects
    char local_redirect_url[512] = {0};
    cfg_get_item("QRZL_local_redirect_url", local_redirect_url, sizeof(local_redirect_url));

    if (strlen(local_redirect_url) > 0) {
        // Use local redirect URL to avoid external server dependency
        debug(LOG_INFO, "Using local redirect URL: %s", local_redirect_url);
        safe_asprintf(&url, "%s", local_redirect_url);
    } else {
        // Fallback to OneLink external authentication server
        char one_link_customers_get_token_url[512] = {0};
        cfg_get_item("ONE_LINK_customers_get_token_url", one_link_customers_get_token_url, sizeof(one_link_customers_get_token_url));

        // Only proceed with external redirect if URL is configured
        if (strlen(one_link_customers_get_token_url) == 0) {
            debug(LOG_WARNING, "No OneLink URL configured, using default auth server");
            safe_asprintf(&url, "%s://%s:%d%s%s",
                          protocol, auth_server->authserv_hostname, port, auth_server->authserv_path, urlFragment);
        } else {
            // 获取设备WiFi MAC地址
            char wifi_mac[18] = {0};
            cfg_get_item("wifi_mac", wifi_mac, sizeof(wifi_mac));

            // 获取客户端MAC地址
            char *terminal_mac = arp_get(r->clientAddr);
            if (!terminal_mac) {
                terminal_mac = "00:00:00:00:00:00"; // 默认值
            }

            // MAC - 转换wifi_mac为无分隔符格式
            char convered_mac[33] = {0};
            convert_mac_format(wifi_mac, convered_mac, sizeof(convered_mac), '\0');

            // terminalMac - 转换terminal_mac为无分隔符格式
            char convered_terminalMac[33] = {0};
            convert_mac_format(terminal_mac, convered_terminalMac, sizeof(convered_terminalMac), '\0');

            // iccid
            char current_iccid[22] = {0};
            cfg_get_item("ziccid", current_iccid, sizeof(current_iccid));

            char sn[22] = {0};
            cfg_get_item("sn", sn, sizeof(sn));

            char sim_select[22] = {0};
            cfg_get_item("sim_select", sim_select, sizeof(sim_select));

            // sequence
            char sequence[2] = {0};
            if(strcmp(sim_select, "RSIM_only") == 0) {
                snprintf(sequence, sizeof(sequence), "%s", "3");
            } else if(strcmp(sim_select, "ESIM1_only") == 0) {
                snprintf(sequence, sizeof(sequence), "%s", "1");
            } else if(strcmp(sim_select, "ESIM2_only") == 0) {
                snprintf(sequence, sizeof(sequence), "%s", "2");
            } else {
                snprintf(sequence, sizeof(sequence), "%s", "0");
            }

            char lan_ip[30] = {0};
            cfg_get_item("lan_ipaddr", lan_ip, sizeof(lan_ip));

            safe_asprintf(&url, "%s/boss-web/wx-login.html?mac=%s&terminalMac=%s&phoneNum=%s&iccid=%s&sn=%s&sequence=%s&deviceIp=http://%s:2060/Api/codeToken",
                        one_link_customers_get_token_url, convered_mac, convered_terminalMac, "", current_iccid, sn, sequence , lan_ip);

            // Free terminal_mac if it was allocated by arp_get
            if (terminal_mac && strcmp(terminal_mac, "00:00:00:00:00:00") != 0) {
                free(terminal_mac);
            }
        }
    }
#else
    safe_asprintf(&url, "%s://%s:%d%s%s",
                  protocol, auth_server->authserv_hostname, port, auth_server->authserv_path, urlFragment);
#endif
    http_send_redirect(r, url, text);
    free(url);
}

/** @brief Sends a redirect to the web browser 
 * @param r The request
 * @param url The url to redirect to
 * @param text The text to include in the redirect header and the manual redirect link title.  NULL is acceptable */
void
http_send_redirect(request * r, const char *url, const char *text)
{
    char *message = NULL;
    char *header = NULL;
    char *response = NULL;
    /* Re-direct them to auth server */
    debug(LOG_DEBUG, "Redirecting client browser to %s", url);
    safe_asprintf(&header, "Location: %s", url);
    safe_asprintf(&response, "302 %s\n", text ? text : "Redirecting");
    httpdSetResponse(r, response);
    httpdAddHeader(r, header);
    free(response);
    free(header);
    safe_asprintf(&message, "Please <a href='%s'>click here</a>.", url);
    send_http_page(r, text ? text : "Redirection to message", message);
    free(message);
}

void
http_callback_auth(httpd * webserver, request * r)
{
    t_client *client;
    httpVar *token;
    char *mac;
    httpVar *logout = httpdGetVariableByName(r, "logout");

    if ((token = httpdGetVariableByName(r, "token"))) {
        /* They supplied variable "token" */
        if (!(mac = arp_get(r->clientAddr))) {
            /* We could not get their MAC address */
            debug(LOG_ERR, "Failed to retrieve MAC address for ip %s", r->clientAddr);
            send_http_page(r, "WiFiDog Error", "Failed to retrieve your MAC address");
        } else {
            /* We have their MAC address */
            LOCK_CLIENT_LIST();

            if ((client = client_list_find(r->clientAddr, mac)) == NULL) {
                debug(LOG_DEBUG, "New client for %s", r->clientAddr);
                client_list_add(r->clientAddr, mac, token->value);
            } else if (logout) {
                logout_client(client);
            } else {
                debug(LOG_DEBUG, "Client for %s is already in the client list", client->ip);
            }

            UNLOCK_CLIENT_LIST();
            if (!logout) { /* applies for case 1 and 3 from above if */
                authenticate_client(r);
            }
            free(mac);
        }
    } else {
        /* They did not supply variable "token" */
        send_http_page(r, "WiFiDog error", "Invalid token");
    }
}

void
http_callback_disconnect(httpd * webserver, request * r)
{
    const s_config *config = config_get_config();
    /* XXX How do you change the status code for the response?? */
    httpVar *token = httpdGetVariableByName(r, "token");
    httpVar *mac = httpdGetVariableByName(r, "mac");

    if (config->httpdusername &&
        (strcmp(config->httpdusername, r->request.authUser) ||
         strcmp(config->httpdpassword, r->request.authPassword))) {
        debug(LOG_INFO, "Disconnect requested, forcing authentication");
        httpdForceAuthenticate(r, config->httpdrealm);
        return;
    }

    if (token && mac) {
        t_client *client;

        LOCK_CLIENT_LIST();
        client = client_list_find_by_mac(mac->value);

        if (!client || strcmp(client->token, token->value)) {
            UNLOCK_CLIENT_LIST();
            debug(LOG_INFO, "Disconnect %s with incorrect token %s", mac->value, token->value);
            httpdOutput(r, "Invalid token for MAC");
            return;
        }

        /* TODO: get current firewall counters */
        logout_client(client);
        UNLOCK_CLIENT_LIST();

    } else {
        debug(LOG_INFO, "Disconnect called without both token and MAC given");
        httpdOutput(r, "Both the token and MAC need to be specified");
        return;
    }

    return;
}

void
send_http_page(request * r, const char *title, const char *message)
{
    s_config *config = config_get_config();
    char *buffer;
    struct stat stat_info;
    int fd;
    ssize_t written;

    fd = open(config->htmlmsgfile, O_RDONLY);
    if (fd == -1) {
        debug(LOG_CRIT, "Failed to open HTML message file %s: %s", config->htmlmsgfile, strerror(errno));
        return;
    }

    if (fstat(fd, &stat_info) == -1) {
        debug(LOG_CRIT, "Failed to stat HTML message file: %s", strerror(errno));
        close(fd);
        return;
    }
    // Cast from long to unsigned int
    buffer = (char *)safe_malloc((size_t) stat_info.st_size + 1);
    written = read(fd, buffer, (size_t) stat_info.st_size);
    if (written == -1) {
        debug(LOG_CRIT, "Failed to read HTML message file: %s", strerror(errno));
        free(buffer);
        close(fd);
        return;
    }
    close(fd);

    buffer[written] = 0;
    httpdAddVariable(r, "title", title);
    httpdAddVariable(r, "message", message);
    httpdAddVariable(r, "nodeID", config->gw_id);
    httpdOutput(r, buffer);
    free(buffer);
}

#ifdef QRZL_WIFIDOG_ONELINK
void
http_callback_auth_success(httpd * webserver, request * r)
{
    debug(LOG_INFO, "Auth success page request from %s", r->clientAddr);

    // Integrated auth_success.html content
    const char *success_html =
        "<!DOCTYPE html>\n"
        "<html lang=\"zh-CN\">\n"
        "<head>\n"
        "  <meta charset=\"UTF-8\">\n"
        "  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n"
        "  <title>认证成功</title>\n"
        "  <style>\n"
        "    body {\n"
        "      margin: 0;\n"
        "      padding: 0;\n"
        "      font-family: \"Segoe UI\", Tahoma, Geneva, Verdana, sans-serif;\n"
        "      background: linear-gradient(to right, #e0f7fa, #ffffff);\n"
        "      display: flex;\n"
        "      justify-content: center;\n"
        "      align-items: center;\n"
        "      min-height: 100vh;\n"
        "    }\n"
        "\n"
        "    .box {\n"
        "      text-align: center;\n"
        "      background-color: #ffffff;\n"
        "      padding: 2em 1.5em;\n"
        "      border-radius: 1.2em;\n"
        "      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);\n"
        "      animation: fadeIn 1s ease-in-out;\n"
        "      width: 90%;\n"
        "      max-width: 400px;\n"
        "      box-sizing: border-box;\n"
        "    }\n"
        "\n"
        "    .icon {\n"
        "      width: 80px;\n"
        "      height: 80px;\n"
        "      margin: 0 auto 1em;\n"
        "    }\n"
        "\n"
        "    .icon svg {\n"
        "      width: 100%;\n"
        "      height: 100%;\n"
        "    }\n"
        "\n"
        "    .box h1 {\n"
        "      font-size: 1.8em;\n"
        "      color: #4CAF50;\n"
        "      margin-bottom: 0.4em;\n"
        "    }\n"
        "\n"
        "    .box h2 {\n"
        "      font-size: 1.1em;\n"
        "      color: #555;\n"
        "      margin-top: 0;\n"
        "    }\n"
        "\n"
        "    @keyframes fadeIn {\n"
        "      from {\n"
        "        opacity: 0;\n"
        "        transform: translateY(-10px);\n"
        "      }\n"
        "      to {\n"
        "        opacity: 1;\n"
        "        transform: translateY(0);\n"
        "      }\n"
        "    }\n"
        "  </style>\n"
        "</head>\n"
        "<body>\n"
        "  <div class=\"box\">\n"
        "    <div class=\"icon\">\n"
        "      <!-- ✅ SVG 圆形打勾图标 -->\n"
        "      <svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\">\n"
        "        <circle cx=\"50\" cy=\"50\" r=\"45\" stroke=\"#4CAF50\" stroke-width=\"8\" fill=\"none\"/>\n"
        "        <polyline points=\"30,55 45,70 75,40\" fill=\"none\" stroke=\"#4CAF50\" stroke-width=\"8\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n"
        "      </svg>\n"
        "    </div>\n"
        "    <h1>认证成功！</h1>\n"
        "    <h2>现在您可以愉快地上网了 😊</h2>\n"
        "  </div>\n"
        "</body>\n"
        "</html>";

    // Set response headers
    httpdSetResponse(r, "200 OK");
    httpdAddHeader(r, "Content-Type: text/html; charset=UTF-8");
    httpdAddHeader(r, "Cache-Control: no-cache");

    // Send the HTML page
    httpdPrintf(r, "%s", success_html);

    debug(LOG_DEBUG, "Auth success page sent to %s", r->clientAddr);
}

/**
 * @brief Handle authentication failure page using integrated auth_failed.html content
 *
 * This function serves the authentication failure page using the content from
 * auth_failed.html integrated directly into the C code.
 *
 * @param webserver HTTP server instance
 * @param r HTTP request object
 */
void
http_callback_auth_failed(httpd * webserver, request * r)
{
    debug(LOG_INFO, "Auth failed page request from %s", r->clientAddr);

    // Integrated auth_failed.html content
    const char *failed_html =
        "<!DOCTYPE html>\n"
        "<html lang=\"zh-CN\">\n"
        "<head>\n"
        "  <meta charset=\"UTF-8\" />\n"
        "  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n"
        "  <title>认证失败</title>\n"
        "  <style>\n"
        "    body {\n"
        "      margin: 0;\n"
        "      padding: 0;\n"
        "      font-family: \"Segoe UI\", Tahoma, Geneva, Verdana, sans-serif;\n"
        "      background: linear-gradient(to right, #ffe0e0, #ffffff);\n"
        "      display: flex;\n"
        "      justify-content: center;\n"
        "      align-items: center;\n"
        "      min-height: 100vh;\n"
        "    }\n"
        "\n"
        "    .box {\n"
        "      text-align: center;\n"
        "      background-color: #ffffff;\n"
        "      padding: 2em 1.5em;\n"
        "      border-radius: 1.2em;\n"
        "      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);\n"
        "      animation: fadeIn 1s ease-in-out;\n"
        "      width: 90%;\n"
        "      max-width: 400px;\n"
        "      box-sizing: border-box;\n"
        "    }\n"
        "\n"
        "    .box h1 {\n"
        "      font-size: 1.8em;\n"
        "      color: #f44336;\n"
        "      margin-bottom: 0.5em;\n"
        "    }\n"
        "\n"
        "    .box h2 {\n"
        "      font-size: 1.1em;\n"
        "      color: #777;\n"
        "      margin-top: 0;\n"
        "    }\n"
        "\n"
        "    .icon {\n"
        "      margin-bottom: 1em;\n"
        "      width: 56px;\n"
        "      height: 56px;\n"
        "      margin-left: auto;\n"
        "      margin-right: auto;\n"
        "    }\n"
        "\n"
        "    .back-button {\n"
        "      margin-top: 2em;\n"
        "      padding: 0.7em 1.5em;\n"
        "      font-size: 1em;\n"
        "      background-color: #f44336;\n"
        "      color: white;\n"
        "      border: none;\n"
        "      border-radius: 0.5em;\n"
        "      cursor: pointer;\n"
        "      transition: background-color 0.3s ease;\n"
        "    }\n"
        "\n"
        "    .back-button:hover {\n"
        "      background-color: #d32f2f;\n"
        "    }\n"
        "\n"
        "    @keyframes fadeIn {\n"
        "      from {\n"
        "        opacity: 0;\n"
        "        transform: translateY(-10px);\n"
        "      }\n"
        "      to {\n"
        "        opacity: 1;\n"
        "        transform: translateY(0);\n"
        "      }\n"
        "    }\n"
        "  </style>\n"
        "</head>\n"
        "<body>\n"
        "  <div class=\"box\">\n"
        "    <div class=\"icon\" aria-label=\"失败图标\" role=\"img\">\n"
        "      <svg viewBox=\"0 0 64 64\" width=\"56\" height=\"56\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" stroke=\"#f44336\" stroke-width=\"6\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n"
        "        <circle cx=\"32\" cy=\"32\" r=\"30\" fill=\"#fdecea\" />\n"
        "        <line x1=\"20\" y1=\"20\" x2=\"44\" y2=\"44\" />\n"
        "        <line x1=\"44\" y1=\"20\" x2=\"20\" y2=\"44\" />\n"
        "      </svg>\n"
        "    </div>\n"
        "    <h1>认证失败</h1>\n"
        "    <button class=\"back-button\" onclick=\"history.back()\">返回认证</button>\n"
        "  </div>\n"
        "</body>\n"
        "</html>";

    // Set response headers
    httpdSetResponse(r, "200 OK");
    httpdAddHeader(r, "Content-Type: text/html; charset=UTF-8");
    httpdAddHeader(r, "Cache-Control: no-cache");

    // Send the HTML page
    httpdPrintf(r, "%s", failed_html);

    debug(LOG_DEBUG, "Auth failed page sent to %s", r->clientAddr);
}

/**
 * @brief Handle OneLink authentication callback from external auth server
 *
 * This function handles the /Api/codeToken callback from OneLink authentication server.
 * It processes the authentication result and allows/denies client access accordingly.
 *
 * Expected parameters:
 * - codeToken: Authentication token from OneLink server
 * - terminalMac: MAC address of the client device
 *
 * @param webserver HTTP server instance
 * @param r HTTP request object
 */
void
http_callback_api_codetoken(httpd * webserver, request * r)
{
    httpVar *codeToken = httpdGetVariableByName(r, "codeToken");
    httpVar *terminalMac = httpdGetVariableByName(r, "terminalMac");

    debug(LOG_INFO, "OneLink auth callback received from %s", r->clientAddr);

    if (codeToken) {
        debug(LOG_INFO, "OneLink codeToken: %s", codeToken->value);
    }
    if (terminalMac) {
        debug(LOG_INFO, "OneLink terminalMac: %s", terminalMac->value);
    }

    // Check if we have both required parameters
    if (!codeToken || !terminalMac ||
        strcmp(codeToken->value, "NULL") == 0 ||
        strcmp(terminalMac->value, "NULL") == 0) {
        debug(LOG_WARNING, "OneLink auth callback missing required parameters");
        http_callback_auth_failed(webserver, r);
        return;
    }

    // Convert MAC address format for consistency (terminalMac should be in xx:xx:xx:xx:xx:xx format)
    char formatted_mac[18] = {0};
    if (convert_mac_format(terminalMac->value, formatted_mac, sizeof(formatted_mac), ':') != 0) {
        debug(LOG_ERR, "OneLink auth callback: invalid MAC format: %s", terminalMac->value);
        http_callback_auth_failed(webserver, r);
        return;
    }

    debug(LOG_INFO, "OneLink authentication successful for MAC: %s", formatted_mac);

    // Find the client by MAC address
    t_client *client = NULL;
    LOCK_CLIENT_LIST();

    // Search for client by MAC address
    client = client_list_find_by_mac(formatted_mac);

    if (client == NULL) {
        debug(LOG_INFO, "OneLink auth: Client with MAC %s not found in client list, adding new client", formatted_mac);

        // Add new client with a temporary token
        client_list_add("0.0.0.0", formatted_mac, codeToken->value);
        client = client_list_find_by_mac(formatted_mac);
    }

    if (client != NULL) {
        // Update client token
        if (client->token) {
            free(client->token);
        }
        client->token = safe_strdup(codeToken->value);

        // Mark client as authenticated by allowing firewall access
        fw_allow(client, FW_MARK_KNOWN);

        debug(LOG_INFO, "OneLink auth: Client %s (%s) authenticated and allowed", client->ip, client->mac);

        UNLOCK_CLIENT_LIST();

        // Send success response using integrated auth_success.html content
        http_callback_auth_success(webserver, r);
    } else {
        UNLOCK_CLIENT_LIST();
        debug(LOG_ERR, "OneLink auth: Failed to create/find client for MAC %s", formatted_mac);
        http_callback_auth_failed(webserver, r);
    }
}

#ifdef LOCAL_AUTH_SERVER
/*
 * Enhanced Authentication Implementation
 * These functions implement authentication success/failure pages using integrated HTML
 * and support third-party authentication URL redirection.
 */

/**
 * @brief Handle ping requests from WiFiDog gateway (health check)
 *
 * This endpoint is used by WiFiDog to check if the authentication server is alive.
 * It should respond with "Pong" to indicate the server is operational.
 *
 * @param webserver HTTP server instance
 * @param r HTTP request object
 */
void
http_callback_local_auth_ping(httpd * webserver, request * r)
{
    debug(LOG_DEBUG, "Local auth server: ping request from %s", r->clientAddr);

    // Set response headers for plain text
    httpdSetResponse(r, "200 OK");
    httpdAddHeader(r, "Content-Type: text/plain");
    httpdAddHeader(r, "Cache-Control: no-cache");

    // Send the response body
    httpdPrintf(r, "Pong");

    debug(LOG_DEBUG, "Local auth server: responded with Pong");
}

/**
 * @brief Handle authentication requests from WiFiDog gateway
 *
 * This endpoint is called by WiFiDog to verify if a client should be allowed access.
 * It receives parameters like stage, ip, mac, token and responds with "Auth: 1" (allow)
 * or "Auth: 0" (deny).
 *
 * @param webserver HTTP server instance
 * @param r HTTP request object
 */
void
http_callback_local_auth_auth(httpd * webserver, request * r)
{
    httpVar *stage = httpdGetVariableByName(r, "stage");
    httpVar *ip = httpdGetVariableByName(r, "ip");
    httpVar *mac = httpdGetVariableByName(r, "mac");
    httpVar *token = httpdGetVariableByName(r, "token");

    debug(LOG_INFO, "Local auth server: auth request from %s", r->clientAddr);
    debug(LOG_DEBUG, "Local auth server: stage=%s, ip=%s, mac=%s, token=%s",
          stage ? stage->value : "NULL",
          ip ? ip->value : "NULL",
          mac ? mac->value : "NULL",
          token ? token->value : "NULL");

    // Local authentication logic
    // This implements a flexible authentication strategy
    const char *auth_response = "Auth: 0";  // Default deny

    if (stage && strcmp(stage->value, "login") == 0) {
        // User is trying to login - validate token
        if (token && strstr(token->value, "local_auth_token_") == token->value) {
            // Token format is valid, allow access
            auth_response = "Auth: 1";
            debug(LOG_INFO, "Local auth server: Login approved for token %s", token->value);
        } else {
            debug(LOG_WARNING, "Local auth server: Invalid token for login: %s",
                  token ? token->value : "NULL");
        }
    } else if (stage && strcmp(stage->value, "counters") == 0) {
        // Gateway is updating traffic counters - always allow
        auth_response = "Auth: 1";
        debug(LOG_DEBUG, "Local auth server: Counters update approved");
    } else {
        // For other stages or no stage, implement default policy
        // In this simple implementation, we allow all authenticated users
        if (token && strlen(token->value) > 0) {
            auth_response = "Auth: 1";
            debug(LOG_DEBUG, "Local auth server: General auth approved for existing token");
        } else {
            debug(LOG_DEBUG, "Local auth server: No valid token, denying access");
        }
    }

    // Set response headers
    httpdSetResponse(r, "200 OK");
    httpdAddHeader(r, "Content-Type: text/plain");
    httpdAddHeader(r, "Cache-Control: no-cache");

    // Send the response
    httpdPrintf(r, "%s", auth_response);

    debug(LOG_INFO, "Local auth server: responded with %s for %s",
          auth_response, ip ? ip->value : "unknown IP");
}

/**
 * @brief Handle login page requests from clients
 *
 * This endpoint generates and serves the login page that users see when they
 * first try to access the internet. It includes a simple form that submits
 * back to the WiFiDog gateway for authentication.
 *
 * @param webserver HTTP server instance
 * @param r HTTP request object
 */
void
http_callback_local_auth_login(httpd * webserver, request * r)
{
    httpVar *gw_address = httpdGetVariableByName(r, "gw_address");
    httpVar *gw_port = httpdGetVariableByName(r, "gw_port");
    httpVar *gw_id = httpdGetVariableByName(r, "gw_id");
    httpVar *url = httpdGetVariableByName(r, "url");
    httpVar *mac = httpdGetVariableByName(r, "mac");

    debug(LOG_INFO, "Local auth server: login page request from %s", r->clientAddr);
    debug(LOG_DEBUG, "Local auth server: gw_address=%s, gw_port=%s, gw_id=%s",
          gw_address ? gw_address->value : "NULL",
          gw_port ? gw_port->value : "NULL",
          gw_id ? gw_id->value : "NULL");

    // Generate login page HTML
    char *login_html = NULL;
    safe_asprintf(&login_html,
        "<!DOCTYPE html>\n"
        "<html>\n"
        "<head>\n"
        "    <title>WiFiDog Local Authentication</title>\n"
        "    <meta charset=\"UTF-8\">\n"
        "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n"
        "    <style>\n"
        "        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }\n"
        "        .container { max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n"
        "        h1 { color: #333; text-align: center; margin-bottom: 30px; }\n"
        "        .info { background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; }\n"
        "        .button { background: #4CAF50; color: white; padding: 15px 32px; text-decoration: none; display: inline-block; font-size: 16px; margin: 10px 0; cursor: pointer; border-radius: 5px; border: none; width: 100%%; }\n"
        "        .button:hover { background: #45a049; }\n"
        "        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }\n"
        "    </style>\n"
        "</head>\n"
        "<body>\n"
        "    <div class=\"container\">\n"
        "        <h1>WiFiDog Local Authentication</h1>\n"
        "        <p>Welcome to the WiFiDog captive portal with local authentication!</p>\n"
        "        \n"
        "        <div class=\"info\">\n"
        "            <p><strong>Gateway ID:</strong> %s</p>\n"
        "            <p><strong>Your IP:</strong> %s</p>\n"
        "            <p><strong>MAC Address:</strong> %s</p>\n"
        "            %s%s%s\n"
        "        </div>\n"
        "        \n"
        "        <form method=\"get\" action=\"http://%s:%s/wifidog/auth\">\n"
        "            <input type=\"hidden\" name=\"token\" value=\"local_auth_token_%ld\">\n"
        "            <input type=\"submit\" value=\"Connect to Internet (Free Access)\" class=\"button\">\n"
        "        </form>\n"
        "        \n"
        "        <div class=\"footer\">\n"
        "            <p>This is a local authentication server integrated into WiFiDog.</p>\n"
        "            <p>No external authentication server required.</p>\n"
        "        </div>\n"
        "    </div>\n"
        "</body>\n"
        "</html>",
        gw_id ? gw_id->value : "Unknown",
        r->clientAddr,
        mac ? mac->value : "Unknown",
        url ? "<p><strong>Requested URL:</strong> " : "",
        url ? url->value : "",
        url ? "</p>" : "",
        gw_address ? gw_address->value : "*************",
        gw_port ? gw_port->value : "2060",
        time(NULL)  // Simple timestamp-based token
    );

    // Set response headers
    httpdSetResponse(r, "200 OK");
    httpdAddHeader(r, "Content-Type: text/html; charset=UTF-8");
    httpdAddHeader(r, "Cache-Control: no-cache");

    // Send the HTML page
    httpdPrintf(r, "%s", login_html);

    free(login_html);
    debug(LOG_DEBUG, "Local auth server: sent login page to %s", r->clientAddr);
}

/**
 * @brief Handle portal page requests (after successful authentication)
 *
 * This endpoint serves a welcome page shown to users after they have
 * successfully authenticated and gained internet access.
 *
 * @param webserver HTTP server instance
 * @param r HTTP request object
 */
void
http_callback_local_auth_portal(httpd * webserver, request * r)
{
    debug(LOG_INFO, "Local auth server: portal page request from %s", r->clientAddr);

    // Generate portal page HTML
    char *portal_html = NULL;
    safe_asprintf(&portal_html,
        "<!DOCTYPE html>\n"
        "<html>\n"
        "<head>\n"
        "    <title>WiFiDog - Welcome</title>\n"
        "    <meta charset=\"UTF-8\">\n"
        "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n"
        "    <style>\n"
        "        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }\n"
        "        .container { max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }\n"
        "        h1 { color: #4CAF50; margin-bottom: 20px; }\n"
        "        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0; }\n"
        "        .link { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; }\n"
        "        .link:hover { background: #0056b3; }\n"
        "        .footer { margin-top: 30px; color: #666; font-size: 12px; }\n"
        "    </style>\n"
        "</head>\n"
        "<body>\n"
        "    <div class=\"container\">\n"
        "        <h1>Welcome!</h1>\n"
        "        \n"
        "        <div class=\"success\">\n"
        "            <p><strong>Authentication Successful!</strong></p>\n"
        "            <p>You have successfully authenticated with WiFiDog and now have internet access.</p>\n"
        "        </div>\n"
        "        \n"
        "        <p>You can now browse the internet freely.</p>\n"
        "        \n"
        "        <a href=\"http://www.baidu.com\" class=\"link\">Test Connection (Baidu)</a>\n"
        "        <a href=\"http://www.google.com\" class=\"link\">Test Connection (Google)</a>\n"
        "        \n"
        "        <div class=\"footer\">\n"
        "            <p>Authenticated via WiFiDog Local Authentication Server</p>\n"
        "            <p>Your IP: %s</p>\n"
        "        </div>\n"
        "    </div>\n"
        "</body>\n"
        "</html>",
        r->clientAddr
    );

    // Set response headers
    httpdSetResponse(r, "200 OK");
    httpdAddHeader(r, "Content-Type: text/html; charset=UTF-8");
    httpdAddHeader(r, "Cache-Control: no-cache");

    // Send the HTML page
    httpdPrintf(r, "%s", portal_html);

    free(portal_html);
    debug(LOG_DEBUG, "Local auth server: sent portal page to %s", r->clientAddr);
}

/**
 * @brief Handle error message requests
 *
 * This endpoint serves error pages when authentication fails or other
 * errors occur in the WiFiDog system.
 *
 * @param webserver HTTP server instance
 * @param r HTTP request object
 */
void
http_callback_local_auth_message(httpd * webserver, request * r)
{
    httpVar *message = httpdGetVariableByName(r, "message");

    debug(LOG_INFO, "Local auth server: error message request from %s", r->clientAddr);
    debug(LOG_DEBUG, "Local auth server: message=%s", message ? message->value : "Unknown error");

    const char *error_message = message ? message->value : "Unknown error occurred";

    // Generate error page HTML
    char *error_html = NULL;
    safe_asprintf(&error_html,
        "<!DOCTYPE html>\n"
        "<html>\n"
        "<head>\n"
        "    <title>WiFiDog - Error</title>\n"
        "    <meta charset=\"UTF-8\">\n"
        "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n"
        "    <style>\n"
        "        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }\n"
        "        .container { max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }\n"
        "        h1 { color: #dc3545; margin-bottom: 20px; }\n"
        "        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0; }\n"
        "        .button { background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; }\n"
        "        .button:hover { background: #545b62; }\n"
        "        .footer { margin-top: 30px; color: #666; font-size: 12px; }\n"
        "    </style>\n"
        "</head>\n"
        "<body>\n"
        "    <div class=\"container\">\n"
        "        <h1>WiFiDog Error</h1>\n"
        "        \n"
        "        <div class=\"error\">\n"
        "            <p><strong>Error:</strong> %s</p>\n"
        "        </div>\n"
        "        \n"
        "        <p>Please try again or contact the network administrator if the problem persists.</p>\n"
        "        \n"
        "        <a href=\"javascript:history.back()\" class=\"button\">Go Back</a>\n"
        "        \n"
        "        <div class=\"footer\">\n"
        "            <p>WiFiDog Local Authentication Server</p>\n"
        "            <p>Your IP: %s</p>\n"
        "        </div>\n"
        "    </div>\n"
        "</body>\n"
        "</html>",
        error_message,
        r->clientAddr
    );

    // Set response headers
    httpdSetResponse(r, "200 OK");
    httpdAddHeader(r, "Content-Type: text/html; charset=UTF-8");
    httpdAddHeader(r, "Cache-Control: no-cache");

    // Send the HTML page
    httpdPrintf(r, "%s", error_html);

    free(error_html);
    debug(LOG_DEBUG, "Local auth server: sent error page to %s with message: %s",
          r->clientAddr, error_message);
}

/**
 * @brief Handle authentication success page using integrated auth_success.html content
 *
 * This function serves the authentication success page using the content from
 * auth_success.html integrated directly into the C code.
 *
 * @param webserver HTTP server instance
 * @param r HTTP request object
 */

#endif
#endif
