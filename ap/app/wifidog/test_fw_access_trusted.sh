#!/bin/bash

# Test script for iptables_fw_access trusted chain functionality
# This script demonstrates how MAC addresses are added/removed from CHAIN_TRUSTED

echo "=== iptables_fw_access CHAIN_TRUSTED Test ==="

# Function to simulate iptables command
iptables_do_command() {
    echo "IPTABLES: $*"
}

# Function to simulate debug logging
debug() {
    local level="$1"
    shift
    echo "DEBUG[$level]: $*"
}

# Constants
CHAIN_OUTGOING="WiFiDog_br0_Outgoing"
CHAIN_INCOMING="WiFiDog_br0_Incoming"
CHAIN_TRUSTED="WiFiDog_br0_Trusted"
FW_MARK_KNOWN=2

# Simulate iptables_fw_access function behavior
iptables_fw_access() {
    local type="$1"
    local ip="$2"
    local mac="$3"
    local tag="$4"
    local rc=0
    
    echo "Called iptables_fw_access($type, $ip, $mac, $tag)"
    
    case "$type" in
        "FW_ACCESS_ALLOW")
            echo "  Processing FW_ACCESS_ALLOW..."
            
            # Original WiFiDog rules
            iptables_do_command "-t mangle -A $CHAIN_OUTGOING -s $ip -m mac --mac-source $mac -j MARK --set-mark $tag"
            iptables_do_command "-t mangle -A $CHAIN_INCOMING -d $ip -j ACCEPT"
            
            # ENABLE_QRZL_APP addition
            echo "  #ifdef ENABLE_QRZL_APP"
            debug "LOG_DEBUG" "Adding MAC $mac to trusted chain for IP $ip"
            iptables_do_command "-t mangle -A $CHAIN_TRUSTED -m mac --mac-source $mac -j MARK --set-mark $FW_MARK_KNOWN"
            echo "  #endif"
            ;;
            
        "FW_ACCESS_DENY")
            echo "  Processing FW_ACCESS_DENY..."
            
            # Original WiFiDog rules
            iptables_do_command "-t mangle -D $CHAIN_OUTGOING -s $ip -m mac --mac-source $mac -j MARK --set-mark $tag"
            iptables_do_command "-t mangle -D $CHAIN_INCOMING -d $ip -j ACCEPT"
            
            # ENABLE_QRZL_APP addition
            echo "  #ifdef ENABLE_QRZL_APP"
            debug "LOG_DEBUG" "Removing MAC $mac from trusted chain for IP $ip"
            iptables_do_command "-t mangle -D $CHAIN_TRUSTED -m mac --mac-source $mac -j MARK --set-mark $FW_MARK_KNOWN"
            echo "  #endif"
            ;;
            
        *)
            echo "  Unknown access type: $type"
            rc=-1
            ;;
    esac
    
    echo "  Return code: $rc"
    echo ""
    return $rc
}

# Test scenarios
echo "=== Test Scenario 1: Allow Access ==="
echo "Client connects: IP=***************, MAC=aa:bb:cc:dd:ee:ff"
iptables_fw_access "FW_ACCESS_ALLOW" "***************" "aa:bb:cc:dd:ee:ff" "2"

echo "=== Test Scenario 2: Deny Access ==="
echo "Client disconnects or access denied: IP=***************, MAC=aa:bb:cc:dd:ee:ff"
iptables_fw_access "FW_ACCESS_DENY" "***************" "aa:bb:cc:dd:ee:ff" "2"

echo "=== Test Scenario 3: Multiple Clients ==="
echo "Multiple clients connecting..."
iptables_fw_access "FW_ACCESS_ALLOW" "***************" "11:22:33:44:55:66" "2"
iptables_fw_access "FW_ACCESS_ALLOW" "***************" "77:88:99:aa:bb:cc" "2"

echo "=== Test Scenario 4: Partial Cleanup ==="
echo "One client disconnects..."
iptables_fw_access "FW_ACCESS_DENY" "***************" "11:22:33:44:55:66" "2"

echo "=== Expected iptables Rules Summary ==="
echo "After all operations, the following rules should exist:"
echo ""
echo "CHAIN_TRUSTED rules (from ENABLE_QRZL_APP):"
echo "  -A $CHAIN_TRUSTED -m mac --mac-source 77:88:99:aa:bb:cc -j MARK --set-mark $FW_MARK_KNOWN"
echo ""
echo "CHAIN_OUTGOING rules:"
echo "  -A $CHAIN_OUTGOING -s *************** -m mac --mac-source 77:88:99:aa:bb:cc -j MARK --set-mark 2"
echo ""
echo "CHAIN_INCOMING rules:"
echo "  -A $CHAIN_INCOMING -d *************** -j ACCEPT"
echo ""

echo "=== Key Benefits ==="
echo "1. MAC addresses in CHAIN_TRUSTED bypass authentication"
echo "2. Automatic cleanup when clients disconnect"
echo "3. Consistent with existing WiFiDog architecture"
echo "4. Conditional compilation with ENABLE_QRZL_APP"
echo ""

echo "=== Test Complete ==="
