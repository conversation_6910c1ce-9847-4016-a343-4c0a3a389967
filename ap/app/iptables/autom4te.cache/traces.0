m4trace:configure.ac:2: -1- AC_INIT([iptables], [*******])
m4trace:configure.ac:2: -1- m4_pattern_forbid([^_?A[CHUM]_])
m4trace:configure.ac:2: -1- m4_pattern_forbid([_AC_])
m4trace:configure.ac:2: -1- m4_pattern_forbid([^LIBOBJS$], [do not use LIBOBJS directly, use AC_LIBOBJ (see section `AC_LIBOBJ vs LIBOBJS'])
m4trace:configure.ac:2: -1- m4_pattern_allow([^AS_FLAGS$])
m4trace:configure.ac:2: -1- m4_pattern_forbid([^_?m4_])
m4trace:configure.ac:2: -1- m4_pattern_forbid([^dnl$])
m4trace:configure.ac:2: -1- m4_pattern_forbid([^_?AS_])
m4trace:configure.ac:2: -1- AC_SUBST([SHELL])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([SHELL])
m4trace:configure.ac:2: -1- m4_pattern_allow([^SHELL$])
m4trace:configure.ac:2: -1- AC_SUBST([PATH_SEPARATOR])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([PATH_SEPARATOR])
m4trace:configure.ac:2: -1- m4_pattern_allow([^PATH_SEPARATOR$])
m4trace:configure.ac:2: -1- AC_SUBST([PACKAGE_NAME], [m4_ifdef([AC_PACKAGE_NAME],      ['AC_PACKAGE_NAME'])])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([PACKAGE_NAME])
m4trace:configure.ac:2: -1- m4_pattern_allow([^PACKAGE_NAME$])
m4trace:configure.ac:2: -1- AC_SUBST([PACKAGE_TARNAME], [m4_ifdef([AC_PACKAGE_TARNAME],   ['AC_PACKAGE_TARNAME'])])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([PACKAGE_TARNAME])
m4trace:configure.ac:2: -1- m4_pattern_allow([^PACKAGE_TARNAME$])
m4trace:configure.ac:2: -1- AC_SUBST([PACKAGE_VERSION], [m4_ifdef([AC_PACKAGE_VERSION],   ['AC_PACKAGE_VERSION'])])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([PACKAGE_VERSION])
m4trace:configure.ac:2: -1- m4_pattern_allow([^PACKAGE_VERSION$])
m4trace:configure.ac:2: -1- AC_SUBST([PACKAGE_STRING], [m4_ifdef([AC_PACKAGE_STRING],    ['AC_PACKAGE_STRING'])])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([PACKAGE_STRING])
m4trace:configure.ac:2: -1- m4_pattern_allow([^PACKAGE_STRING$])
m4trace:configure.ac:2: -1- AC_SUBST([PACKAGE_BUGREPORT], [m4_ifdef([AC_PACKAGE_BUGREPORT], ['AC_PACKAGE_BUGREPORT'])])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([PACKAGE_BUGREPORT])
m4trace:configure.ac:2: -1- m4_pattern_allow([^PACKAGE_BUGREPORT$])
m4trace:configure.ac:2: -1- AC_SUBST([PACKAGE_URL], [m4_ifdef([AC_PACKAGE_URL],       ['AC_PACKAGE_URL'])])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([PACKAGE_URL])
m4trace:configure.ac:2: -1- m4_pattern_allow([^PACKAGE_URL$])
m4trace:configure.ac:2: -1- AC_SUBST([exec_prefix], [NONE])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([exec_prefix])
m4trace:configure.ac:2: -1- m4_pattern_allow([^exec_prefix$])
m4trace:configure.ac:2: -1- AC_SUBST([prefix], [NONE])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([prefix])
m4trace:configure.ac:2: -1- m4_pattern_allow([^prefix$])
m4trace:configure.ac:2: -1- AC_SUBST([program_transform_name], [s,x,x,])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([program_transform_name])
m4trace:configure.ac:2: -1- m4_pattern_allow([^program_transform_name$])
m4trace:configure.ac:2: -1- AC_SUBST([bindir], ['${exec_prefix}/bin'])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([bindir])
m4trace:configure.ac:2: -1- m4_pattern_allow([^bindir$])
m4trace:configure.ac:2: -1- AC_SUBST([sbindir], ['${exec_prefix}/sbin'])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([sbindir])
m4trace:configure.ac:2: -1- m4_pattern_allow([^sbindir$])
m4trace:configure.ac:2: -1- AC_SUBST([libexecdir], ['${exec_prefix}/libexec'])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([libexecdir])
m4trace:configure.ac:2: -1- m4_pattern_allow([^libexecdir$])
m4trace:configure.ac:2: -1- AC_SUBST([datarootdir], ['${prefix}/share'])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([datarootdir])
m4trace:configure.ac:2: -1- m4_pattern_allow([^datarootdir$])
m4trace:configure.ac:2: -1- AC_SUBST([datadir], ['${datarootdir}'])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([datadir])
m4trace:configure.ac:2: -1- m4_pattern_allow([^datadir$])
m4trace:configure.ac:2: -1- AC_SUBST([sysconfdir], ['${prefix}/etc'])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([sysconfdir])
m4trace:configure.ac:2: -1- m4_pattern_allow([^sysconfdir$])
m4trace:configure.ac:2: -1- AC_SUBST([sharedstatedir], ['${prefix}/com'])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([sharedstatedir])
m4trace:configure.ac:2: -1- m4_pattern_allow([^sharedstatedir$])
m4trace:configure.ac:2: -1- AC_SUBST([localstatedir], ['${prefix}/var'])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([localstatedir])
m4trace:configure.ac:2: -1- m4_pattern_allow([^localstatedir$])
m4trace:configure.ac:2: -1- AC_SUBST([includedir], ['${prefix}/include'])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([includedir])
m4trace:configure.ac:2: -1- m4_pattern_allow([^includedir$])
m4trace:configure.ac:2: -1- AC_SUBST([oldincludedir], ['/usr/include'])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([oldincludedir])
m4trace:configure.ac:2: -1- m4_pattern_allow([^oldincludedir$])
m4trace:configure.ac:2: -1- AC_SUBST([docdir], [m4_ifset([AC_PACKAGE_TARNAME],
				     ['${datarootdir}/doc/${PACKAGE_TARNAME}'],
				     ['${datarootdir}/doc/${PACKAGE}'])])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([docdir])
m4trace:configure.ac:2: -1- m4_pattern_allow([^docdir$])
m4trace:configure.ac:2: -1- AC_SUBST([infodir], ['${datarootdir}/info'])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([infodir])
m4trace:configure.ac:2: -1- m4_pattern_allow([^infodir$])
m4trace:configure.ac:2: -1- AC_SUBST([htmldir], ['${docdir}'])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([htmldir])
m4trace:configure.ac:2: -1- m4_pattern_allow([^htmldir$])
m4trace:configure.ac:2: -1- AC_SUBST([dvidir], ['${docdir}'])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([dvidir])
m4trace:configure.ac:2: -1- m4_pattern_allow([^dvidir$])
m4trace:configure.ac:2: -1- AC_SUBST([pdfdir], ['${docdir}'])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([pdfdir])
m4trace:configure.ac:2: -1- m4_pattern_allow([^pdfdir$])
m4trace:configure.ac:2: -1- AC_SUBST([psdir], ['${docdir}'])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([psdir])
m4trace:configure.ac:2: -1- m4_pattern_allow([^psdir$])
m4trace:configure.ac:2: -1- AC_SUBST([libdir], ['${exec_prefix}/lib'])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([libdir])
m4trace:configure.ac:2: -1- m4_pattern_allow([^libdir$])
m4trace:configure.ac:2: -1- AC_SUBST([localedir], ['${datarootdir}/locale'])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([localedir])
m4trace:configure.ac:2: -1- m4_pattern_allow([^localedir$])
m4trace:configure.ac:2: -1- AC_SUBST([mandir], ['${datarootdir}/man'])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([mandir])
m4trace:configure.ac:2: -1- m4_pattern_allow([^mandir$])
m4trace:configure.ac:2: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_NAME])
m4trace:configure.ac:2: -1- m4_pattern_allow([^PACKAGE_NAME$])
m4trace:configure.ac:2: -1- AH_OUTPUT([PACKAGE_NAME], [/* Define to the full name of this package. */
@%:@undef PACKAGE_NAME])
m4trace:configure.ac:2: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_TARNAME])
m4trace:configure.ac:2: -1- m4_pattern_allow([^PACKAGE_TARNAME$])
m4trace:configure.ac:2: -1- AH_OUTPUT([PACKAGE_TARNAME], [/* Define to the one symbol short name of this package. */
@%:@undef PACKAGE_TARNAME])
m4trace:configure.ac:2: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_VERSION])
m4trace:configure.ac:2: -1- m4_pattern_allow([^PACKAGE_VERSION$])
m4trace:configure.ac:2: -1- AH_OUTPUT([PACKAGE_VERSION], [/* Define to the version of this package. */
@%:@undef PACKAGE_VERSION])
m4trace:configure.ac:2: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_STRING])
m4trace:configure.ac:2: -1- m4_pattern_allow([^PACKAGE_STRING$])
m4trace:configure.ac:2: -1- AH_OUTPUT([PACKAGE_STRING], [/* Define to the full name and version of this package. */
@%:@undef PACKAGE_STRING])
m4trace:configure.ac:2: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_BUGREPORT])
m4trace:configure.ac:2: -1- m4_pattern_allow([^PACKAGE_BUGREPORT$])
m4trace:configure.ac:2: -1- AH_OUTPUT([PACKAGE_BUGREPORT], [/* Define to the address where bug reports for this package should be sent. */
@%:@undef PACKAGE_BUGREPORT])
m4trace:configure.ac:2: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_URL])
m4trace:configure.ac:2: -1- m4_pattern_allow([^PACKAGE_URL$])
m4trace:configure.ac:2: -1- AH_OUTPUT([PACKAGE_URL], [/* Define to the home page for this package. */
@%:@undef PACKAGE_URL])
m4trace:configure.ac:2: -1- AC_SUBST([DEFS])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([DEFS])
m4trace:configure.ac:2: -1- m4_pattern_allow([^DEFS$])
m4trace:configure.ac:2: -1- AC_SUBST([ECHO_C])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([ECHO_C])
m4trace:configure.ac:2: -1- m4_pattern_allow([^ECHO_C$])
m4trace:configure.ac:2: -1- AC_SUBST([ECHO_N])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([ECHO_N])
m4trace:configure.ac:2: -1- m4_pattern_allow([^ECHO_N$])
m4trace:configure.ac:2: -1- AC_SUBST([ECHO_T])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([ECHO_T])
m4trace:configure.ac:2: -1- m4_pattern_allow([^ECHO_T$])
m4trace:configure.ac:2: -1- AC_SUBST([LIBS])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([LIBS])
m4trace:configure.ac:2: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.ac:2: -1- AC_SUBST([build_alias])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([build_alias])
m4trace:configure.ac:2: -1- m4_pattern_allow([^build_alias$])
m4trace:configure.ac:2: -1- AC_SUBST([host_alias])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([host_alias])
m4trace:configure.ac:2: -1- m4_pattern_allow([^host_alias$])
m4trace:configure.ac:2: -1- AC_SUBST([target_alias])
m4trace:configure.ac:2: -1- AC_SUBST_TRACE([target_alias])
m4trace:configure.ac:2: -1- m4_pattern_allow([^target_alias$])
m4trace:configure.ac:8: -1- AC_CONFIG_HEADERS([config.h])
m4trace:configure.ac:10: -1- AC_REQUIRE_AUX_FILE([install-sh])
m4trace:configure.ac:10: -1- AC_SUBST([INSTALL_PROGRAM])
m4trace:configure.ac:10: -1- AC_SUBST_TRACE([INSTALL_PROGRAM])
m4trace:configure.ac:10: -1- m4_pattern_allow([^INSTALL_PROGRAM$])
m4trace:configure.ac:10: -1- AC_SUBST([INSTALL_SCRIPT])
m4trace:configure.ac:10: -1- AC_SUBST_TRACE([INSTALL_SCRIPT])
m4trace:configure.ac:10: -1- m4_pattern_allow([^INSTALL_SCRIPT$])
m4trace:configure.ac:10: -1- AC_SUBST([INSTALL_DATA])
m4trace:configure.ac:10: -1- AC_SUBST_TRACE([INSTALL_DATA])
m4trace:configure.ac:10: -1- m4_pattern_allow([^INSTALL_DATA$])
m4trace:configure.ac:11: -1- AM_INIT_AUTOMAKE([-Wall])
m4trace:configure.ac:11: -1- m4_pattern_allow([^AM_[A-Z]+FLAGS$])
m4trace:configure.ac:11: -1- AM_AUTOMAKE_VERSION([1.10.1])
m4trace:configure.ac:11: -1- AC_SUBST([am__isrc], [' -I$(srcdir)'])
m4trace:configure.ac:11: -1- AC_SUBST_TRACE([am__isrc])
m4trace:configure.ac:11: -1- m4_pattern_allow([^am__isrc$])
m4trace:configure.ac:11: -1- _AM_SUBST_NOTMAKE([am__isrc])
m4trace:configure.ac:11: -1- AC_SUBST([CYGPATH_W])
m4trace:configure.ac:11: -1- AC_SUBST_TRACE([CYGPATH_W])
m4trace:configure.ac:11: -1- m4_pattern_allow([^CYGPATH_W$])
m4trace:configure.ac:11: -1- _m4_warn([obsolete], [The macro `AC_FOREACH' is obsolete.
You should run autoupdate.], [../../lib/autoconf/general.m4:194: AC_FOREACH is expanded from...
aclocal.m4:7358: _AM_SET_OPTIONS is expanded from...
aclocal.m4:7050: AM_INIT_AUTOMAKE is expanded from...
configure.ac:11: the top level])
m4trace:configure.ac:11: -1- AC_SUBST([PACKAGE], ['AC_PACKAGE_TARNAME'])
m4trace:configure.ac:11: -1- AC_SUBST_TRACE([PACKAGE])
m4trace:configure.ac:11: -1- m4_pattern_allow([^PACKAGE$])
m4trace:configure.ac:11: -1- AC_SUBST([VERSION], ['AC_PACKAGE_VERSION'])
m4trace:configure.ac:11: -1- AC_SUBST_TRACE([VERSION])
m4trace:configure.ac:11: -1- m4_pattern_allow([^VERSION$])
m4trace:configure.ac:11: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE])
m4trace:configure.ac:11: -1- m4_pattern_allow([^PACKAGE$])
m4trace:configure.ac:11: -1- AH_OUTPUT([PACKAGE], [/* Name of package */
@%:@undef PACKAGE])
m4trace:configure.ac:11: -1- AC_DEFINE_TRACE_LITERAL([VERSION])
m4trace:configure.ac:11: -1- m4_pattern_allow([^VERSION$])
m4trace:configure.ac:11: -1- AH_OUTPUT([VERSION], [/* Version number of package */
@%:@undef VERSION])
m4trace:configure.ac:11: -1- AC_REQUIRE_AUX_FILE([missing])
m4trace:configure.ac:11: -1- AC_SUBST([ACLOCAL])
m4trace:configure.ac:11: -1- AC_SUBST_TRACE([ACLOCAL])
m4trace:configure.ac:11: -1- m4_pattern_allow([^ACLOCAL$])
m4trace:configure.ac:11: -1- AC_SUBST([AUTOCONF])
m4trace:configure.ac:11: -1- AC_SUBST_TRACE([AUTOCONF])
m4trace:configure.ac:11: -1- m4_pattern_allow([^AUTOCONF$])
m4trace:configure.ac:11: -1- AC_SUBST([AUTOMAKE])
m4trace:configure.ac:11: -1- AC_SUBST_TRACE([AUTOMAKE])
m4trace:configure.ac:11: -1- m4_pattern_allow([^AUTOMAKE$])
m4trace:configure.ac:11: -1- AC_SUBST([AUTOHEADER])
m4trace:configure.ac:11: -1- AC_SUBST_TRACE([AUTOHEADER])
m4trace:configure.ac:11: -1- m4_pattern_allow([^AUTOHEADER$])
m4trace:configure.ac:11: -1- AC_SUBST([MAKEINFO])
m4trace:configure.ac:11: -1- AC_SUBST_TRACE([MAKEINFO])
m4trace:configure.ac:11: -1- m4_pattern_allow([^MAKEINFO$])
m4trace:configure.ac:11: -1- AC_SUBST([install_sh])
m4trace:configure.ac:11: -1- AC_SUBST_TRACE([install_sh])
m4trace:configure.ac:11: -1- m4_pattern_allow([^install_sh$])
m4trace:configure.ac:11: -1- AC_SUBST([STRIP])
m4trace:configure.ac:11: -1- AC_SUBST_TRACE([STRIP])
m4trace:configure.ac:11: -1- m4_pattern_allow([^STRIP$])
m4trace:configure.ac:11: -1- AC_SUBST([INSTALL_STRIP_PROGRAM])
m4trace:configure.ac:11: -1- AC_SUBST_TRACE([INSTALL_STRIP_PROGRAM])
m4trace:configure.ac:11: -1- m4_pattern_allow([^INSTALL_STRIP_PROGRAM$])
m4trace:configure.ac:11: -1- AC_REQUIRE_AUX_FILE([install-sh])
m4trace:configure.ac:11: -1- AC_SUBST([MKDIR_P])
m4trace:configure.ac:11: -1- AC_SUBST_TRACE([MKDIR_P])
m4trace:configure.ac:11: -1- m4_pattern_allow([^MKDIR_P$])
m4trace:configure.ac:11: -1- AC_SUBST([mkdir_p], ["$MKDIR_P"])
m4trace:configure.ac:11: -1- AC_SUBST_TRACE([mkdir_p])
m4trace:configure.ac:11: -1- m4_pattern_allow([^mkdir_p$])
m4trace:configure.ac:11: -1- AC_SUBST([AWK])
m4trace:configure.ac:11: -1- AC_SUBST_TRACE([AWK])
m4trace:configure.ac:11: -1- m4_pattern_allow([^AWK$])
m4trace:configure.ac:11: -1- AC_SUBST([SET_MAKE])
m4trace:configure.ac:11: -1- AC_SUBST_TRACE([SET_MAKE])
m4trace:configure.ac:11: -1- m4_pattern_allow([^SET_MAKE$])
m4trace:configure.ac:11: -1- AC_SUBST([am__leading_dot])
m4trace:configure.ac:11: -1- AC_SUBST_TRACE([am__leading_dot])
m4trace:configure.ac:11: -1- m4_pattern_allow([^am__leading_dot$])
m4trace:configure.ac:11: -1- AC_SUBST([AMTAR])
m4trace:configure.ac:11: -1- AC_SUBST_TRACE([AMTAR])
m4trace:configure.ac:11: -1- m4_pattern_allow([^AMTAR$])
m4trace:configure.ac:11: -1- AC_SUBST([am__tar])
m4trace:configure.ac:11: -1- AC_SUBST_TRACE([am__tar])
m4trace:configure.ac:11: -1- m4_pattern_allow([^am__tar$])
m4trace:configure.ac:11: -1- AC_SUBST([am__untar])
m4trace:configure.ac:11: -1- AC_SUBST_TRACE([am__untar])
m4trace:configure.ac:11: -1- m4_pattern_allow([^am__untar$])
m4trace:configure.ac:12: -1- AC_SUBST([CC])
m4trace:configure.ac:12: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:12: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:12: -1- AC_SUBST([CFLAGS])
m4trace:configure.ac:12: -1- AC_SUBST_TRACE([CFLAGS])
m4trace:configure.ac:12: -1- m4_pattern_allow([^CFLAGS$])
m4trace:configure.ac:12: -1- AC_SUBST([LDFLAGS])
m4trace:configure.ac:12: -1- AC_SUBST_TRACE([LDFLAGS])
m4trace:configure.ac:12: -1- m4_pattern_allow([^LDFLAGS$])
m4trace:configure.ac:12: -1- AC_SUBST([LIBS])
m4trace:configure.ac:12: -1- AC_SUBST_TRACE([LIBS])
m4trace:configure.ac:12: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.ac:12: -1- AC_SUBST([CPPFLAGS])
m4trace:configure.ac:12: -1- AC_SUBST_TRACE([CPPFLAGS])
m4trace:configure.ac:12: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.ac:12: -1- AC_SUBST([CC])
m4trace:configure.ac:12: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:12: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:12: -1- AC_SUBST([CC])
m4trace:configure.ac:12: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:12: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:12: -1- AC_SUBST([CC])
m4trace:configure.ac:12: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:12: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:12: -1- AC_SUBST([CC])
m4trace:configure.ac:12: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:12: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:12: -1- AC_SUBST([ac_ct_CC])
m4trace:configure.ac:12: -1- AC_SUBST_TRACE([ac_ct_CC])
m4trace:configure.ac:12: -1- m4_pattern_allow([^ac_ct_CC$])
m4trace:configure.ac:12: -1- AC_SUBST([EXEEXT], [$ac_cv_exeext])
m4trace:configure.ac:12: -1- AC_SUBST_TRACE([EXEEXT])
m4trace:configure.ac:12: -1- m4_pattern_allow([^EXEEXT$])
m4trace:configure.ac:12: -1- AC_SUBST([OBJEXT], [$ac_cv_objext])
m4trace:configure.ac:12: -1- AC_SUBST_TRACE([OBJEXT])
m4trace:configure.ac:12: -1- m4_pattern_allow([^OBJEXT$])
m4trace:configure.ac:12: -1- AC_SUBST([DEPDIR], ["${am__leading_dot}deps"])
m4trace:configure.ac:12: -1- AC_SUBST_TRACE([DEPDIR])
m4trace:configure.ac:12: -1- m4_pattern_allow([^DEPDIR$])
m4trace:configure.ac:12: -1- AC_SUBST([am__include])
m4trace:configure.ac:12: -1- AC_SUBST_TRACE([am__include])
m4trace:configure.ac:12: -1- m4_pattern_allow([^am__include$])
m4trace:configure.ac:12: -1- AC_SUBST([am__quote])
m4trace:configure.ac:12: -1- AC_SUBST_TRACE([am__quote])
m4trace:configure.ac:12: -1- m4_pattern_allow([^am__quote$])
m4trace:configure.ac:12: -1- AM_CONDITIONAL([AMDEP], [test "x$enable_dependency_tracking" != xno])
m4trace:configure.ac:12: -1- AC_SUBST([AMDEP_TRUE])
m4trace:configure.ac:12: -1- AC_SUBST_TRACE([AMDEP_TRUE])
m4trace:configure.ac:12: -1- m4_pattern_allow([^AMDEP_TRUE$])
m4trace:configure.ac:12: -1- AC_SUBST([AMDEP_FALSE])
m4trace:configure.ac:12: -1- AC_SUBST_TRACE([AMDEP_FALSE])
m4trace:configure.ac:12: -1- m4_pattern_allow([^AMDEP_FALSE$])
m4trace:configure.ac:12: -1- _AM_SUBST_NOTMAKE([AMDEP_TRUE])
m4trace:configure.ac:12: -1- _AM_SUBST_NOTMAKE([AMDEP_FALSE])
m4trace:configure.ac:12: -1- AC_SUBST([AMDEPBACKSLASH])
m4trace:configure.ac:12: -1- AC_SUBST_TRACE([AMDEPBACKSLASH])
m4trace:configure.ac:12: -1- m4_pattern_allow([^AMDEPBACKSLASH$])
m4trace:configure.ac:12: -1- _AM_SUBST_NOTMAKE([AMDEPBACKSLASH])
m4trace:configure.ac:12: -1- AC_SUBST([CCDEPMODE], [depmode=$am_cv_CC_dependencies_compiler_type])
m4trace:configure.ac:12: -1- AC_SUBST_TRACE([CCDEPMODE])
m4trace:configure.ac:12: -1- m4_pattern_allow([^CCDEPMODE$])
m4trace:configure.ac:12: -1- AM_CONDITIONAL([am__fastdepCC], [
  test "x$enable_dependency_tracking" != xno \
  && test "$am_cv_CC_dependencies_compiler_type" = gcc3])
m4trace:configure.ac:12: -1- AC_SUBST([am__fastdepCC_TRUE])
m4trace:configure.ac:12: -1- AC_SUBST_TRACE([am__fastdepCC_TRUE])
m4trace:configure.ac:12: -1- m4_pattern_allow([^am__fastdepCC_TRUE$])
m4trace:configure.ac:12: -1- AC_SUBST([am__fastdepCC_FALSE])
m4trace:configure.ac:12: -1- AC_SUBST_TRACE([am__fastdepCC_FALSE])
m4trace:configure.ac:12: -1- m4_pattern_allow([^am__fastdepCC_FALSE$])
m4trace:configure.ac:12: -1- _AM_SUBST_NOTMAKE([am__fastdepCC_TRUE])
m4trace:configure.ac:12: -1- _AM_SUBST_NOTMAKE([am__fastdepCC_FALSE])
m4trace:configure.ac:13: -1- AM_PROG_CC_C_O
m4trace:configure.ac:13: -1- AC_DEFINE_TRACE_LITERAL([NO_MINUS_C_MINUS_O])
m4trace:configure.ac:13: -1- m4_pattern_allow([^NO_MINUS_C_MINUS_O$])
m4trace:configure.ac:13: -1- AH_OUTPUT([NO_MINUS_C_MINUS_O], [/* Define to 1 if your C compiler doesn\'t accept -c and -o together. */
@%:@undef NO_MINUS_C_MINUS_O])
m4trace:configure.ac:13: -1- AC_REQUIRE_AUX_FILE([compile])
m4trace:configure.ac:14: -1- _m4_warn([obsolete], [The macro `AC_HELP_STRING' is obsolete.
You should run autoupdate.], [../../lib/autoconf/general.m4:207: AC_HELP_STRING is expanded from...
../../lib/autoconf/general.m4:1462: AC_ARG_ENABLE is expanded from...
aclocal.m4:2096: AC_ENABLE_STATIC is expanded from...
aclocal.m4:2125: AC_DISABLE_STATIC is expanded from...
configure.ac:14: the top level])
m4trace:configure.ac:15: -1- AC_PROG_LIBTOOL
m4trace:configure.ac:15: -1- _m4_warn([obsolete], [The macro `AC_HELP_STRING' is obsolete.
You should run autoupdate.], [../../lib/autoconf/general.m4:207: AC_HELP_STRING is expanded from...
../../lib/autoconf/general.m4:1462: AC_ARG_ENABLE is expanded from...
aclocal.m4:2057: AC_ENABLE_SHARED is expanded from...
aclocal.m4:94: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:74: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:39: AC_PROG_LIBTOOL is expanded from...
aclocal.m4:6566: AM_PROG_LIBTOOL is expanded from...
configure.ac:15: the top level])
m4trace:configure.ac:15: -1- _m4_warn([obsolete], [The macro `AC_HELP_STRING' is obsolete.
You should run autoupdate.], [../../lib/autoconf/general.m4:207: AC_HELP_STRING is expanded from...
../../lib/autoconf/general.m4:1462: AC_ARG_ENABLE is expanded from...
aclocal.m4:2135: AC_ENABLE_FAST_INSTALL is expanded from...
aclocal.m4:94: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:74: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:39: AC_PROG_LIBTOOL is expanded from...
aclocal.m4:6566: AM_PROG_LIBTOOL is expanded from...
configure.ac:15: the top level])
m4trace:configure.ac:15: -1- AC_CANONICAL_HOST
m4trace:configure.ac:15: -1- AC_CANONICAL_BUILD
m4trace:configure.ac:15: -1- AC_REQUIRE_AUX_FILE([config.sub])
m4trace:configure.ac:15: -1- AC_REQUIRE_AUX_FILE([config.guess])
m4trace:configure.ac:15: -1- AC_SUBST([build], [$ac_cv_build])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([build])
m4trace:configure.ac:15: -1- m4_pattern_allow([^build$])
m4trace:configure.ac:15: -1- AC_SUBST([build_cpu], [$[1]])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([build_cpu])
m4trace:configure.ac:15: -1- m4_pattern_allow([^build_cpu$])
m4trace:configure.ac:15: -1- AC_SUBST([build_vendor], [$[2]])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([build_vendor])
m4trace:configure.ac:15: -1- m4_pattern_allow([^build_vendor$])
m4trace:configure.ac:15: -1- AC_SUBST([build_os])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([build_os])
m4trace:configure.ac:15: -1- m4_pattern_allow([^build_os$])
m4trace:configure.ac:15: -1- AC_SUBST([host], [$ac_cv_host])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([host])
m4trace:configure.ac:15: -1- m4_pattern_allow([^host$])
m4trace:configure.ac:15: -1- AC_SUBST([host_cpu], [$[1]])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([host_cpu])
m4trace:configure.ac:15: -1- m4_pattern_allow([^host_cpu$])
m4trace:configure.ac:15: -1- AC_SUBST([host_vendor], [$[2]])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([host_vendor])
m4trace:configure.ac:15: -1- m4_pattern_allow([^host_vendor$])
m4trace:configure.ac:15: -1- AC_SUBST([host_os])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([host_os])
m4trace:configure.ac:15: -1- m4_pattern_allow([^host_os$])
m4trace:configure.ac:15: -1- _m4_warn([obsolete], [The macro `AC_HELP_STRING' is obsolete.
You should run autoupdate.], [../../lib/autoconf/general.m4:207: AC_HELP_STRING is expanded from...
../../lib/autoconf/general.m4:1481: AC_ARG_WITH is expanded from...
aclocal.m4:2276: AC_PROG_LD is expanded from...
aclocal.m4:94: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:74: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:39: AC_PROG_LIBTOOL is expanded from...
aclocal.m4:6566: AM_PROG_LIBTOOL is expanded from...
configure.ac:15: the top level])
m4trace:configure.ac:15: -1- AC_SUBST([SED])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([SED])
m4trace:configure.ac:15: -1- m4_pattern_allow([^SED$])
m4trace:configure.ac:15: -1- AC_SUBST([GREP])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([GREP])
m4trace:configure.ac:15: -1- m4_pattern_allow([^GREP$])
m4trace:configure.ac:15: -1- AC_SUBST([EGREP])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([EGREP])
m4trace:configure.ac:15: -1- m4_pattern_allow([^EGREP$])
m4trace:configure.ac:15: -1- AC_SUBST([LN_S], [$as_ln_s])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([LN_S])
m4trace:configure.ac:15: -1- m4_pattern_allow([^LN_S$])
m4trace:configure.ac:15: -1- AC_SUBST([ECHO])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([ECHO])
m4trace:configure.ac:15: -1- m4_pattern_allow([^ECHO$])
m4trace:configure.ac:15: -1- AC_SUBST([AR])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([AR])
m4trace:configure.ac:15: -1- m4_pattern_allow([^AR$])
m4trace:configure.ac:15: -1- AC_SUBST([RANLIB])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([RANLIB])
m4trace:configure.ac:15: -1- m4_pattern_allow([^RANLIB$])
m4trace:configure.ac:15: -1- AC_SUBST([STRIP])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([STRIP])
m4trace:configure.ac:15: -1- m4_pattern_allow([^STRIP$])
m4trace:configure.ac:15: -1- AC_SUBST([DSYMUTIL])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([DSYMUTIL])
m4trace:configure.ac:15: -1- m4_pattern_allow([^DSYMUTIL$])
m4trace:configure.ac:15: -1- AC_SUBST([NMEDIT])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([NMEDIT])
m4trace:configure.ac:15: -1- m4_pattern_allow([^NMEDIT$])
m4trace:configure.ac:15: -1- _m4_warn([obsolete], [The macro `AC_HELP_STRING' is obsolete.
You should run autoupdate.], [../../lib/autoconf/general.m4:207: AC_HELP_STRING is expanded from...
../../lib/autoconf/general.m4:1462: AC_ARG_ENABLE is expanded from...
aclocal.m4:94: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:74: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:39: AC_PROG_LIBTOOL is expanded from...
aclocal.m4:6566: AM_PROG_LIBTOOL is expanded from...
configure.ac:15: the top level])
m4trace:configure.ac:15: -1- _m4_warn([obsolete], [The macro `AC_HELP_STRING' is obsolete.
You should run autoupdate.], [../../lib/autoconf/general.m4:207: AC_HELP_STRING is expanded from...
../../lib/autoconf/general.m4:1481: AC_ARG_WITH is expanded from...
aclocal.m4:94: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:74: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:39: AC_PROG_LIBTOOL is expanded from...
aclocal.m4:6566: AM_PROG_LIBTOOL is expanded from...
configure.ac:15: the top level])
m4trace:configure.ac:15: -1- _m4_warn([obsolete], [The macro `AC_TRY_LINK' is obsolete.
You should run autoupdate.], [../../lib/autoconf/general.m4:2687: AC_TRY_LINK is expanded from...
../../lib/m4sugar/m4sh.m4:639: AS_IF is expanded from...
../../lib/autoconf/general.m4:2031: AC_CACHE_VAL is expanded from...
../../lib/autoconf/general.m4:2052: AC_CACHE_CHECK is expanded from...
aclocal.m4:565: _LT_AC_LOCK is expanded from...
aclocal.m4:1185: AC_LIBTOOL_SYS_HARD_LINK_LOCKS is expanded from...
aclocal.m4:2809: _LT_AC_LANG_C_CONFIG is expanded from...
aclocal.m4:2808: AC_LIBTOOL_LANG_C_CONFIG is expanded from...
aclocal.m4:94: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:74: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:39: AC_PROG_LIBTOOL is expanded from...
aclocal.m4:6566: AM_PROG_LIBTOOL is expanded from...
configure.ac:15: the top level])
m4trace:configure.ac:15: -1- _m4_warn([syntax], [AC_LANG_CONFTEST: no AC_LANG_SOURCE call detected in body], [../../lib/autoconf/lang.m4:193: AC_LANG_CONFTEST is expanded from...
../../lib/autoconf/general.m4:2661: _AC_LINK_IFELSE is expanded from...
../../lib/autoconf/general.m4:2678: AC_LINK_IFELSE is expanded from...
aclocal.m4:377: _LT_AC_SYS_LIBPATH_AIX is expanded from...
aclocal.m4:5574: AC_LIBTOOL_PROG_LD_SHLIBS is expanded from...
aclocal.m4:2809: _LT_AC_LANG_C_CONFIG is expanded from...
aclocal.m4:2808: AC_LIBTOOL_LANG_C_CONFIG is expanded from...
aclocal.m4:94: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:74: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:39: AC_PROG_LIBTOOL is expanded from...
aclocal.m4:6566: AM_PROG_LIBTOOL is expanded from...
configure.ac:15: the top level])
m4trace:configure.ac:15: -1- _m4_warn([syntax], [AC_LANG_CONFTEST: no AC_LANG_SOURCE call detected in body], [../../lib/autoconf/lang.m4:193: AC_LANG_CONFTEST is expanded from...
../../lib/autoconf/general.m4:2661: _AC_LINK_IFELSE is expanded from...
../../lib/autoconf/general.m4:2678: AC_LINK_IFELSE is expanded from...
aclocal.m4:377: _LT_AC_SYS_LIBPATH_AIX is expanded from...
aclocal.m4:5574: AC_LIBTOOL_PROG_LD_SHLIBS is expanded from...
aclocal.m4:2809: _LT_AC_LANG_C_CONFIG is expanded from...
aclocal.m4:2808: AC_LIBTOOL_LANG_C_CONFIG is expanded from...
aclocal.m4:94: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:74: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:39: AC_PROG_LIBTOOL is expanded from...
aclocal.m4:6566: AM_PROG_LIBTOOL is expanded from...
configure.ac:15: the top level])
m4trace:configure.ac:15: -1- AH_OUTPUT([HAVE_DLFCN_H], [/* Define to 1 if you have the <dlfcn.h> header file. */
@%:@undef HAVE_DLFCN_H])
m4trace:configure.ac:15: -1- AC_SUBST([CPP])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([CPP])
m4trace:configure.ac:15: -1- m4_pattern_allow([^CPP$])
m4trace:configure.ac:15: -1- AC_SUBST([CPPFLAGS])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([CPPFLAGS])
m4trace:configure.ac:15: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.ac:15: -1- AC_SUBST([CPP])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([CPP])
m4trace:configure.ac:15: -1- m4_pattern_allow([^CPP$])
m4trace:configure.ac:15: -1- AC_DEFINE_TRACE_LITERAL([STDC_HEADERS])
m4trace:configure.ac:15: -1- m4_pattern_allow([^STDC_HEADERS$])
m4trace:configure.ac:15: -1- AH_OUTPUT([STDC_HEADERS], [/* Define to 1 if you have the ANSI C header files. */
@%:@undef STDC_HEADERS])
m4trace:configure.ac:15: -1- AH_OUTPUT([HAVE_SYS_TYPES_H], [/* Define to 1 if you have the <sys/types.h> header file. */
@%:@undef HAVE_SYS_TYPES_H])
m4trace:configure.ac:15: -1- AH_OUTPUT([HAVE_SYS_STAT_H], [/* Define to 1 if you have the <sys/stat.h> header file. */
@%:@undef HAVE_SYS_STAT_H])
m4trace:configure.ac:15: -1- AH_OUTPUT([HAVE_STDLIB_H], [/* Define to 1 if you have the <stdlib.h> header file. */
@%:@undef HAVE_STDLIB_H])
m4trace:configure.ac:15: -1- AH_OUTPUT([HAVE_STRING_H], [/* Define to 1 if you have the <string.h> header file. */
@%:@undef HAVE_STRING_H])
m4trace:configure.ac:15: -1- AH_OUTPUT([HAVE_MEMORY_H], [/* Define to 1 if you have the <memory.h> header file. */
@%:@undef HAVE_MEMORY_H])
m4trace:configure.ac:15: -1- AH_OUTPUT([HAVE_STRINGS_H], [/* Define to 1 if you have the <strings.h> header file. */
@%:@undef HAVE_STRINGS_H])
m4trace:configure.ac:15: -1- AH_OUTPUT([HAVE_INTTYPES_H], [/* Define to 1 if you have the <inttypes.h> header file. */
@%:@undef HAVE_INTTYPES_H])
m4trace:configure.ac:15: -1- AH_OUTPUT([HAVE_STDINT_H], [/* Define to 1 if you have the <stdint.h> header file. */
@%:@undef HAVE_STDINT_H])
m4trace:configure.ac:15: -1- AH_OUTPUT([HAVE_UNISTD_H], [/* Define to 1 if you have the <unistd.h> header file. */
@%:@undef HAVE_UNISTD_H])
m4trace:configure.ac:15: -1- AC_DEFINE_TRACE_LITERAL([HAVE_DLFCN_H])
m4trace:configure.ac:15: -1- m4_pattern_allow([^HAVE_DLFCN_H$])
m4trace:configure.ac:15: -1- _LT_AC_TAGCONFIG
m4trace:configure.ac:15: -1- _m4_warn([obsolete], [The macro `AC_HELP_STRING' is obsolete.
You should run autoupdate.], [../../lib/autoconf/general.m4:207: AC_HELP_STRING is expanded from...
../../lib/autoconf/general.m4:1481: AC_ARG_WITH is expanded from...
aclocal.m4:1934: _LT_AC_TAGCONFIG is expanded from...
aclocal.m4:94: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:74: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:39: AC_PROG_LIBTOOL is expanded from...
aclocal.m4:6566: AM_PROG_LIBTOOL is expanded from...
configure.ac:15: the top level])
m4trace:configure.ac:15: -1- _m4_warn([obsolete], [back quotes and double quotes must not be escaped in: tag name \"$tagname\" already exists], [aclocal.m4:1934: _LT_AC_TAGCONFIG is expanded from...
aclocal.m4:94: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:74: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:39: AC_PROG_LIBTOOL is expanded from...
aclocal.m4:6566: AM_PROG_LIBTOOL is expanded from...
configure.ac:15: the top level])
m4trace:configure.ac:15: -1- AC_SUBST([CXX])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([CXX])
m4trace:configure.ac:15: -1- m4_pattern_allow([^CXX$])
m4trace:configure.ac:15: -1- AC_SUBST([CXXFLAGS])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([CXXFLAGS])
m4trace:configure.ac:15: -1- m4_pattern_allow([^CXXFLAGS$])
m4trace:configure.ac:15: -1- AC_SUBST([LDFLAGS])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([LDFLAGS])
m4trace:configure.ac:15: -1- m4_pattern_allow([^LDFLAGS$])
m4trace:configure.ac:15: -1- AC_SUBST([LIBS])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([LIBS])
m4trace:configure.ac:15: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.ac:15: -1- AC_SUBST([CPPFLAGS])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([CPPFLAGS])
m4trace:configure.ac:15: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.ac:15: -1- AC_SUBST([CXX])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([CXX])
m4trace:configure.ac:15: -1- m4_pattern_allow([^CXX$])
m4trace:configure.ac:15: -1- AC_SUBST([ac_ct_CXX])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([ac_ct_CXX])
m4trace:configure.ac:15: -1- m4_pattern_allow([^ac_ct_CXX$])
m4trace:configure.ac:15: -1- AC_SUBST([CXXDEPMODE], [depmode=$am_cv_CXX_dependencies_compiler_type])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([CXXDEPMODE])
m4trace:configure.ac:15: -1- m4_pattern_allow([^CXXDEPMODE$])
m4trace:configure.ac:15: -1- AM_CONDITIONAL([am__fastdepCXX], [
  test "x$enable_dependency_tracking" != xno \
  && test "$am_cv_CXX_dependencies_compiler_type" = gcc3])
m4trace:configure.ac:15: -1- AC_SUBST([am__fastdepCXX_TRUE])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([am__fastdepCXX_TRUE])
m4trace:configure.ac:15: -1- m4_pattern_allow([^am__fastdepCXX_TRUE$])
m4trace:configure.ac:15: -1- AC_SUBST([am__fastdepCXX_FALSE])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([am__fastdepCXX_FALSE])
m4trace:configure.ac:15: -1- m4_pattern_allow([^am__fastdepCXX_FALSE$])
m4trace:configure.ac:15: -1- _AM_SUBST_NOTMAKE([am__fastdepCXX_TRUE])
m4trace:configure.ac:15: -1- _AM_SUBST_NOTMAKE([am__fastdepCXX_FALSE])
m4trace:configure.ac:15: -1- AC_SUBST([CXXCPP])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([CXXCPP])
m4trace:configure.ac:15: -1- m4_pattern_allow([^CXXCPP$])
m4trace:configure.ac:15: -1- AC_SUBST([CPPFLAGS])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([CPPFLAGS])
m4trace:configure.ac:15: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.ac:15: -1- AC_SUBST([CXXCPP])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([CXXCPP])
m4trace:configure.ac:15: -1- m4_pattern_allow([^CXXCPP$])
m4trace:configure.ac:15: -1- _m4_warn([syntax], [AC_LANG_CONFTEST: no AC_LANG_SOURCE call detected in body], [../../lib/autoconf/lang.m4:193: AC_LANG_CONFTEST is expanded from...
../../lib/autoconf/general.m4:2661: _AC_LINK_IFELSE is expanded from...
../../lib/autoconf/general.m4:2678: AC_LINK_IFELSE is expanded from...
aclocal.m4:377: _LT_AC_SYS_LIBPATH_AIX is expanded from...
aclocal.m4:2886: _LT_AC_LANG_CXX_CONFIG is expanded from...
aclocal.m4:2885: AC_LIBTOOL_LANG_CXX_CONFIG is expanded from...
aclocal.m4:1934: _LT_AC_TAGCONFIG is expanded from...
aclocal.m4:94: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:74: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:39: AC_PROG_LIBTOOL is expanded from...
aclocal.m4:6566: AM_PROG_LIBTOOL is expanded from...
configure.ac:15: the top level])
m4trace:configure.ac:15: -1- _m4_warn([syntax], [AC_LANG_CONFTEST: no AC_LANG_SOURCE call detected in body], [../../lib/autoconf/lang.m4:193: AC_LANG_CONFTEST is expanded from...
../../lib/autoconf/general.m4:2661: _AC_LINK_IFELSE is expanded from...
../../lib/autoconf/general.m4:2678: AC_LINK_IFELSE is expanded from...
aclocal.m4:377: _LT_AC_SYS_LIBPATH_AIX is expanded from...
aclocal.m4:2886: _LT_AC_LANG_CXX_CONFIG is expanded from...
aclocal.m4:2885: AC_LIBTOOL_LANG_CXX_CONFIG is expanded from...
aclocal.m4:1934: _LT_AC_TAGCONFIG is expanded from...
aclocal.m4:94: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:74: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:39: AC_PROG_LIBTOOL is expanded from...
aclocal.m4:6566: AM_PROG_LIBTOOL is expanded from...
configure.ac:15: the top level])
m4trace:configure.ac:15: -1- AC_SUBST([F77])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([F77])
m4trace:configure.ac:15: -1- m4_pattern_allow([^F77$])
m4trace:configure.ac:15: -1- AC_SUBST([FFLAGS])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([FFLAGS])
m4trace:configure.ac:15: -1- m4_pattern_allow([^FFLAGS$])
m4trace:configure.ac:15: -1- AC_SUBST([LDFLAGS])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([LDFLAGS])
m4trace:configure.ac:15: -1- m4_pattern_allow([^LDFLAGS$])
m4trace:configure.ac:15: -1- AC_SUBST([LIBS])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([LIBS])
m4trace:configure.ac:15: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.ac:15: -1- AC_SUBST([F77])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([F77])
m4trace:configure.ac:15: -1- m4_pattern_allow([^F77$])
m4trace:configure.ac:15: -1- AC_SUBST([ac_ct_F77])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([ac_ct_F77])
m4trace:configure.ac:15: -1- m4_pattern_allow([^ac_ct_F77$])
m4trace:configure.ac:15: -1- _m4_warn([obsolete], [The macro `AC_LANG_SAVE' is obsolete.
You should run autoupdate.], [../../lib/autoconf/lang.m4:125: AC_LANG_SAVE is expanded from...
aclocal.m4:4195: _LT_AC_LANG_GCJ_CONFIG is expanded from...
aclocal.m4:4194: AC_LIBTOOL_LANG_GCJ_CONFIG is expanded from...
aclocal.m4:1934: _LT_AC_TAGCONFIG is expanded from...
aclocal.m4:94: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:74: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:39: AC_PROG_LIBTOOL is expanded from...
aclocal.m4:6566: AM_PROG_LIBTOOL is expanded from...
configure.ac:15: the top level])
m4trace:configure.ac:15: -1- _m4_warn([syntax], [AC_LANG_CONFTEST: no AC_LANG_SOURCE call detected in body], [../../lib/autoconf/lang.m4:193: AC_LANG_CONFTEST is expanded from...
../../lib/autoconf/general.m4:2661: _AC_LINK_IFELSE is expanded from...
../../lib/autoconf/general.m4:2678: AC_LINK_IFELSE is expanded from...
aclocal.m4:377: _LT_AC_SYS_LIBPATH_AIX is expanded from...
aclocal.m4:5574: AC_LIBTOOL_PROG_LD_SHLIBS is expanded from...
aclocal.m4:4195: _LT_AC_LANG_GCJ_CONFIG is expanded from...
aclocal.m4:4194: AC_LIBTOOL_LANG_GCJ_CONFIG is expanded from...
aclocal.m4:1934: _LT_AC_TAGCONFIG is expanded from...
aclocal.m4:94: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:74: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:39: AC_PROG_LIBTOOL is expanded from...
aclocal.m4:6566: AM_PROG_LIBTOOL is expanded from...
configure.ac:15: the top level])
m4trace:configure.ac:15: -1- _m4_warn([syntax], [AC_LANG_CONFTEST: no AC_LANG_SOURCE call detected in body], [../../lib/autoconf/lang.m4:193: AC_LANG_CONFTEST is expanded from...
../../lib/autoconf/general.m4:2661: _AC_LINK_IFELSE is expanded from...
../../lib/autoconf/general.m4:2678: AC_LINK_IFELSE is expanded from...
aclocal.m4:377: _LT_AC_SYS_LIBPATH_AIX is expanded from...
aclocal.m4:5574: AC_LIBTOOL_PROG_LD_SHLIBS is expanded from...
aclocal.m4:4195: _LT_AC_LANG_GCJ_CONFIG is expanded from...
aclocal.m4:4194: AC_LIBTOOL_LANG_GCJ_CONFIG is expanded from...
aclocal.m4:1934: _LT_AC_TAGCONFIG is expanded from...
aclocal.m4:94: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:74: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:39: AC_PROG_LIBTOOL is expanded from...
aclocal.m4:6566: AM_PROG_LIBTOOL is expanded from...
configure.ac:15: the top level])
m4trace:configure.ac:15: -1- _m4_warn([obsolete], [The macro `AC_LANG_RESTORE' is obsolete.
You should run autoupdate.], [../../lib/autoconf/lang.m4:134: AC_LANG_RESTORE is expanded from...
aclocal.m4:4195: _LT_AC_LANG_GCJ_CONFIG is expanded from...
aclocal.m4:4194: AC_LIBTOOL_LANG_GCJ_CONFIG is expanded from...
aclocal.m4:1934: _LT_AC_TAGCONFIG is expanded from...
aclocal.m4:94: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:74: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:39: AC_PROG_LIBTOOL is expanded from...
aclocal.m4:6566: AM_PROG_LIBTOOL is expanded from...
configure.ac:15: the top level])
m4trace:configure.ac:15: -1- _m4_warn([obsolete], [The macro `AC_LANG_SAVE' is obsolete.
You should run autoupdate.], [../../lib/autoconf/lang.m4:125: AC_LANG_SAVE is expanded from...
aclocal.m4:4251: _LT_AC_LANG_RC_CONFIG is expanded from...
aclocal.m4:4250: AC_LIBTOOL_LANG_RC_CONFIG is expanded from...
aclocal.m4:1934: _LT_AC_TAGCONFIG is expanded from...
aclocal.m4:94: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:74: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:39: AC_PROG_LIBTOOL is expanded from...
aclocal.m4:6566: AM_PROG_LIBTOOL is expanded from...
configure.ac:15: the top level])
m4trace:configure.ac:15: -1- _m4_warn([obsolete], [The macro `AC_LANG_RESTORE' is obsolete.
You should run autoupdate.], [../../lib/autoconf/lang.m4:134: AC_LANG_RESTORE is expanded from...
aclocal.m4:4251: _LT_AC_LANG_RC_CONFIG is expanded from...
aclocal.m4:4250: AC_LIBTOOL_LANG_RC_CONFIG is expanded from...
aclocal.m4:1934: _LT_AC_TAGCONFIG is expanded from...
aclocal.m4:94: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:74: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:39: AC_PROG_LIBTOOL is expanded from...
aclocal.m4:6566: AM_PROG_LIBTOOL is expanded from...
configure.ac:15: the top level])
m4trace:configure.ac:15: -1- AC_SUBST([LIBTOOL])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([LIBTOOL])
m4trace:configure.ac:15: -1- m4_pattern_allow([^LIBTOOL$])
m4trace:configure.ac:56: -1- AC_SUBST([blacklist_modules])
m4trace:configure.ac:56: -1- AC_SUBST_TRACE([blacklist_modules])
m4trace:configure.ac:56: -1- m4_pattern_allow([^blacklist_modules$])
m4trace:configure.ac:58: -1- AM_CONDITIONAL([ENABLE_STATIC], [test "$enable_static" = "yes"])
m4trace:configure.ac:58: -1- AC_SUBST([ENABLE_STATIC_TRUE])
m4trace:configure.ac:58: -1- AC_SUBST_TRACE([ENABLE_STATIC_TRUE])
m4trace:configure.ac:58: -1- m4_pattern_allow([^ENABLE_STATIC_TRUE$])
m4trace:configure.ac:58: -1- AC_SUBST([ENABLE_STATIC_FALSE])
m4trace:configure.ac:58: -1- AC_SUBST_TRACE([ENABLE_STATIC_FALSE])
m4trace:configure.ac:58: -1- m4_pattern_allow([^ENABLE_STATIC_FALSE$])
m4trace:configure.ac:58: -1- _AM_SUBST_NOTMAKE([ENABLE_STATIC_TRUE])
m4trace:configure.ac:58: -1- _AM_SUBST_NOTMAKE([ENABLE_STATIC_FALSE])
m4trace:configure.ac:59: -1- AM_CONDITIONAL([ENABLE_SHARED], [test "$enable_shared" = "yes"])
m4trace:configure.ac:59: -1- AC_SUBST([ENABLE_SHARED_TRUE])
m4trace:configure.ac:59: -1- AC_SUBST_TRACE([ENABLE_SHARED_TRUE])
m4trace:configure.ac:59: -1- m4_pattern_allow([^ENABLE_SHARED_TRUE$])
m4trace:configure.ac:59: -1- AC_SUBST([ENABLE_SHARED_FALSE])
m4trace:configure.ac:59: -1- AC_SUBST_TRACE([ENABLE_SHARED_FALSE])
m4trace:configure.ac:59: -1- m4_pattern_allow([^ENABLE_SHARED_FALSE$])
m4trace:configure.ac:59: -1- _AM_SUBST_NOTMAKE([ENABLE_SHARED_TRUE])
m4trace:configure.ac:59: -1- _AM_SUBST_NOTMAKE([ENABLE_SHARED_FALSE])
m4trace:configure.ac:60: -1- AM_CONDITIONAL([ENABLE_IPV4], [test "$enable_ipv4" = "yes"])
m4trace:configure.ac:60: -1- AC_SUBST([ENABLE_IPV4_TRUE])
m4trace:configure.ac:60: -1- AC_SUBST_TRACE([ENABLE_IPV4_TRUE])
m4trace:configure.ac:60: -1- m4_pattern_allow([^ENABLE_IPV4_TRUE$])
m4trace:configure.ac:60: -1- AC_SUBST([ENABLE_IPV4_FALSE])
m4trace:configure.ac:60: -1- AC_SUBST_TRACE([ENABLE_IPV4_FALSE])
m4trace:configure.ac:60: -1- m4_pattern_allow([^ENABLE_IPV4_FALSE$])
m4trace:configure.ac:60: -1- _AM_SUBST_NOTMAKE([ENABLE_IPV4_TRUE])
m4trace:configure.ac:60: -1- _AM_SUBST_NOTMAKE([ENABLE_IPV4_FALSE])
m4trace:configure.ac:61: -1- AM_CONDITIONAL([ENABLE_IPV6], [test "$enable_ipv6" = "yes"])
m4trace:configure.ac:61: -1- AC_SUBST([ENABLE_IPV6_TRUE])
m4trace:configure.ac:61: -1- AC_SUBST_TRACE([ENABLE_IPV6_TRUE])
m4trace:configure.ac:61: -1- m4_pattern_allow([^ENABLE_IPV6_TRUE$])
m4trace:configure.ac:61: -1- AC_SUBST([ENABLE_IPV6_FALSE])
m4trace:configure.ac:61: -1- AC_SUBST_TRACE([ENABLE_IPV6_FALSE])
m4trace:configure.ac:61: -1- m4_pattern_allow([^ENABLE_IPV6_FALSE$])
m4trace:configure.ac:61: -1- _AM_SUBST_NOTMAKE([ENABLE_IPV6_TRUE])
m4trace:configure.ac:61: -1- _AM_SUBST_NOTMAKE([ENABLE_IPV6_FALSE])
m4trace:configure.ac:62: -1- AM_CONDITIONAL([ENABLE_DEVEL], [test "$enable_devel" = "yes"])
m4trace:configure.ac:62: -1- AC_SUBST([ENABLE_DEVEL_TRUE])
m4trace:configure.ac:62: -1- AC_SUBST_TRACE([ENABLE_DEVEL_TRUE])
m4trace:configure.ac:62: -1- m4_pattern_allow([^ENABLE_DEVEL_TRUE$])
m4trace:configure.ac:62: -1- AC_SUBST([ENABLE_DEVEL_FALSE])
m4trace:configure.ac:62: -1- AC_SUBST_TRACE([ENABLE_DEVEL_FALSE])
m4trace:configure.ac:62: -1- m4_pattern_allow([^ENABLE_DEVEL_FALSE$])
m4trace:configure.ac:62: -1- _AM_SUBST_NOTMAKE([ENABLE_DEVEL_TRUE])
m4trace:configure.ac:62: -1- _AM_SUBST_NOTMAKE([ENABLE_DEVEL_FALSE])
m4trace:configure.ac:63: -1- AM_CONDITIONAL([ENABLE_LIBIPQ], [test "$enable_libipq" = "yes"])
m4trace:configure.ac:63: -1- AC_SUBST([ENABLE_LIBIPQ_TRUE])
m4trace:configure.ac:63: -1- AC_SUBST_TRACE([ENABLE_LIBIPQ_TRUE])
m4trace:configure.ac:63: -1- m4_pattern_allow([^ENABLE_LIBIPQ_TRUE$])
m4trace:configure.ac:63: -1- AC_SUBST([ENABLE_LIBIPQ_FALSE])
m4trace:configure.ac:63: -1- AC_SUBST_TRACE([ENABLE_LIBIPQ_FALSE])
m4trace:configure.ac:63: -1- m4_pattern_allow([^ENABLE_LIBIPQ_FALSE$])
m4trace:configure.ac:63: -1- _AM_SUBST_NOTMAKE([ENABLE_LIBIPQ_TRUE])
m4trace:configure.ac:63: -1- _AM_SUBST_NOTMAKE([ENABLE_LIBIPQ_FALSE])
m4trace:configure.ac:81: -1- AC_SUBST([regular_CFLAGS])
m4trace:configure.ac:81: -1- AC_SUBST_TRACE([regular_CFLAGS])
m4trace:configure.ac:81: -1- m4_pattern_allow([^regular_CFLAGS$])
m4trace:configure.ac:82: -1- AC_SUBST([kinclude_CFLAGS])
m4trace:configure.ac:82: -1- AC_SUBST_TRACE([kinclude_CFLAGS])
m4trace:configure.ac:82: -1- m4_pattern_allow([^kinclude_CFLAGS$])
m4trace:configure.ac:83: -1- AC_SUBST([kbuilddir])
m4trace:configure.ac:83: -1- AC_SUBST_TRACE([kbuilddir])
m4trace:configure.ac:83: -1- m4_pattern_allow([^kbuilddir$])
m4trace:configure.ac:84: -1- AC_SUBST([ksourcedir])
m4trace:configure.ac:84: -1- AC_SUBST_TRACE([ksourcedir])
m4trace:configure.ac:84: -1- m4_pattern_allow([^ksourcedir$])
m4trace:configure.ac:85: -1- AC_SUBST([xtlibdir])
m4trace:configure.ac:85: -1- AC_SUBST_TRACE([xtlibdir])
m4trace:configure.ac:85: -1- m4_pattern_allow([^xtlibdir$])
m4trace:configure.ac:86: -1- AC_SUBST([pkgconfigdir])
m4trace:configure.ac:86: -1- AC_SUBST_TRACE([pkgconfigdir])
m4trace:configure.ac:86: -1- m4_pattern_allow([^pkgconfigdir$])
m4trace:configure.ac:87: -1- AC_SUBST([libxtables_vcurrent])
m4trace:configure.ac:87: -1- AC_SUBST_TRACE([libxtables_vcurrent])
m4trace:configure.ac:87: -1- m4_pattern_allow([^libxtables_vcurrent$])
m4trace:configure.ac:88: -1- AC_SUBST([libxtables_vage])
m4trace:configure.ac:88: -1- AC_SUBST_TRACE([libxtables_vage])
m4trace:configure.ac:88: -1- m4_pattern_allow([^libxtables_vage$])
m4trace:configure.ac:90: -1- AC_SUBST([libxtables_vmajor])
m4trace:configure.ac:90: -1- AC_SUBST_TRACE([libxtables_vmajor])
m4trace:configure.ac:90: -1- m4_pattern_allow([^libxtables_vmajor$])
m4trace:configure.ac:92: -1- AC_CONFIG_FILES([Makefile extensions/GNUmakefile include/Makefile
	libipq/Makefile
	include/xtables.h include/iptables/internal.h libiptc.pc xtables.pc])
m4trace:configure.ac:95: -1- AC_SUBST([LIB@&t@OBJS], [$ac_libobjs])
m4trace:configure.ac:95: -1- AC_SUBST_TRACE([LIB@&t@OBJS])
m4trace:configure.ac:95: -1- m4_pattern_allow([^LIB@&t@OBJS$])
m4trace:configure.ac:95: -1- AC_SUBST([LTLIBOBJS], [$ac_ltlibobjs])
m4trace:configure.ac:95: -1- AC_SUBST_TRACE([LTLIBOBJS])
m4trace:configure.ac:95: -1- m4_pattern_allow([^LTLIBOBJS$])
m4trace:configure.ac:95: -1- AC_SUBST_TRACE([top_builddir])
m4trace:configure.ac:95: -1- AC_SUBST_TRACE([top_build_prefix])
m4trace:configure.ac:95: -1- AC_SUBST_TRACE([srcdir])
m4trace:configure.ac:95: -1- AC_SUBST_TRACE([abs_srcdir])
m4trace:configure.ac:95: -1- AC_SUBST_TRACE([top_srcdir])
m4trace:configure.ac:95: -1- AC_SUBST_TRACE([abs_top_srcdir])
m4trace:configure.ac:95: -1- AC_SUBST_TRACE([builddir])
m4trace:configure.ac:95: -1- AC_SUBST_TRACE([abs_builddir])
m4trace:configure.ac:95: -1- AC_SUBST_TRACE([abs_top_builddir])
m4trace:configure.ac:95: -1- AC_SUBST_TRACE([INSTALL])
m4trace:configure.ac:95: -1- AC_SUBST_TRACE([MKDIR_P])
