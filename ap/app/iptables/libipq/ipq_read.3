.TH IPQ_READ 3 "16 October 2001" "Linux iptables 1.2" "Linux Programmer's Manual" 
.\"
.\" $Id: ipq_read.3,v 1.2 2001/10/16 14:41:02 jamesm Exp $
.\"
.\"     Copyright (c) 2000-2001 Netfilter Core Team
.\"
.\"     This program is free software; you can redistribute it and/or modify
.\"     it under the terms of the GNU General Public License as published by
.\"     the Free Software Foundation; either version 2 of the License, or
.\"     (at your option) any later version.
.\"
.\"     This program is distributed in the hope that it will be useful,
.\"     but WITHOUT ANY WARRANTY; without even the implied warranty of
.\"     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
.\"     GNU General Public License for more details.
.\"
.\"     You should have received a copy of the GNU General Public License
.\"     along with this program; if not, write to the Free Software
.\"     Foundation, Inc., 675 Mass Ave, Cambridge, MA 02139, USA.
.\"
.\"
.SH NAME
ipq_read - read queue messages from ip_queue and read into supplied buffer
.SH SYNOPSIS
.B #include <linux/netfilter.h>
.br
.B #include <libipq.h>
.sp
.BI "ssize_t ipq_read(const struct ipq_handle *" h ", unsigned char *" buf ", size_t " len ", int " timeout ");"
.SH DESCRIPTION
The
.B ipq_read
function reads a queue message from the kernel and copies it to
the memory pointed to by 
.I buf
to a maximum length of
. IR len .
.PP
The
.I h
parameter is a context handle which must previously have been returned 
successfully from a call to
.BR ipq_create_handle .
.PP
The caller is responsible for ensuring that the memory pointed to by
.I buf
is large enough to contain
.I len
bytes.
.PP
The
.I timeout
parameter may be used to set a timeout for the operation, specified in microseconds.
This is implemented internally by the library via the
.BR select
system call.  A value of zero provides normal, backwards-compatible blocking behaviour
with no timeout.  A negative value causes the function to return immediately.
.PP
Data returned via
.I buf
should not be accessed directly.  Use the 
.BR ipq_message_type ,
.BR ipq_get_packet ", and"
.BR ipq_get_msgerr
functions to access the queue message in the buffer.
.SH RETURN VALUE
On failure, -1 is returned.
.br
On success, a non-zero positive value is returned when no timeout
value is specified.
.br
On success with a timeout value specified, zero is returned if no data
was available to read, or if a non-blocked signal was caught.  In the
latter case, the global
.B errno
value will be set to 
.BR EINTR .
.SH ERRORS
On error, a descriptive error message will be available
via the
.B ipq_errstr
function.
.SH DIAGNOSTICS
While the
.B ipq_read
function may return successfully, the queue message copied to the buffer
may itself be an error message from a higher level kernel component.  Use
.B ipq_message_type
to determine if it is an error message, and
.B ipq_get_msgerr
to access the value of the message.
.SH BUGS
None known.
.SH AUTHOR
James Morris <<EMAIL>>
.SH COPYRIGHT
Copyright (c) 2000-2001 Netfilter Core Team.
.PP
Distributed under the GNU General Public License.
.SH CREDITS
Joost Remijn implemented the timeout feature, which appeared in the 1.2.4 release of iptables.
.SH SEE ALSO
.BR iptables (8),
.BR libipq (3),
.BR select (2).

