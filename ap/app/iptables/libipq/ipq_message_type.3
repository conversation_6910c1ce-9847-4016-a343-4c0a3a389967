.TH IPQ_MESSAGE_TYPE 3 "16 October 2001" "Linux iptables 1.2" "Linux Programmer's Manual" 
.\"
.\" $Id: ipq_message_type.3,v 1.1 2000/11/20 14:13:32 jamesm Exp $
.\"
.\"     Copyright (c) 2000-2001 Netfilter Core Team
.\"
.\"     This program is free software; you can redistribute it and/or modify
.\"     it under the terms of the GNU General Public License as published by
.\"     the Free Software Foundation; either version 2 of the License, or
.\"     (at your option) any later version.
.\"
.\"     This program is distributed in the hope that it will be useful,
.\"     but WITHOUT ANY WARRANTY; without even the implied warranty of
.\"     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
.\"     GNU General Public License for more details.
.\"
.\"     You should have received a copy of the GNU General Public License
.\"     along with this program; if not, write to the Free Software
.\"     Foundation, Inc., 675 Mass Ave, Cambridge, MA 02139, USA.
.\"
.\"
.SH NAME
ipq_message_type, ipq_get_packet, ipq_getmsgerr - query queue messages
.SH SYNOPSIS
.B #include <linux/netfilter.h>
.br
.B #include <libipq.h>
.sp
.BI "int ipq_message_type(const unsigned char *" buf ");"
.br
.BI "ipq_packet_msg_t *ipq_get_packet(const unsigned char *" buf ");"
.br
.BI "int ipq_get_msgerr(const unsigned char *" buf ");"
.SH DESCRIPTION
The
.B ipq_message_type
function returns the type of queue message returned to userspace
via
.BR ipq_read .
.PP
.B ipq_message_type
should always be called following a successful call to
.B ipq_read
to determine whether the message is a packet message or an
error message. The
.I buf
parameter should be the same data obtained from
the previous call to
.BR ipq_read .
.PP
.B ipq_message_type
will return one of the following values:
.TP
.B NLMSG_ERROR
An error message generated by the Netlink transport.
.PP
.TP
.B IPQM_PACKET
A packet message containing packet metadata and optional packet payload data.
.PP
The
.B ipq_get_packet
function should be called if
.B ipq_message_type
returns
.BR IPQM_PACKET .
The
.I buf
parameter should point to the same data used for the call to
.BR ipq_message_type .
The pointer returned by
.B ipq_get_packet
points to a packet message, which is declared as follows:
.PP
.RS
.nf
typedef struct ipq_packet_msg {
	unsigned long packet_id;        /* ID of queued packet */
	unsigned long mark;             /* Netfilter mark value */
	long timestamp_sec;             /* Packet arrival time (seconds) */
	long timestamp_usec;            /* Packet arrvial time (+useconds) */
	unsigned int hook;              /* Netfilter hook we rode in on */
	char indev_name[IFNAMSIZ];      /* Name of incoming interface */
	char outdev_name[IFNAMSIZ];     /* Name of outgoing interface */
	unsigned short hw_protocol;     /* Hardware protocol (network order) */
	unsigned short hw_type;         /* Hardware type */
	unsigned char hw_addrlen;       /* Hardware address length */
	unsigned char hw_addr[8];       /* Hardware address */
	size_t data_len;                /* Length of packet data */
	unsigned char payload[0];       /* Optional packet data */
} ipq_packet_msg_t;
.fi
.RE
.PP
Each of these fields may be read by the application.  If the queue mode
is
.B IPQ_COPY_PACKET
and the
.I data_len
value is greater than zero, the packet payload contents may be accessed
in the memory following the
.B ipq_packet_msg_t
structure to a range of
.I data_len.
.PP
The
.I packet_id
field contains a packet identifier to be used when calling
.BR ipq_set_verdict .
.PP
The
.B ipq_get_msgerr
function should be called if
.B ipq_message_type
returns
.BR NLMSG_ERROR.
The
.I buf
parameter should point to the same data used for the call to
.BR ipq_message_type .
The value returned by
.B ipq_get_msgerr
is set by higher level kernel code and corresponds to standard
.B errno
values.
.SH BUGS
None known.
.SH AUTHOR
James Morris <<EMAIL>>
.SH COPYRIGHT
Copyright (c) 2000-2001 Netfilter Core Team.
.PP
Distributed under the GNU General Public License.
.SH SEE ALSO
.BR iptables (8),
.BR libipq (3).
