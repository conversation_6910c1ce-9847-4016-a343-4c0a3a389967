.TH IPQ_CREATE_HANDLE 3 "16 October 2001" "Linux iptables 1.2" "Linux Programmer's Manual" 
.\"
\" $Id: ipq_create_handle.3,v 1.2 2001/10/16 14:41:02 jamesm Exp $
.\"
.\"     Copyright (c) 2000-2001 Netfilter Core Team
.\"
.\"     This program is free software; you can redistribute it and/or modify
.\"     it under the terms of the GNU General Public License as published by
.\"     the Free Software Foundation; either version 2 of the License, or
.\"     (at your option) any later version.
.\"
.\"     This program is distributed in the hope that it will be useful,
.\"     but WITHOUT ANY WARRANTY; without even the implied warranty of
.\"     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
.\"     GNU General Public License for more details.
.\"
.\"     You should have received a copy of the GNU General Public License
.\"     along with this program; if not, write to the Free Software
.\"     Foundation, Inc., 675 Mass Ave, Cambridge, MA 02139, USA.
.\"
.\"
.SH NAME
ipq_create_handle, ipq_destroy_handle - create and destroy libipq handles.
.SH SYNOPSIS
.B #include <linux/netfilter.h>
.br
.B #include <libipq.h>
.sp
.BI "struct ipq_handle *ipq_create_handle(u_int32_t " flags ", u_int32_t " protocol ");"
.br
.BI "int ipq_destroy_handle(struct ipq_handle *" h );
.SH DESCRIPTION
The
.B ipq_create_handle
function initialises libipq for an application, attempts to bind to the
Netlink socket used by ip_queue, and returns an opaque context handle.  It
should be the first libipq function to be called by an application.  The
handle returned should be used in all subsequent library calls which 
require a handle parameter.
.PP
The
.I flags
parameter is not currently used and should be set to zero by the application
for forward compatibility.
.PP
The
.I protocol
parameter is used to specify the protocol of the packets to be queued.
Valid values are NFPROTO_IPV4 for IPv4 and NFPROTO_IPV6 for IPv6. Currently,
only one protocol may be queued at a time for a handle.
.PP
The
.B ipq_destroy_handle
function frees up resources allocated by
.BR ipq_create_handle ,
and should be used when the handle is no longer required by the application.
.SH RETURN VALUES
On success,
.B ipq_create_handle
returns a pointer to a context handle.
.br
On failure, NULL is returned.
.PP
On success,
.B ipq_destroy_handle
returns zero.
.br
On failure, -1 is returned.
.SH ERRORS
On failure, a descriptive error message will be available
via the
.B ipq_errstr
function.
.SH BUGS
None known.
.SH AUTHOR
James Morris <<EMAIL>>
.SH COPYRIGHT
Copyright (c) 2000-2001 Netfilter Core Team.
.PP
Distributed under the GNU General Public License.
.SH SEE ALSO
.BR iptables (8),
.BR libipq (3).
