.TH IPQ_SET_VERDICT 3 "16 October 2001" "Linux iptables 1.2" "Linux Programmer's Manual" 
.\"
.\" $Id: ipq_set_verdict.3,v 1.1 2000/11/20 14:13:32 jamesm Exp $
.\"
.\"     Copyright (c) 2000-2001 Netfilter Core Team
.\"
.\"     This program is free software; you can redistribute it and/or modify
.\"     it under the terms of the GNU General Public License as published by
.\"     the Free Software Foundation; either version 2 of the License, or
.\"     (at your option) any later version.
.\"
.\"     This program is distributed in the hope that it will be useful,
.\"     but WITHOUT ANY WARRANTY; without even the implied warranty of
.\"     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
.\"     GNU General Public License for more details.
.\"
.\"     You should have received a copy of the GNU General Public License
.\"     along with this program; if not, write to the Free Software
.\"     Foundation, Inc., 675 Mass Ave, Cambridge, MA 02139, USA.
.\"
.\"
.SH NAME
ipq_set_verdict - issue verdict and optionally modified packet to kernel
.SH SYNOPSIS
.B #include <linux/netfilter.h>
.br
.B #include <libipq.h>
.sp
.BI "int ipq_set_verdict(const struct ipq_handle *" h ", ipq_id_t " id ", unsigned int " verdict ", size_t " data_len ", unsigned char *" buf ");"
.SH DESCRIPTION
The
.B ipq_set_verdict
function issues a verdict on a packet previously obtained with
.BR ipq_read ,
specifing the intended disposition of the packet, and optionally
supplying a modified version of the payload data.
.PP
The
.I h
parameter is a context handle which must previously have been returned 
successfully from a call to
.BR ipq_create_handle .
.PP
The
.I id
parameter is the packet identifier obtained via
.BR ipq_get_packet .
.PP
The
.I verdict
parameter must be one of:
.TP
.B NF_ACCEPT
Accept the packet and continue traversal within the kernel.
.br
.TP
.B NF_DROP
Drop the packet.
.TP
\fBNF_QUEUE\fP
Requeue the packet.
.PP
\fBNF_STOLEN\fP and \fBNF_REPEAT\fP are kernel-internal constants and should
not be used from userspace as their exact side effects have not been
investigated.
.PP
The
.I data_len
parameter is the length of the data pointed to
by
.IR buf ,
the optional replacement payload data.
.PP
If simply setting a verdict without modifying the payload data, use zero
for
.I data_len
and NULL for
.IR buf .
.PP
The application is responsible for recalculating any packet checksums
when modifying packets.
.SH RETURN VALUE
On failure, -1 is returned.
.br
On success, a non-zero positive value is returned.
.SH ERRORS
On error, a descriptive error message will be available
via the
.B ipq_errstr
function.
.SH BUGS
None known.
.SH AUTHOR
James Morris <<EMAIL>>
.SH COPYRIGHT
Copyright (c) 2000-2001 Netfilter Core Team.
.PP
Distributed under the GNU General Public License.
.SH SEE ALSO
.BR iptables (8),
.BR libipq (3).

