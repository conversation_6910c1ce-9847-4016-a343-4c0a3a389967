.TH LIBIPQ 3 "16 October 2001" "Linux iptables 1.2" "Linux Programmer's Manual" 
.\"
.\" $Id: libipq.3,v 1.4 2001/10/16 16:58:25 jamesm Exp $
.\"
.\"     Copyright (c) 2000-2001 Netfilter Core Team
.\"
.\"     This program is free software; you can redistribute it and/or modify
.\"     it under the terms of the GNU General Public License as published by
.\"     the Free Software Foundation; either version 2 of the License, or
.\"     (at your option) any later version.
.\"
.\"     This program is distributed in the hope that it will be useful,
.\"     but WITHOUT ANY WARRANTY; without even the implied warranty of
.\"     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
.\"     GNU General Public License for more details.
.\"
.\"     You should have received a copy of the GNU General Public License
.\"     along with this program; if not, write to the Free Software
.\"     Foundation, Inc., 675 Mass Ave, Cambridge, MA 02139, USA.
.\"
.\"
.SH NAME
libipq \- iptables userspace packet queuing library.
.SH SYNOPSIS
.B #include <linux/netfilter.h>
.br
.B #include <libipq.h>
.SH DESCRIPTION
libipq is a development library for iptables userspace packet queuing.
.SS Userspace Packet Queuing
Netfilter provides a mechanism for passing packets out of the stack for
queueing to userspace, then receiving these packets back into the kernel
with a verdict specifying what to do with the packets (such as ACCEPT
or DROP).  These packets may also be modified in userspace prior to
reinjection back into the kernel.
.PP
For each supported protocol, a kernel module called a
.I queue handler
may register with Netfilter to perform the mechanics of passing
packets to and from userspace.
.PP
The standard queue handler for IPv4 is ip_queue.  It is provided as an
experimental module with 2.4 kernels, and uses a Netlink socket for
kernel/userspace communication.
.PP
Once ip_queue is loaded, IP packets may be selected with iptables
and queued for userspace processing via the QUEUE target.  For example,
running the following commands:
.PP
	# modprobe iptable_filter
.br	
	# modprobe ip_queue
.br	
	# iptables -A OUTPUT -p icmp -j QUEUE
.PP
will cause any locally generated ICMP packets (e.g. ping output) to
be sent to the ip_queue module, which will then attempt to deliver the
packets to a userspace application.  If no userspace application is waiting,
the packets will be dropped
.PP
An application may receive and process these packets via libipq.
.PP
.PP
.SS Libipq Overview
Libipq provides an API for communicating with ip_queue.  The following is
an overview of API usage, refer to individual man pages for more details
on each function.
.PP
.B Initialisation
.br
To initialise the library, call
.BR ipq_create_handle (3).
This will attempt to bind to the Netlink socket used by ip_queue and
return an opaque context handle for subsequent library calls.
.PP
.B Setting the Queue Mode
.br
.BR ipq_set_mode (3)
allows the application to specify whether packet metadata, or packet
payloads as well as metadata are copied to userspace.  It is also used to
initially notify ip_queue that an application is ready to receive queue
messages.
.PP
.B Receiving Packets from the Queue
.br
.BR ipq_read (3)
waits for queue messages to arrive from ip_queue and copies
them into a supplied buffer.
Queue messages may be
.I packet messages
or
.I error messages.
.PP
The type of packet may be determined with
.BR ipq_message_type (3).
.PP
If it's a packet message, the metadata and optional payload may be retrieved with
.BR ipq_get_packet (3).
.PP
To retrieve the value of an error message, use
.BR ipq_get_msgerr (3).
.PP
.B Issuing Verdicts on Packets
.br
To issue a verdict on a packet, and optionally return a modified version
of the packet to the kernel, call
.BR ipq_set_verdict (3).
.PP
.B Error Handling
.br
An error string corresponding to the current value of the internal error
variable
.B ipq_errno
may be obtained with
.BR ipq_errstr (3).
.PP
For simple applications, calling
.BR ipq_perror (3)
will print the same message as
.BR ipq_errstr (3),
as well as the string corresponding to the global
.B errno
value (if set) to stderr.
.PP
.B Cleaning Up
.br
To free up the Netlink socket and destroy resources associated with
the context handle, call
.BR ipq_destroy_handle (3).
.SH SUMMARY
.TP 4
.BR ipq_create_handle (3)
Initialise library, return context handle.
.TP
.BR ipq_set_mode (3)
Set the queue mode, to copy either packet metadata, or payloads
as well as metadata to userspace.
.TP
.BR ipq_read (3)
Wait for a queue message to arrive from ip_queue and read it into
a buffer.
.TP
.BR ipq_message_type (3)
Determine message type in the buffer.
.TP
.BR ipq_get_packet (3)
Retrieve a packet message from the buffer.
.TP
.BR ipq_get_msgerr (3)
Retrieve an error message from the buffer.
.TP
.BR ipq_set_verdict (3)
Set a verdict on a packet, optionally replacing its contents.
.TP
.BR ipq_errstr (3)
Return an error message corresponding to the internal ipq_errno variable.
.TP
.BR ipq_perror (3)
Helper function to print error messages to stderr.
.TP
.BR ipq_destroy_handle (3)
Destroy context handle and associated resources.
.SH EXAMPLE
The following is an example of a simple application which receives
packets and issues NF_ACCEPT verdicts on each packet.
.RS
.nf
/*
 * This code is GPL.
 */
#include <linux/netfilter.h>
#include <libipq.h>
#include <stdio.h>

#define BUFSIZE 2048 

static void die(struct ipq_handle *h)
{
	ipq_perror("passer");
	ipq_destroy_handle(h);
	exit(1);
}

int main(int argc, char **argv)
{
	int status;
	unsigned char buf[BUFSIZE];
	struct ipq_handle *h;
	
	h = ipq_create_handle(0, NFPROTO_IPV4);
	if (!h)
		die(h);
		
	status = ipq_set_mode(h, IPQ_COPY_PACKET, BUFSIZE);
	if (status < 0)
		die(h);
		
	do{
		status = ipq_read(h, buf, BUFSIZE, 0);
		if (status < 0)
			die(h);
			
		switch (ipq_message_type(buf)) {
			case NLMSG_ERROR:
				fprintf(stderr, "Received error message %d\\n",
				        ipq_get_msgerr(buf));
				break;
				
			case IPQM_PACKET: {
				ipq_packet_msg_t *m = ipq_get_packet(buf);
				
				status = ipq_set_verdict(h, m->packet_id,
				                         NF_ACCEPT, 0, NULL);
				if (status < 0)
					die(h);
				break;
			}
			
			default:
				fprintf(stderr, "Unknown message type!\\n");
				break;
		}
	} while (1);
	
	ipq_destroy_handle(h);
	return 0;
}
.RE
.fi
.PP
Pointers to more libipq application examples may be found in The
Netfilter FAQ.
.SH DIAGNOSTICS
For information about monitoring and tuning ip_queue, refer to the
Linux 2.4 Packet Filtering HOWTO.
.PP
If an application modifies a packet, it needs to also update any
checksums for the packet.  Typically, the kernel will silently discard
modified packets with invalid checksums. 
.SH SECURITY
Processes require CAP_NET_ADMIN capabilty to access the kernel ip_queue
module.  Such processes can potentially access and modify any IP packets
received, generated or forwarded by the kernel.
.SH TODO
Per-handle
.B ipq_errno
values.
.SH BUGS
Probably.
.SH AUTHOR
James Morris <<EMAIL>>
.SH COPYRIGHT
Copyright (c) 2000-2001 Netfilter Core Team.
.PP
Distributed under the GNU General Public License.
.SH CREDITS
Joost Remijn implemented the
.B ipq_read
timeout feature, which appeared in the 1.2.4 release of iptables.
.PP
Fernando Anton added support for IPv6.
.SH SEE ALSO
.BR iptables (8),
.BR ipq_create_handle (3),
.BR ipq_destroy_handle (3),
.BR ipq_errstr (3),
.BR ipq_get_msgerr (3),
.BR ipq_get_packet (3),
.BR ipq_message_type (3),
.BR ipq_perror (3),
.BR ipq_read (3),
.BR ipq_set_mode (3),
.BR ipq_set_verdict (3).
.PP
The Netfilter home page at http://netfilter.samba.org/
which has links to The Networking Concepts HOWTO, The Linux 2.4 Packet
Filtering HOWTO, The Linux 2.4 NAT HOWTO, The Netfilter Hacking HOWTO,
The Netfilter FAQ and many other useful resources.

