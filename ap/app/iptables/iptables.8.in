.TH IPTABLES 8 "" "@PACKAGE_AND_VERSION@" "@PACKAGE_AND_VERSION@"
.\"
.\" Man page written by <PERSON><PERSON> <<EMAIL>> (May 1999)
.\" It is based on ipchains page.
.\" TODO: add a word for protocol helpers (FTP, IRC, SNMP-ALG)
.\"
.\" ipchains page by <PERSON>`<PERSON>'' Russell March 1997
.\" Based on the original ipfwadm man page by <PERSON><PERSON> <<EMAIL>>
.\"
.\"	This program is free software; you can redistribute it and/or modify
.\"	it under the terms of the GNU General Public License as published by
.\"	the Free Software Foundation; either version 2 of the License, or
.\"	(at your option) any later version.
.\"
.\"	This program is distributed in the hope that it will be useful,
.\"	but WITHOUT ANY WARRANTY; without even the implied warranty of
.\"	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
.\"	GNU General Public License for more details.
.\"
.\"	You should have received a copy of the GNU General Public License
.\"	along with this program; if not, write to the Free Software
.\"	Foundation, Inc., 675 Mass Ave, Cambridge, MA 02139, USA.
.\"
.\"
.SH NAME
iptables - administration tool for IPv4 packet filtering and NAT
.SH SYNOPSIS
\fBiptables\fP [\fB\-t\fP \fItable\fP] {\fB\-A\fP|\fB\-D\fP} \fIchain\fP \fIrule-specification\fP
.PP
\fBiptables\fP [\fB\-t\fP \fItable\fP] \fB\-I\fP \fIchain\fP [\fIrulenum\fP] \fIrule-specification\fP
.PP
\fBiptables\fP [\fB\-t\fP \fItable\fP] \fB\-R\fP \fIchain rulenum rule-specification\fP
.PP
\fBiptables\fP [\fB\-t\fP \fItable\fP] \fB\-D\fP \fIchain rulenum\fP
.PP
\fBiptables\fP [\fB\-t\fP \fItable\fP] \fB\-S\fP [\fIchain\fP [\fIrulenum\fP]]
.PP
\fBiptables\fP [\fB\-t\fP \fItable\fP] {\fB\-F\fP|\fB\-L\fP|\fB\-Z\fP} [\fIchain\fP] [\fIoptions...\fP]
.PP
\fBiptables\fP [\fB\-t\fP \fItable\fP] \fB\-N\fP \fIchain\fP
.PP
\fBiptables\fP [\fB\-t\fP \fItable\fP] \fB\-X\fP [\fIchain\fP]
.PP
\fBiptables\fP [\fB\-t\fP \fItable\fP] \fB\-P\fP \fIchain target\fP
.PP
\fBiptables\fP [\fB\-t\fP \fItable\fP] \fB\-E\fP \fIold-chain-name new-chain-name\fP
.PP
rule-specification = [\fImatches...\fP] [\fItarget\fP]
.PP
match = \fB\-m\fP \fImatchname\fP [\fIper-match-options\fP]
.PP
target = \fB\-j\fP \fItargetname\fP [\fIper\-target\-options\fP]
.SH DESCRIPTION
\fBIptables\fP is used to set up, maintain, and inspect the
tables of IPv4 packet
filter rules in the Linux kernel.  Several different tables
may be defined.  Each table contains a number of built-in
chains and may also contain user-defined chains.
.PP
Each chain is a list of rules which can match a set of packets.  Each
rule specifies what to do with a packet that matches.  This is called
a `target', which may be a jump to a user-defined chain in the same
table.
.SH TARGETS
A firewall rule specifies criteria for a packet and a target.  If the
packet does not match, the next rule in the chain is the examined; if
it does match, then the next rule is specified by the value of the
target, which can be the name of a user-defined chain or one of the
special values \fBACCEPT\fP, \fBDROP\fP, \fBQUEUE\fP or \fBRETURN\fP.
.PP
\fBACCEPT\fP means to let the packet through.
\fBDROP\fP means to drop the packet on the floor.
\fBQUEUE\fP means to pass the packet to userspace.
(How the packet can be received
by a userspace process differs by the particular queue handler.  2.4.x
and 2.6.x kernels up to 2.6.13 include the \fBip_queue\fP
queue handler.  Kernels 2.6.14 and later additionally include the
\fBnfnetlink_queue\fP queue handler.  Packets with a target of QUEUE will be
sent to queue number '0' in this case. Please also see the \fBNFQUEUE\fP
target as described later in this man page.)
\fBRETURN\fP means stop traversing this chain and resume at the next
rule in the
previous (calling) chain.  If the end of a built-in chain is reached
or a rule in a built-in chain with target \fBRETURN\fP
is matched, the target specified by the chain policy determines the
fate of the packet.
.SH TABLES
There are currently three independent tables (which tables are present
at any time depends on the kernel configuration options and which
modules are present).
.TP
\fB\-t\fP, \fB\-\-table\fP \fItable\fP
This option specifies the packet matching table which the command
should operate on.  If the kernel is configured with automatic module
loading, an attempt will be made to load the appropriate module for
that table if it is not already there.

The tables are as follows:
.RS
.TP .4i
\fBfilter\fP:
This is the default table (if no \-t option is passed). It contains
the built-in chains \fBINPUT\fP (for packets destined to local sockets),
\fBFORWARD\fP (for packets being routed through the box), and
\fBOUTPUT\fP (for locally-generated packets).
.TP
\fBnat\fP:
This table is consulted when a packet that creates a new
connection is encountered.  It consists of three built-ins: \fBPREROUTING\fP
(for altering packets as soon as they come in), \fBOUTPUT\fP
(for altering locally-generated packets before routing), and \fBPOSTROUTING\fP
(for altering packets as they are about to go out).
.TP
\fBmangle\fP:
This table is used for specialized packet alteration.  Until kernel
2.4.17 it had two built-in chains: \fBPREROUTING\fP
(for altering incoming packets before routing) and \fBOUTPUT\fP
(for altering locally-generated packets before routing).
Since kernel 2.4.18, three other built-in chains are also supported:
\fBINPUT\fP (for packets coming into the box itself), \fBFORWARD\fP
(for altering packets being routed through the box), and \fBPOSTROUTING\fP
(for altering packets as they are about to go out).
.TP
\fBraw\fP:
This table is used mainly for configuring exemptions from connection
tracking in combination with the NOTRACK target.  It registers at the netfilter
hooks with higher priority and is thus called before ip_conntrack, or any other
IP tables.  It provides the following built-in chains: \fBPREROUTING\fP
(for packets arriving via any network interface) \fBOUTPUT\fP
(for packets generated by local processes)
.RE
.SH OPTIONS
The options that are recognized by
\fBiptables\fP can be divided into several different groups.
.SS COMMANDS
These options specify the desired action to perform. Only one of them
can be specified on the command line unless otherwise stated
below. For long versions of the command and option names, you
need to use only enough letters to ensure that
\fBiptables\fP can differentiate it from all other options.
.TP
\fB\-A\fP, \fB\-\-append\fP \fIchain rule-specification\fP
Append one or more rules to the end of the selected chain.
When the source and/or destination names resolve to more than one
address, a rule will be added for each possible address combination.
.TP
\fB\-D\fP, \fB\-\-delete\fP \fIchain rule-specification\fP
.ns
.TP
\fB\-D\fP, \fB\-\-delete\fP \fIchain rulenum\fP
Delete one or more rules from the selected chain.  There are two
versions of this command: the rule can be specified as a number in the
chain (starting at 1 for the first rule) or a rule to match.
.TP
\fB\-I\fP, \fB\-\-insert\fP \fIchain\fP [\fIrulenum\fP] \fIrule-specification\fP
Insert one or more rules in the selected chain as the given rule
number.  So, if the rule number is 1, the rule or rules are inserted
at the head of the chain.  This is also the default if no rule number
is specified.
.TP
\fB\-R\fP, \fB\-\-replace\fP \fIchain rulenum rule-specification\fP
Replace a rule in the selected chain.  If the source and/or
destination names resolve to multiple addresses, the command will
fail.  Rules are numbered starting at 1.
.TP
\fB\-L\fP, \fB\-\-list\fP [\fIchain\fP]
List all rules in the selected chain.  If no chain is selected, all
chains are listed. Like every other iptables command, it applies to the
specified table (filter is the default), so NAT rules get listed by
.nf
 iptables \-t nat \-n \-L
.fi
Please note that it is often used with the \fB\-n\fP
option, in order to avoid long reverse DNS lookups.
It is legal to specify the \fB\-Z\fP
(zero) option as well, in which case the chain(s) will be atomically
listed and zeroed.  The exact output is affected by the other
arguments given. The exact rules are suppressed until you use
.nf
 iptables \-L \-v
.fi
.TP
\fB\-S\fP, \fB\-\-list\-rules\fP [\fIchain\fP]
Print all rules in the selected chain.  If no chain is selected, all
chains are printed like iptables\-save. Like every other iptables command,
it applies to the specified table (filter is the default).
.TP
\fB\-F\fP, \fB\-\-flush\fP [\fIchain\fP]
Flush the selected chain (all the chains in the table if none is given).
This is equivalent to deleting all the rules one by one.
.TP
\fB\-Z\fP, \fB\-\-zero\fP [\fIchain\fP]
Zero the packet and byte counters in all chains.  It is legal to
specify the
\fB\-L\fP, \fB\-\-list\fP
(list) option as well, to see the counters immediately before they are
cleared. (See above.)
.TP
\fB\-N\fP, \fB\-\-new\-chain\fP \fIchain\fP
Create a new user-defined chain by the given name.  There must be no
target of that name already.
.TP
\fB\-X\fP, \fB\-\-delete\-chain\fP [\fIchain\fP]
Delete the optional user-defined chain specified.  There must be no references
to the chain.  If there are, you must delete or replace the referring rules
before the chain can be deleted.  The chain must be empty, i.e. not contain
any rules.  If no argument is given, it will attempt to delete every
non-builtin chain in the table.
.TP
\fB\-P\fP, \fB\-\-policy\fP \fIchain target\fP
Set the policy for the chain to the given target.  See the section \fBTARGETS\fP
for the legal targets.  Only built-in (non-user-defined) chains can have
policies, and neither built-in nor user-defined chains can be policy
targets.
.TP
\fB\-E\fP, \fB\-\-rename\-chain\fP \fIold\-chain new\-chain\fP
Rename the user specified chain to the user supplied name.  This is
cosmetic, and has no effect on the structure of the table.
.TP
\fB\-\h\fP
Help.
Give a (currently very brief) description of the command syntax.
.SS PARAMETERS
The following parameters make up a rule specification (as used in the
add, delete, insert, replace and append commands).
.TP
[\fB!\fP] \fB\-p\fP, \fB\-\-protocol\fP \fIprotocol\fP
The protocol of the rule or of the packet to check.
The specified protocol can be one of \fBtcp\fP, \fBudp\fP, \fBudplite\fP,
\fBicmp\fP, \fBesp\fP, \fBah\fP, \fBsctp\fP or \fBall\fP,
or it can be a numeric value, representing one of these protocols or a
different one.  A protocol name from /etc/protocols is also allowed.
A "!" argument before the protocol inverts the
test.  The number zero is equivalent to \fBall\fP.
Protocol \fBall\fP
will match with all protocols and is taken as default when this
option is omitted.
.TP
[\fB!\fP] \fB\-s\fP, \fB\-\-source\fP \fIaddress\fP[\fB/\fP\fImask\fP]
Source specification. \fIAddress\fP
can be either a network name, a hostname (please note that specifying
any name to be resolved with a remote query such as DNS is a really bad idea),
a network IP address (with \fB/\fP\fImask\fP), or a plain IP address.
The \fImask\fP
can be either a network mask or a plain number,
specifying the number of 1's at the left side of the network mask.
Thus, a mask of \fI24\fP is equivalent to \fI255.255.255.0\fP.
A "!" argument before the address specification inverts the sense of
the address. The flag \fB\-\-src\fP is an alias for this option.
.TP
[\fB!\fP] \fB\-d\fP, \fB\-\-destination\fP \fIaddress\fP[\fB/\fP\fImask\fP]
Destination specification. 
See the description of the \fB\-s\fP
(source) flag for a detailed description of the syntax.  The flag
\fB\-\-dst\fP is an alias for this option.
.TP
\fB\-j\fP, \fB\-\-jump\fP \fItarget\fP
This specifies the target of the rule; i.e., what to do if the packet
matches it.  The target can be a user-defined chain (other than the
one this rule is in), one of the special builtin targets which decide
the fate of the packet immediately, or an extension (see \fBEXTENSIONS\fP
below).  If this
option is omitted in a rule (and \fB\-g\fP
is not used), then matching the rule will have no
effect on the packet's fate, but the counters on the rule will be
incremented.
.TP
\fB\-g\fP, \fB\-\-goto\fP \fIchain\fP
This specifies that the processing should continue in a user
specified chain. Unlike the \-\-jump option return will not continue
processing in this chain but instead in the chain that called us via
\-\-jump.
.TP
[\fB!\fP] \fB\-i\fP, \fB\-\-in\-interface\fP \fIname\fP
Name of an interface via which a packet was received (only for
packets entering the \fBINPUT\fP, \fBFORWARD\fP and \fBPREROUTING\fP
chains).  When the "!" argument is used before the interface name, the
sense is inverted.  If the interface name ends in a "+", then any
interface which begins with this name will match.  If this option is
omitted, any interface name will match.
.TP
[\fB!\fP] \fB\-o\fP, \fB\-\-out\-interface\fP \fIname\fP
Name of an interface via which a packet is going to be sent (for packets
entering the \fBFORWARD\fP, \fBOUTPUT\fP and \fBPOSTROUTING\fP
chains).  When the "!" argument is used before the interface name, the
sense is inverted.  If the interface name ends in a "+", then any
interface which begins with this name will match.  If this option is
omitted, any interface name will match.
.TP
[\fB!\fP] \fB\-f\fP, \fB\-\-fragment\fP
This means that the rule only refers to second and further fragments
of fragmented packets.  Since there is no way to tell the source or
destination ports of such a packet (or ICMP type), such a packet will
not match any rules which specify them.  When the "!" argument
precedes the "\-f" flag, the rule will only match head fragments, or
unfragmented packets.
.TP
\fB\-c\fP, \fB\-\-set\-counters\fP \fIpackets bytes\fP
This enables the administrator to initialize the packet and byte
counters of a rule (during \fBINSERT\fP, \fBAPPEND\fP, \fBREPLACE\fP
operations).
.SS "OTHER OPTIONS"
The following additional options can be specified:
.TP
\fB\-v\fP, \fB\-\-verbose\fP
Verbose output.  This option makes the list command show the interface
name, the rule options (if any), and the TOS masks.  The packet and
byte counters are also listed, with the suffix 'K', 'M' or 'G' for
1000, 1,000,000 and 1,000,000,000 multipliers respectively (but see
the \fB\-x\fP flag to change this).
For appending, insertion, deletion and replacement, this causes
detailed information on the rule or rules to be printed.
.TP
\fB\-n\fP, \fB\-\-numeric\fP
Numeric output.
IP addresses and port numbers will be printed in numeric format.
By default, the program will try to display them as host names,
network names, or services (whenever applicable).
.TP
\fB\-x\fP, \fB\-\-exact\fP
Expand numbers.
Display the exact value of the packet and byte counters,
instead of only the rounded number in K's (multiples of 1000)
M's (multiples of 1000K) or G's (multiples of 1000M).  This option is
only relevant for the \fB\-L\fP command.
.TP
\fB\-\-line\-numbers\fP
When listing rules, add line numbers to the beginning of each rule,
corresponding to that rule's position in the chain.
.TP
\fB\-\-modprobe=\fP\fIcommand\fP
When adding or inserting rules into a chain, use \fIcommand\fP
to load any necessary modules (targets, match extensions, etc).
.SH MATCH EXTENSIONS
iptables can use extended packet matching modules.  These are loaded
in two ways: implicitly, when \fB\-p\fP or \fB\-\-protocol\fP
is specified, or with the \fB\-m\fP or \fB\-\-match\fP
options, followed by the matching module name; after these, various
extra command line options become available, depending on the specific
module.  You can specify multiple extended match modules in one line,
and you can use the \fB\-h\fP or \fB\-\-help\fP
options after the module has been specified to receive help specific
to that module.
.PP
The following are included in the base package, and most of these can
be preceded by a "\fB!\fP" to invert the sense of the match.
.\" @MATCH@
.SH TARGET EXTENSIONS
iptables can use extended target modules: the following are included
in the standard distribution.
.\" @TARGET@
.SH DIAGNOSTICS
Various error messages are printed to standard error.  The exit code
is 0 for correct functioning.  Errors which appear to be caused by
invalid or abused command line parameters cause an exit code of 2, and
other errors cause an exit code of 1.
.SH BUGS
Bugs?  What's this? ;-)
Well, you might want to have a look at http://bugzilla.netfilter.org/
.SH COMPATIBILITY WITH IPCHAINS
This \fBiptables\fP
is very similar to ipchains by Rusty Russell.  The main difference is
that the chains \fBINPUT\fP and \fBOUTPUT\fP
are only traversed for packets coming into the local host and
originating from the local host respectively.  Hence every packet only
passes through one of the three chains (except loopback traffic, which
involves both INPUT and OUTPUT chains); previously a forwarded packet
would pass through all three.
.PP
The other main difference is that \fB\-i\fP refers to the input interface;
\fB\-o\fP refers to the output interface, and both are available for packets
entering the \fBFORWARD\fP chain.
.PP
The various forms of NAT have been separated out; \fBiptables\fP
is a pure packet filter when using the default `filter' table, with
optional extension modules.  This should simplify much of the previous
confusion over the combination of IP masquerading and packet filtering
seen previously.  So the following options are handled differently:
.nf
 \-j MASQ
 \-M \-S
 \-M \-L
.fi
There are several other changes in iptables.
.SH SEE ALSO
\fBiptables\-save\fP(8),
\fBiptables\-restore\fP(8),
\fBip6tables\fP(8),
\fBip6tables\-save\fP(8),
\fBip6tables\-restore\fP(8),
\fBlibipq\fP(3).
.PP
The packet-filtering-HOWTO details iptables usage for
packet filtering, the NAT-HOWTO details NAT,
the netfilter-extensions-HOWTO details the extensions that are
not in the standard distribution,
and the netfilter-hacking-HOWTO details the netfilter internals.
.br
See
.BR "http://www.netfilter.org/" .
.SH AUTHORS
Rusty Russell originally wrote iptables, in early consultation with Michael
Neuling.
.PP
Marc Boucher made Rusty abandon ipnatctl by lobbying for a generic packet
selection framework in iptables, then wrote the mangle table, the owner match,
the mark stuff, and ran around doing cool stuff everywhere.
.PP
James Morris wrote the TOS target, and tos match.
.PP
Jozsef Kadlecsik wrote the REJECT target.
.PP
Harald Welte wrote the ULOG and NFQUEUE target, the new libiptc, as well as the TTL, DSCP, ECN matches and targets.
.PP
The Netfilter Core Team is: Marc Boucher, Martin Josefsson, Yasuyuki Kozakai,
Jozsef Kadlecsik, Patrick McHardy, James Morris, Pablo Neira Ayuso,
Harald Welte and Rusty Russell.
.PP
Man page originally written by Herve Eychenne <<EMAIL>>.
.\" .. and did I mention that we are incredibly cool people?
.\" .. sexy, too ..
.\" .. witty, charming, powerful ..
.\" .. and most of all, modest ..
