This module copies security markings from packets to connections
(if unlabeled), and from connections back to packets (also only
if unlabeled).  Typically used in conjunction with SECMARK, it is
only valid in the
.B mangle
table.
.TP
\fB\-\-save\fP
If the packet has a security marking, copy it to the connection
if the connection is not marked.
.TP
\fB\-\-restore\fP
If the packet does not have a security marking, and the connection
does, copy the security marking from the connection to the packet.

