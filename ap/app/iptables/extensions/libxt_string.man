This modules matches a given string by using some pattern matching strategy. It requires a linux kernel >= 2.6.14.
.TP
\fB\-\-algo\fP {\fBbm\fP|\fBkmp\fP}
Select the pattern matching strategy. (bm = <PERSON><PERSON>, kmp = <PERSON><PERSON><PERSON>-<PERSON><PERSON>)
.TP
\fB\-\-from\fP \fIoffset\fP
Set the offset from which it starts looking for any matching. If not passed, default is 0.
.TP
\fB\-\-to\fP \fIoffset\fP
Set the offset from which it starts looking for any matching. If not passed, default is the packet size.
.TP
[\fB!\fP] \fB\-\-string\fP \fIpattern\fP
Matches the given pattern.
.TP
[\fB!\fP] \fB\-\-hex\-string\fP \fIpattern\fP
Matches the given pattern in hex notation.
