Similar to SNAT/DNAT depending on chain: it takes a range of addresses
(`\-\-to *******\-*******') and gives a client the same
source-/destination-address for each connection.
.TP
\fB\-\-to\fP \fIipaddr\fP[\fB\-\fP\fIipaddr\fP]
Addresses to map source to. May be specified more than once for
multiple ranges.
.TP
\fB\-\-nodst\fP
Don't use the destination-ip in the calculations when selecting the
new source-ip
.TP
\fB\-\-random\fP
Port mapping will be forcibly randomized to avoid attacks based on 
port prediction (kernel >= 2.6.21).
