This allows you to match the ECN bits of the IPv4 and TCP header.  ECN is the Explicit Congestion Notification mechanism as specified in RFC3168
.TP
[\fB!\fP] \fB\-\-ecn\-tcp\-cwr\fP
This matches if the TCP ECN CWR (Congestion Window Received) bit is set.
.TP
[\fB!\fP] \fB\-\-ecn\-tcp\-ece\fP
This matches if the TCP ECN ECE (ECN Echo) bit is set.
.TP
[\fB!\fP] \fB\-\-ecn\-ip\-ect\fP \fInum\fP
This matches a particular IPv4 ECT (ECN-Capable Transport). You have to specify
a number between `0' and `3'.
