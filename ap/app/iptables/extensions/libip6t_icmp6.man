This extension can be used if `\-\-protocol ipv6\-icmp' or `\-\-protocol icmpv6' is
specified. It provides the following option:
.TP
[\fB!\fP] \fB\-\-icmpv6\-type\fP \fItype\fP[\fB/\fP\fIcode\fP]|\fItypename\fP
This allows specification of the ICMPv6 type, which can be a numeric
ICMPv6
.IR type ,
.IR type
and
.IR code ,
or one of the ICMPv6 type names shown by the command
.nf
 ip6tables \-p ipv6\-icmp \-h
.fi
