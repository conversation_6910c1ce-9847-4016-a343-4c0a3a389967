This is used to send back an error packet in response to the matched
packet: otherwise it is equivalent to 
.B DROP
so it is a terminating TARGET, ending rule traversal.
This target is only valid in the
.BR INPUT ,
.B FORWARD
and
.B OUTPUT
chains, and user-defined chains which are only called from those
chains.  The following option controls the nature of the error packet
returned:
.TP
\fB\-\-reject\-with\fP \fItype\fP
The type given can be
\fBicmp6\-no\-route\fP,
\fBno\-route\fP,
\fBicmp6\-adm\-prohibited\fP,
\fBadm\-prohibited\fP,
\fBicmp6\-addr\-unreachable\fP,
\fBaddr\-unreach\fP,
\fBicmp6\-port\-unreachable\fP or
\fBport\-unreach\fP
which return the appropriate ICMPv6 error message (\fBport\-unreach\fP is
the default). Finally, the option
\fBtcp\-reset\fP
can be used on rules which only match the TCP protocol: this causes a
TCP RST packet to be sent back.  This is mainly useful for blocking 
.I ident
(113/tcp) probes which frequently occur when sending mail to broken mail
hosts (which won't accept your mail otherwise).
\fBtcp\-reset\fP
can only be used with kernel versions 2.6.14 or later.
