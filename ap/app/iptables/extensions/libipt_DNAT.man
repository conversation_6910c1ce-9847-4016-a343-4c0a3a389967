This target is only valid in the
.B nat
table, in the
.B PREROUTING
and
.B OUTPUT
chains, and user-defined chains which are only called from those
chains.  It specifies that the destination address of the packet
should be modified (and all future packets in this connection will
also be mangled), and rules should cease being examined.  It takes one
type of option:
.TP
\fB\-\-to\-destination\fP [\fIipaddr\fP][\fB\-\fP\fIipaddr\fP][\fB:\fP\fIport\fP[\fB\-\fP\fIport\fP]]
which can specify a single new destination IP address, an inclusive
range of IP addresses, and optionally, a port range (which is only
valid if the rule also specifies
\fB\-p tcp\fP
or
\fB\-p udp\fP).
If no port range is specified, then the destination port will never be
modified. If no IP address is specified then only the destination port
will be modified.

In Kernels up to 2.6.10 you can add several \-\-to\-destination options. For
those kernels, if you specify more than one destination address, either via an
address range or multiple \-\-to\-destination options, a simple round-robin (one
after another in cycle) load balancing takes place between these addresses.
Later Kernels (>= 2.6.11-rc1) don't have the ability to NAT to multiple ranges
anymore.
.TP
\fB\-\-random\fP
If option
\fB\-\-random\fP
is used then port mapping will be randomized (kernel >= 2.6.22).
.RS
.PP
