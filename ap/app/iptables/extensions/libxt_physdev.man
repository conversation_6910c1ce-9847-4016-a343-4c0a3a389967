This module matches on the bridge port input and output devices enslaved
to a bridge device. This module is a part of the infrastructure that enables
a transparent bridging IP firewall and is only useful for kernel versions
above version 2.5.44.
.TP
[\fB!\fP] \fB\-\-physdev\-in\fP \fIname\fP
Name of a bridge port via which a packet is received (only for
packets entering the
.BR INPUT ,
.B FORWARD
and
.B PREROUTING
chains). If the interface name ends in a "+", then any
interface which begins with this name will match. If the packet didn't arrive
through a bridge device, this packet won't match this option, unless '!' is used.
.TP
[\fB!\fP] \fB\-\-physdev\-out\fP \fIname\fP
Name of a bridge port via which a packet is going to be sent (for packets
entering the
.BR FORWARD ,
.B OUTPUT
and
.B POSTROUTING
chains).  If the interface name ends in a "+", then any
interface which begins with this name will match. Note that in the
.BR nat " and " mangle
.B OUTPUT
chains one cannot match on the bridge output port, however one can in the
.B "filter OUTPUT"
chain. If the packet won't leave by a bridge device or if it is yet unknown what
the output device will be, then the packet won't match this option,
unless '!' is used.
.TP
[\fB!\fP] \fB\-\-physdev\-is\-in\fP
Matches if the packet has entered through a bridge interface.
.TP
[\fB!\fP] \fB\-\-physdev\-is\-out\fP
Matches if the packet will leave through a bridge interface.
.TP
[\fB!\fP] \fB\-\-physdev\-is\-bridged\fP
Matches if the packet is being bridged and therefore is not being routed.
This is only useful in the FORWARD and POSTROUTING chains.
