This module sets the Type of Service field in the IPv4 header (including the
'precedence' bits) or the Priority field in the IPv6 header. Note that TOS
shares the same bits as DSCP and ECN. The TOS target is only valid in the
\fBmangle\fR table.
.TP
\fB\-\-set\-tos\fP \fIvalue\fP[\fB/\fP\fImask\fP]
Zeroes out the bits given by \fImask\fR and XORs \fIvalue\fR into the
TOS/Priority field. If \fImask\fR is omitted, 0xFF is assumed.
.TP
\fB\-\-set\-tos\fP \fIsymbol\fP
You can specify a symbolic name when using the TOS target for IPv4. It implies
a mask of 0xFF. The list of recognized TOS names can be obtained by calling
iptables with \fB\-j TOS \-h\fP.
.PP
The following mnemonics are available:
.TP
\fB\-\-and\-tos\fP \fIbits\fP
Binary AND the TOS value with \fIbits\fR. (Mnemonic for \fB\-\-set\-tos
0/\fR\fIinvbits\fR, where \fIinvbits\fR is the binary negation of \fIbits\fR.)
.TP
\fB\-\-or\-tos\fP \fIbits\fP
Binary OR the TOS value with \fIbits\fR. (Mnemonic for \fB\-\-set\-tos\fP
\fIbits\fR\fB/\fR\fIbits\fR.)
.TP
\fB\-\-xor\-tos\fP \fIbits\fP
Binary XOR the TOS value with \fIbits\fR. (Mnemonic for \fB\-\-set\-tos\fP
\fIbits\fR\fB/0\fR.)
