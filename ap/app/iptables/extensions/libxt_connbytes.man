Match by how many bytes or packets a connection (or one of the two
flows constituting the connection) has transferred so far, or by
average bytes per packet.
.PP
The counters are 64-bit and are thus not expected to overflow ;)
.PP
The primary use is to detect long-lived downloads and mark them to be
scheduled using a lower priority band in traffic control.
.PP
The transferred bytes per connection can also be viewed through
`conntrack -L` and accessed via ctnetlink.
.PP
NOTE that for connections which have no accounting information, the match will
always return false. The "net.netfilter.nf_conntrack_acct" sysctl flag controls
whether \fBnew\fP connections will be byte/packet counted. Existing connection
flows will not be gaining/losing a/the accounting structure when be sysctl flag
is flipped.
.TP
[\fB!\fP] \fB\-\-connbytes\fP \fIfrom\fP[\fB:\fP\fIto\fP]
match packets from a connection whose packets/bytes/average packet
size is more than FROM and less than TO bytes/packets. if TO is
omitted only FROM check is done. "!" is used to match packets not
falling in the range.
.TP
\fB\-\-connbytes\-dir\fP {\fBoriginal\fP|\fBreply\fP|\fBboth\fP}
which packets to consider
.TP
\fB\-\-connbytes\-mode\fP {\fBpackets\fP|\fBbytes\fP|\fBavgpkt\fP}
whether to check the amount of packets, number of bytes transferred or
the average size (in bytes) of all packets received so far. Note that
when "both" is used together with "avgpkt", and data is going (mainly)
only in one direction (for example HTTP), the average packet size will
be about half of the actual data packets.
.TP
Example:
iptables .. \-m connbytes \-\-connbytes 10000:100000 \-\-connbytes\-dir both \-\-connbytes\-mode bytes ...
