These extensions can be used if `\-\-protocol tcp' is specified. It
provides the following options:
.TP
[\fB!\fP] \fB\-\-source\-port\fP,\fB\-\-sport\fP \fIport\fP[\fB:\fP\fIport\fP]
Source port or port range specification. This can either be a service
name or a port number. An inclusive range can also be specified,
using the format \fIport\fP\fB:\fP\fIport\fP.
If the first port is omitted, "0" is assumed; if the last is omitted,
"65535" is assumed.
If the second port is greater than the first they will be swapped.
The flag
\fB\-\-sport\fP
is a convenient alias for this option.
.TP
[\fB!\fP] \fB\-\-destination\-port\fP,\fB\-\-dport\fP \fIport\fP[\fB,\fP\fIport\fP]
Destination port or port range specification.  The flag
\fB\-\-dport\fP
is a convenient alias for this option.
.TP
[\fB!\fP] \fB\-\-tcp\-flags\fP \fImask\fP \fIcomp\fP
Match when the TCP flags are as specified.  The first argument \fImask\fP is the
flags which we should examine, written as a comma-separated list, and
the second argument \fIcomp\fP is a comma-separated list of flags which must be
set.  Flags are:
.BR "SYN ACK FIN RST URG PSH ALL NONE" .
Hence the command
.nf
 iptables \-A FORWARD \-p tcp \-\-tcp\-flags SYN,ACK,FIN,RST SYN
.fi
will only match packets with the SYN flag set, and the ACK, FIN and
RST flags unset.
.TP
[\fB!\fP] \fB\-\-syn\fP
Only match TCP packets with the SYN bit set and the ACK and RST  bits
cleared.  Such packets are used to request TCP connection initiation;
for example, blocking such packets coming in an interface will prevent
incoming TCP connections, but outgoing TCP connections will be
unaffected.
It is equivalent to \fB\-\-tcp\-flags SYN,RST,ACK SYN\fP.
If the "!" flag precedes the "\-\-syn", the sense of the
option is inverted.
.TP
[\fB!\fP] \fB\-\-tcp\-option\fP \fInumber\fP
Match if TCP option set.
