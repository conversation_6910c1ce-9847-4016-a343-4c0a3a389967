This target is only valid in the
.B nat
table, in the
.B POSTROUTING
chain.  It should only be used with dynamically assigned IP (dialup)
connections: if you have a static IP address, you should use the SNAT
target.  Masquerading is equivalent to specifying a mapping to the IP
address of the interface the packet is going out, but also has the
effect that connections are
.I forgotten
when the interface goes down.  This is the correct behavior when the
next dialup is unlikely to have the same interface address (and hence
any established connections are lost anyway).  It takes one option:
.TP
\fB\-\-to\-ports\fP \fIport\fP[\fB\-\fP\fIport\fP]
This specifies a range of source ports to use, overriding the default
.B SNAT
source port-selection heuristics (see above).  This is only valid
if the rule also specifies
\fB\-p tcp\fP
or
\fB\-p udp\fP.
.TP
\fB\-\-random\fP
Randomize source port mapping
If option
\fB\-\-random\fP
is used then port mapping will be randomized (kernel >= 2.6.21).
.RS
.PP
