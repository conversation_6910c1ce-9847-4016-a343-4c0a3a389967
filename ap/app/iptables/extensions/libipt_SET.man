This modules adds and/or deletes entries from IP sets which can be defined 
by ipset(8).
.TP
\fB\-\-add\-set\fP \fIsetname\fP \fIflag\fP[\fB,\fP\fIflag\fP...]
add the address(es)/port(s) of the packet to the sets
.TP
\fB\-\-del\-set\fP \fIsetname\fP \fIflag\fP[\fB,\fP\fIflag\fP...]
delete the address(es)/port(s) of the packet from the sets,
where flags are
.BR "src"
and/or
.BR "dst"
and there can be no more than six of them.
.PP
The bindings to follow must previously be defined in order to use 
multilevel adding/deleting by the SET target.
