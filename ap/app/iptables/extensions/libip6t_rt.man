Match on IPv6 routing header
.TP
[\fB!\fP] \fB\-\-rt\-type\fP \fItype\fP
Match the type (numeric).
.TP
[\fB!\fP] \fB\-\-rt\-segsleft\fP \fInum\fP[\fB:\fP\fInum\fP]
Match the `segments left' field (range).
.TP
[\fB!\fP] \fB\-\-rt\-len\fP \fIlength\fP
Match the length of this header.
.TP
\fB\-\-rt\-0\-res\fP
Match the reserved field, too (type=0)
.TP
\fB\-\-rt\-0\-addrs\fP \fIaddr\fP[\fB,\fP\fIaddr\fP...]
Match type=0 addresses (list).
.TP
\fB\-\-rt\-0\-not\-strict\fP
List of type=0 addresses is not a strict list.
