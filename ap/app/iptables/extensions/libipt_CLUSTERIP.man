This module allows you to configure a simple cluster of nodes that share
a certain IP and MAC address without an explicit load balancer in front of
them.  Connections are statically distributed between the nodes in this
cluster.
.TP
\fB\-\-new\fP
Create a new ClusterIP.  You always have to set this on the first rule
for a given ClusterIP.
.TP
\fB\-\-hashmode\fP \fImode\fP
Specify the hashing mode.  Has to be one of
\fBsourceip\fP, \fBsourceip\-sourceport\fP, \fBsourceip\-sourceport\-destport\fP.
.TP
\fB\-\-clustermac\fP \fImac\fP
Specify the ClusterIP MAC address. Has to be a link\-layer multicast address
.TP
\fB\-\-total\-nodes\fP \fInum\fP
Number of total nodes within this cluster.
.TP
\fB\-\-local\-node\fP \fInum\fP
Local node number within this cluster.
.TP
\fB\-\-hash\-init\fP \fIrnd\fP
Specify the random seed used for hash initialization.
