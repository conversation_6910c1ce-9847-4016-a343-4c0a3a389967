This module matches packets based on some statistic condition.
It supports two distinct modes settable with the 
\fB\-\-mode\fP
option.
.PP
Supported options:
.TP
\fB\-\-mode\fP \fImode\fP
Set the matching mode of the matching rule, supported modes are
.B random
and
.B nth. 
.TP
\fB\-\-probability\fP \fIp\fP
Set the probability from 0 to 1 for a packet to be randomly
matched. It works only with the
.B random
mode.
.TP
\fB\-\-every\fP \fIn\fP
Match one packet every nth packet. It works only with the
.B nth
mode (see also the 
\fB\-\-packet\fP
option).
.TP
\fB\-\-packet\fP \fIp\fP
Set the initial counter value (0 <= p <= n\-1, default 0) for the
.B nth 
mode.
