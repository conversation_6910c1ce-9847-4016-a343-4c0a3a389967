# -*- Makefile -*-

top_builddir := @top_builddir@
builddir     := @builddir@
top_srcdir  := @top_srcdir@
srcdir      := @srcdir@
ksourcedir  := @ksourcedir@
prefix      := @prefix@
exec_prefix := @exec_prefix@
libdir      := @libdir@
libexecdir  := @libexecdir@
xtlibdir    := @xtlibdir@

CC             := @CC@
CCLD           := ${CC}
CFLAGS         := @CFLAGS@
LDFLAGS        := @LDFLAGS@
regular_CFLAGS := @regular_CFLAGS@
kinclude_CFLAGS := @kinclude_CFLAGS@

AM_CFLAGS      := ${regular_CFLAGS} -I${top_builddir}/include -I${top_srcdir}/include ${kinclude_CFLAGS}
AM_DEPFLAGS     = -Wp,-MMD,$(@D)/.$(@F).d,-MT,$@

ifeq (${V},)
AM_LIBTOOL_SILENT = --silent
AM_VERBOSE_CC     = @echo "  CC      " $@;
AM_VERBOSE_CCLD   = @echo "  CCLD    " $@;
AM_VERBOSE_CXX    = @echo "  CXX     " $@;
AM_VERBOSE_CXXLD  = @echo "  CXXLD   " $@;
AM_VERBOSE_AR     = @echo "  AR      " $@;
AM_VERBOSE_GEN    = @echo "  GEN     " $@;
endif

#
#	Wildcard module list
#
pfx_build_mod := $(patsubst ${srcdir}/libxt_%.c,%,$(wildcard ${srcdir}/libxt_*.c))
@ENABLE_IPV4_TRUE@ pf4_build_mod := $(patsubst ${srcdir}/libipt_%.c,%,$(wildcard ${srcdir}/libipt_*.c))
@ENABLE_IPV6_TRUE@ pf6_build_mod := $(patsubst ${srcdir}/libip6t_%.c,%,$(wildcard ${srcdir}/libip6t_*.c))
pfx_build_mod := $(filter-out @blacklist_modules@,${pfx_build_mod})
pf4_build_mod := $(filter-out @blacklist_modules@,${pf4_build_mod})
pf6_build_mod := $(filter-out @blacklist_modules@,${pf6_build_mod})

# Only build matches/targets for the current kernel config
-include $(LINUX_DIR)/.config
lower=$(shell echo $(1) | tr [:upper:] [:lower:])
upper=$(shell echo $(1) | tr [:lower:] [:upper:])
keeplower=$(filter-out $(call upper,$(1)),$(1))
keepupper=$(filter-out $(call lower,$(1)),$(1))
TEST_CONFIG_VAL=$(patsubst $(3)_%,%,$(filter $(3)_%,$(foreach i,$(1),$(CONFIG_$(2)_$(i))_$(i))))
TEST_CONFIG_TYPE=$(call TEST_CONFIG_VAL,$(1),$(2),y) $(call TEST_CONFIG_VAL,$(1),$(2),m)
TEST_CONFIG=$(call TEST_CONFIG_TYPE,$(1),$(2)_$(3)) $(call TEST_CONFIG_TYPE,$(1),NETFILTER_XT_$(3))

PF_EXT_SLIB_BASE:=icmp
PF_EXT_SLIB_OPT:=$(filter-out $(PF_EXT_SLIB_BASE),$(pf4_build_mod))
PF_EXT_SLIB_MATCH:=$(sort $(call upper,$(call keeplower,$(PF_EXT_SLIB_OPT))))
PF_EXT_SLIB_TARGET:=$(sort $(call keepupper,$(PF_EXT_SLIB_OPT)))
pf4_build_mod:=$(PF_EXT_SLIB_BASE)
pf4_build_mod+=$(call lower,$(call TEST_CONFIG,$(PF_EXT_SLIB_MATCH),IP_NF,MATCH))
pf4_build_mod+=$(call TEST_CONFIG,$(PF_EXT_SLIB_TARGET),IP_NF,TARGET)

PF6_EXT_SLIB_BASE:=icmp6
PF6_EXT_SLIB_OPT:=$(filter-out $(PF6_EXT_SLIB_BASE),$(pf6_build_mod))
PF6_EXT_SLIB_MATCH:=$(sort $(call upper,$(call keeplower,$(PF6_EXT_SLIB_OPT))))
PF6_EXT_SLIB_TARGET:=$(sort $(call keepupper,$(PF6_EXT_SLIB_OPT)))
pf6_build_mod:=$(PF6_EXT_SLIB_BASE)
pf6_build_mod+=$(call lower,$(call TEST_CONFIG,$(PF6_EXT_SLIB_MATCH),IP6_NF,MATCH))
pf6_build_mod+=$(call TEST_CONFIG,$(PF6_EXT_SLIB_TARGET),IP6_NF,TARGET)

PFX_EXT_SLIB_BASE:=tcp udp standard
PFX_EXT_SLIB_OPT:=$(filter-out $(PFX_EXT_SLIB_BASE),$(pfx_build_mod))
PFX_EXT_SLIB_MATCH:=$(sort $(call upper,$(call keeplower,$(PFX_EXT_SLIB_OPT))))
PFX_EXT_SLIB_TARGET:=$(sort $(call keepupper,$(PFX_EXT_SLIB_OPT)))
pfx_build_mod:=$(PFX_EXT_SLIB_BASE)
pfx_build_mod+=$(call lower,$(call TEST_CONFIG,$(PFX_EXT_SLIB_MATCH),IP_NF,MATCH))
pfx_build_mod+=$(call TEST_CONFIG,$(PFX_EXT_SLIB_TARGET),IP_NF,TARGET)
ifeq ($(DO_IPV6), 1)
pfx_build_mod+=$(call lower,$(call TEST_CONFIG,$(PFX_EXT_SLIB_MATCH),IP6_NF,MATCH))
pfx_build_mod+=$(call TEST_CONFIG,$(PFX_EXT_SLIB_TARGET),IP6_NF,TARGET)
endif
pfx_build_mod:=$(sort $(pfx_build_mod))

ifneq ($(CONFIG_NETFILTER_XT_TARGET_DSCP),)
pfx_build_mod+=TOS
endif
ifneq ($(CONFIG_IP_NF_NAT)$(CONFIG_NF_NAT),)
pf4_build_mod+=SNAT DNAT
endif
ifneq ($(CONFIG_IP_NF_MATCH_AH_ESP),)
pf4_build_mod+=ah
pfx_build_mod+=esp
endif
pfx_build_mod+=webstr

pfx_objs      := $(patsubst %,libxt_%.o,${pfx_build_mod})
pf4_objs      := $(patsubst %,libipt_%.o,${pf4_build_mod})
pf6_objs      := $(patsubst %,libip6t_%.o,${pf6_build_mod})
pfx_solibs    := $(patsubst %,libxt_%.so,${pfx_build_mod})
pf4_solibs    := $(patsubst %,libipt_%.so,${pf4_build_mod})
pf6_solibs    := $(patsubst %,libip6t_%.so,${pf6_build_mod})


#
# Building blocks
#
targets := libext4.a libext6.a matches4.man matches6.man \
           targets4.man targets6.man
targets_install :=
@ENABLE_STATIC_TRUE@ libext4_objs := ${pfx_objs} ${pf4_objs}
@ENABLE_STATIC_TRUE@ libext6_objs := ${pfx_objs} ${pf6_objs}
@ENABLE_SHARED_TRUE@ targets += ${pfx_solibs} ${pf4_solibs} ${pf6_solibs}
@ENABLE_SHARED_TRUE@ targets_install += ${pfx_solibs} ${pf4_solibs} ${pf6_solibs}

.SECONDARY:

.PHONY: all install clean distclean FORCE

all: ${targets}

install: ${targets_install}
	@mkdir -p "${DESTDIR}${xtlibdir}";
	if test -n "${targets_install}"; then install -pm0755 $^ "${DESTDIR}${xtlibdir}/"; fi;

clean:
	rm -f *.o *.oo *.so *.a {matches,targets}[46].man initext4.c initext6.c;
	rm -f libipt_LED_def.c

distclean: clean
	rm -f .*.d .*.dd;

init%.o: init%.c
	${AM_VERBOSE_CC} ${CC} ${AM_DEPFLAGS} ${AM_CFLAGS} -D_INIT=$*_init ${CFLAGS} -o $@ -c $<;

-include .*.d


#
#	Shared libraries
#
lib%.so: lib%.oo
	${AM_VERBOSE_CCLD} ${CCLD} ${AM_LDFLAGS} -shared ${LDFLAGS} -o $@ $<;

lib%.oo: ${srcdir}/lib%.c
	${AM_VERBOSE_CC} ${CC} ${AM_DEPFLAGS} ${AM_CFLAGS} -D_INIT=lib$*_init -DPIC -fPIC ${CFLAGS} -o $@ -c $<;


#
#	Static bits
#
#	If static building is disabled, libext*.a will still be generated,
#	but will be empty. This is good since we can do with less case
#	handling code in the Makefiles.
#
lib%.o: ${srcdir}/lib%.c
	${AM_VERBOSE_CC} ${CC} ${AM_DEPFLAGS} ${AM_CFLAGS} -DNO_SHARED_LIBS=1 -D_INIT=lib$*_init ${CFLAGS} -o $@ -c $<;

libext4.a: initext4.o ${libext4_objs}
	${AM_VERBOSE_AR} ${AR} crs $@ $^;

libext6.a: initext6.o ${libext6_objs}
	${AM_VERBOSE_AR} ${AR} crs $@ $^;

initext_func  := $(sort $(addprefix xt_,${pfx_build_mod})) $(sort $(addprefix ipt_,${pf4_build_mod}))
initext6_func := $(sort $(addprefix xt_,${pfx_build_mod})) $(sort $(addprefix ip6t_,${pf6_build_mod}))

.initext4.dd: FORCE
	@echo "${initext_func}" >$@.tmp; \
	cmp -s $@ $@.tmp || mv $@.tmp $@; \
	rm -f $@.tmp;

.initext6.dd: FORCE
	@echo "${initext6_func}" >$@.tmp; \
	cmp -s $@ $@.tmp || mv $@.tmp $@; \
	rm -f $@.tmp;

initext4.c: .initext4.dd
	${AM_VERBOSE_GEN}
	@( \
	echo "" >$@; \
	for i in ${initext_func}; do \
		echo "extern void lib$${i}_init(void);" >>$@; \
	done; \
	echo "void init_extensions(void);" >>$@; \
	echo "void init_extensions(void)" >>$@; \
	echo "{" >>$@; \
	for i in ${initext_func}; do \
		echo  " ""lib$${i}_init();" >>$@; \
	done; \
	echo "}" >>$@; \
	);

initext6.c: .initext6.dd
	${AM_VERBOSE_GEN}
	@( \
	echo "" >$@; \
	for i in ${initext6_func}; do \
		echo "extern void lib$${i}_init(void);" >>$@; \
	done; \
	echo "void init_extensions(void);" >>$@; \
	echo "void init_extensions(void)" >>$@; \
	echo "{" >>$@; \
	for i in ${initext6_func}; do \
		echo " ""lib$${i}_init();" >>$@; \
	done; \
	echo "}" >>$@; \
	);

libipt_LED.o: libipt_LED_def.c

LEDMAN = $(LINUX_DIR)/include/linux/ledman.h
libipt_LED_def.c: libipt_LED_def.pl $(LEDMAN)
	perl $< <$(LEDMAN) >$@

#
#	Manual pages
#
ex_matches = $(sort $(shell echo $(1) | grep -Eo '\b[a-z0-9]+\b'))
ex_targets = $(sort $(shell echo $(1) | grep -Eo '\b[A-Z0-9]+\b'))
man_run    = \
	${AM_VERBOSE_GEN} \
	for ext in $(1); do \
		f="${srcdir}/libxt_$$ext.man"; \
		if [ -f "$$f" ]; then \
			echo ".SS $$ext"; \
			cat "$$f"; \
			continue; \
		fi; \
		f="${srcdir}/lib$(2)t_$$ext.man"; \
		if [ -f "$$f" ]; then \
			echo ".SS $$ext"; \
			cat "$$f"; \
			continue; \
		fi; \
	done >$@;

matches4.man: .initext4.dd $(wildcard ${srcdir}/lib*.man)
	$(call man_run,$(call ex_matches,${pfx_build_mod} ${pf4_build_mod}),ip)

matches6.man: .initext6.dd $(wildcard ${srcdir}/lib*.man)
	$(call man_run,$(call ex_matches,${pfx_build_mod} ${pf6_build_mod}),ip6)

targets4.man: .initext4.dd $(wildcard ${srcdir}/lib*.man)
	$(call man_run,$(call ex_targets,${pfx_build_mod} ${pf4_build_mod}),ip)

targets6.man: .initext6.dd $(wildcard ${srcdir}/lib*.man)
	$(call man_run,$(call ex_targets,${pfx_build_mod} ${pf6_build_mod}),ip6)
