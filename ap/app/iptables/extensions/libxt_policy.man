This modules matches the policy used by IPsec for handling a packet.
.TP
\fB\-\-dir\fP {\fBin\fP|\fBout\fP}
Used to select whether to match the policy used for decapsulation or the
policy that will be used for encapsulation.
.B in
is valid in the
.B PREROUTING, INPUT and FORWARD
chains,
.B out
is valid in the
.B POSTROUTING, OUTPUT and FORWARD
chains.
.TP
\fB\-\-pol\fP {\fBnone\fP|\fBipsec\fP}
Matches if the packet is subject to IPsec processing.
.TP
\fB\-\-strict\fP
Selects whether to match the exact policy or match if any rule of
the policy matches the given policy.
.TP
[\fB!\fP] \fB\-\-reqid\fP \fIid\fP
Matches the reqid of the policy rule. The reqid can be specified with
.B setkey(8)
using
.B unique:id
as level.
.TP
[\fB!\fP] \fB\-\-spi\fP \fIspi\fP
Matches the SPI of the SA.
.TP
[\fB!\fP] \fB\-\-proto\fP {\fBah\fP|\fBesp\fP|\fBipcomp\fP}
Matches the encapsulation protocol.
.TP
[\fB!\fP] \fB\-\-mode\fP {\fBtunnel\fP|\fBtransport\fP}
Matches the encapsulation mode.
.TP
[\fB!\fP] \fB\-\-tunnel\-src\fP \fIaddr\fP[\fB/\fP\fImask\fP]
Matches the source end-point address of a tunnel mode SA.
Only valid with \fB\-\-mode tunnel\fP.
.TP
[\fB!\fP] \fB\-\-tunnel\-dst\fP \fIaddr\fP[\fB/\fP\fImask\fP]
Matches the destination end-point address of a tunnel mode SA.
Only valid with \fB\-\-mode tunnel\fP.
.TP
\fB\-\-next\fP
Start the next element in the policy specification. Can only be used with
\fB\-\-strict\fP.
