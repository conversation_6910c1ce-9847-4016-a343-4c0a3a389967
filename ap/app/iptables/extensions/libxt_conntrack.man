This module, when combined with connection tracking, allows access to the
connection tracking state for this packet/connection.
.TP
[\fB!\fR] \fB\-\-ctstate\fP \fIstatelist\fP
\fIstatelist\fR is a comma separated list of the connection states to match.
Possible states are listed below.
.TP
[\fB!\fR] \fB\-\-ctproto\fP \fIl4proto\fP
Layer-4 protocol to match (by number or name)
.TP
[\fB!\fR] \fB\-\-ctorigsrc\fP \fIaddress\fP[\fB/\fP\fImask\fP]
.TP
[\fB!\fR] \fB\-\-ctorigdst\fP \fIaddress\fP[\fB/\fP\fImask\fP]
.TP
[\fB!\fR] \fB\-\-ctreplsrc\fP \fIaddress\fP[\fB/\fP\fImask\fP]
.TP
[\fB!\fR] \fB\-\-ctrepldst\fP \fIaddress\fP[\fB/\fP\fImask\fP]
Match against original/reply source/destination address
.TP
[\fB!\fR] \fB\-\-ctorigsrcport\fP \fIport\fP
.TP
[\fB!\fR] \fB\-\-ctorigdstport\fP \fIport\fP
.TP
[\fB!\fR] \fB\-\-ctreplsrcport\fP \fIport\fP
.TP
[\fB!\fR] \fB\-\-ctrepldstport\fP \fIport\fP
Match against original/reply source/destination port (TCP/UDP/etc.) or GRE key.
.TP
[\fB!\fR] \fB\-\-ctstatus\fP \fIstatelist\fP
\fIstatuslist\fR is a comma separated list of the connection statuses to match.
Possible statuses are listed below.
.TP
[\fB!\fR] \fB\-\-ctexpire\fP \fItime\fP[\fB:\fP\fItime\fP]
Match remaining lifetime in seconds against given value or range of values
(inclusive)
.TP
\fB\-\-ctdir\fP {\fBORIGINAL\fP|\fBREPLY\fP}
Match packets that are flowing in the specified direction. If this flag is not
specified at all, matches packets in both directions.
.PP
States for \fB\-\-ctstate\fP:
.TP
\fBINVALID\fR
meaning that the packet is associated with no known connection
.TP
\fBNEW\fR
meaning that the packet has started a new connection, or otherwise associated
with a connection which has not seen packets in both directions, and
.TP
\fBESTABLISHED\fR
meaning that the packet is associated with a connection which has seen packets
in both directions,
.TP
\fBRELATED\fR
meaning that the packet is starting a new connection, but is associated with an
existing connection, such as an FTP data transfer, or an ICMP error.
.TP
\fBSNAT\fR
A virtual state, matching if the original source address differs from the reply
destination.
.TP
\fBDNAT\fR
A virtual state, matching if the original destination differs from the reply
source.
.PP
Statuses for \fB\-\-ctstatus\fP:
.TP
\fBNONE\fR
None of the below.
.TP
\fBEXPECTED\fR
This is an expected connection (i.e. a conntrack helper set it up)
.TP
\fBSEEN_REPLY\fR
Conntrack has seen packets in both directions.
.TP
\fBASSURED\fR
Conntrack entry should never be early-expired.
.TP
\fBCONFIRMED\fR
Connection is confirmed: originating packet has left box.
