This module matches a set of source or destination ports.  Up to 15
ports can be specified.  A port range (port:port) counts as two
ports.  It can only be used in conjunction with
\fB\-p tcp\fP
or
\fB\-p udp\fP.
.TP
[\fB!\fP] \fB\-\-source\-ports\fP,\fB\-\-sports\fP \fIport\fP[\fB,\fP\fIport\fP|\fB,\fP\fIport\fP\fB:\fP\fIport\fP]...
Match if the source port is one of the given ports.  The flag
\fB\-\-sports\fP
is a convenient alias for this option. Multiple ports or port ranges are
separated using a comma, and a port range is specified using a colon.
\fB53,1024:65535\fP would therefore match ports 53 and all from 1024 through
65535.
.TP
[\fB!\fP] \fB\-\-destination\-ports\fP,\fB\-\-dports\fP \fIport\fP[\fB,\fP\fIport\fP|\fB,\fP\fIport\fP\fB:\fP\fIport\fP]...
Match if the destination port is one of the given ports.  The flag
\fB\-\-dports\fP
is a convenient alias for this option.
.TP
[\fB!\fP] \fB\-\-ports\fP \fIport\fP[\fB,\fP\fIport\fP|\fB,\fP\fIport\fP\fB:\fP\fIport\fP]...
Match if either the source or destination ports are equal to one of
the given ports.
