This target is only valid in the
.B nat
table, in the
.B PREROUTING
and
.B OUTPUT
chains, and user-defined chains which are only called from those
chains.  It redirects the packet to the machine itself by changing the
destination IP to the primary address of the incoming interface
(locally-generated packets are mapped to the 127.0.0.1 address).
.TP
\fB\-\-to\-ports\fP \fIport\fP[\fB\-\fP\fIport\fP]
This specifies a destination port or range of ports to use: without
this, the destination port is never altered.  This is only valid
if the rule also specifies
\fB\-p tcp\fP
or
\fB\-p udp\fP.
.TP
\fB\-\-random\fP
If option
\fB\-\-random\fP
is used then port mapping will be randomized (kernel >= 2.6.22).
.RS
.PP
