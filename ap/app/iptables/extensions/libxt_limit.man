This module matches at a limited rate using a token bucket filter.
A rule using this extension will match until this limit is reached
(unless the `!' flag is used).  It can be used in combination with the
.B LOG
target to give limited logging, for example.
.TP
[\fB!\fP] \fB\-\-limit\fP \fIrate\fP[\fB/second\fP|\fB/minute\fP|\fB/hour\fP|\fB/day\fP]
Maximum average matching rate: specified as a number, with an optional
`/second', `/minute', `/hour', or `/day' suffix; the default is
3/hour.
.TP
\fB\-\-limit\-burst\fP \fInumber\fP
Maximum initial number of packets to match: this number gets
recharged by one every time the limit specified above is not reached,
up to this number; the default is 5.
