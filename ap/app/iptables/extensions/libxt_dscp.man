This module matches the 6 bit DSCP field within the TOS field in the
IP header.  DSCP has superseded TOS within the IETF.
.TP
[\fB!\fP] \fB\-\-dscp\fP \fIvalue\fP
Match against a numeric (decimal or hex) value [0-63].
.TP
[\fB!\fP] \fB\-\-dscp\-class\fP \fIclass\fP
Match the DiffServ class. This value may be any of the
BE, EF, AFxx or CSx classes.  It will then be converted
into its according numeric value.
