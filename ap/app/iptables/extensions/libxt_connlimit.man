Allows you to restrict the number of parallel connections to a server per
client IP address (or client address block).
.TP
[\fB!\fP] \fB\-\-connlimit\-above\fP \fIn\fP
Match if the number of existing connections is (not) above \fIn\fR.
.TP
\fB\-\-connlimit\-mask\fP \fIprefix_length\fP
Group hosts using the prefix length. For IPv4, this must be a number between
(including) 0 and 32. For IPv6, between 0 and 128.
.P
Examples:
.TP
# allow 2 telnet connections per client host
iptables \-A INPUT \-p tcp \-\-syn \-\-dport 23 \-m connlimit \-\-connlimit\-above 2 \-j REJECT
.TP
# you can also match the other way around:
iptables \-A INPUT \-p tcp \-\-syn \-\-dport 23 \-m connlimit ! \-\-connlimit\-above 2 \-j ACCEPT
.TP
# limit the number of parallel HTTP requests to 16 per class C sized \
network (24 bit netmask)
iptables \-p tcp \-\-syn \-\-dport 80 \-m connlimit \-\-connlimit\-above 16
\-\-connlimit\-mask 24 \-j REJECT
.TP
# limit the number of parallel HTTP requests to 16 for the link local network \
(ipv6)
ip6tables \-p tcp \-\-syn \-\-dport 80 \-s fe80::/64 \-m connlimit \-\-connlimit\-above
16 \-\-connlimit\-mask 64 \-j REJECT
