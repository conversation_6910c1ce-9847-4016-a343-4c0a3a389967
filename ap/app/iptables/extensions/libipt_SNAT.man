This target is only valid in the
.B nat
table, in the
.B POSTROUTING
chain.  It specifies that the source address of the packet should be
modified (and all future packets in this connection will also be
mangled), and rules should cease being examined.  It takes one type
of option:
.TP
\fB\-\-to\-source\fP \fIipaddr\fP[\fB\-\fP\fIipaddr\fP][\fB:\fP\fIport\fP[\fB\-\fP\fIport\fP]]
which can specify a single new source IP address, an inclusive range
of IP addresses, and optionally, a port range (which is only valid if
the rule also specifies
\fB\-p tcp\fP
or
\fB\-p udp\fP).
If no port range is specified, then source ports below 512 will be
mapped to other ports below 512: those between 512 and 1023 inclusive
will be mapped to ports below 1024, and other ports will be mapped to
1024 or above. Where possible, no port alteration will

In Kernels up to 2.6.10, you can add several \-\-to\-source options. For those
kernels, if you specify more than one source address, either via an address
range or multiple \-\-to\-source options, a simple round-robin (one after another
in cycle) takes place between these addresses.
Later Kernels (>= 2.6.11-rc1) don't have the ability to NAT to multiple ranges
anymore.
.TP
\fB\-\-random\fP
If option
\fB\-\-random\fP
is used then port mapping will be randomized (kernel >= 2.6.21).
.RS
.PP
