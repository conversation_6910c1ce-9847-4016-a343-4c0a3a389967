This module matches the 8-bit Type of Service field in the IPv4 header (i.e.
including the "Precedence" bits) or the (also 8-bit) Priority field in the IPv6
header.
.TP
[\fB!\fP] \fB\-\-tos\fP \fIvalue\fP[\fB/\fP\fImask\fP]
Matches packets with the given TOS mark value. If a mask is specified, it is
logically ANDed with the TOS mark before the comparison.
.TP
[\fB!\fP] \fB\-\-tos\fP \fIsymbol\fP
You can specify a symbolic name when using the tos match for IPv4. The list of
recognized TOS names can be obtained by calling iptables with \fB\-m tos \-h\fP.
Note that this implies a mask of 0x3F, i.e. all but the ECN bits.
