# Makefile.in generated by automake 1.10.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994, 1995, 1996, 1997, 1998, 1999, 2000, 2001, 2002,
# 2003, 2004, 2005, 2006, 2007, 2008  Free Software Foundation, Inc.
# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

# -*- Makefile -*-



VPATH = @srcdir@
pkgdatadir = $(datadir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
@ENABLE_DEVEL_TRUE@am__append_1 = include
@ENABLE_LIBIPQ_TRUE@am__append_2 = libipq
bin_PROGRAMS = $(am__EXEEXT_1)
sbin_PROGRAMS = $(am__EXEEXT_2) $(am__EXEEXT_3) $(am__EXEEXT_4) \
	$(am__EXEEXT_5)
noinst_PROGRAMS =
@ENABLE_IPV4_TRUE@@ENABLE_STATIC_TRUE@am__append_3 = iptables-static
@ENABLE_IPV6_TRUE@@ENABLE_STATIC_TRUE@am__append_4 = ip6tables-static
@ENABLE_IPV4_TRUE@@ENABLE_SHARED_TRUE@am__append_5 = iptables iptables-multi iptables-restore iptables-save
@ENABLE_IPV6_TRUE@@ENABLE_SHARED_TRUE@am__append_6 = ip6tables ip6tables-multi ip6tables-restore ip6tables-save
@ENABLE_SHARED_TRUE@am__append_7 = iptables-xml
subdir = .
DIST_COMMON = $(am__configure_deps) $(srcdir)/Makefile.am \
	$(srcdir)/Makefile.in $(srcdir)/config.h.in \
	$(srcdir)/libiptc.pc.in $(srcdir)/xtables.pc.in \
	$(top_srcdir)/configure \
	$(top_srcdir)/extensions/GNUmakefile.in \
	$(top_srcdir)/include/iptables/internal.h.in COPYING INSTALL \
	compile config.guess config.sub depcomp install-sh ltmain.sh \
	missing
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
am__CONFIG_DISTCLEAN_FILES = config.status config.cache config.log \
 configure.lineno config.status.lineno
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = config.h
CONFIG_CLEAN_FILES = extensions/GNUmakefile \
	include/iptables/internal.h libiptc.pc xtables.pc
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = `echo $$p | sed -e 's|^.*/||'`;
am__installdirs = "$(DESTDIR)$(libdir)" "$(DESTDIR)$(bindir)" \
	"$(DESTDIR)$(sbindir)" "$(DESTDIR)$(man8dir)" \
	"$(DESTDIR)$(pkgconfigdir)"
libLTLIBRARIES_INSTALL = $(INSTALL)
LTLIBRARIES = $(lib_LTLIBRARIES)
libiptc_libiptc_la_LIBADD =
am__dirstamp = $(am__leading_dot)dirstamp
am_libiptc_libiptc_la_OBJECTS = libiptc/libip4tc.lo \
	libiptc/libip6tc.lo
libiptc_libiptc_la_OBJECTS = $(am_libiptc_libiptc_la_OBJECTS)
libiptc_libiptc_la_LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(libiptc_libiptc_la_LDFLAGS) $(LDFLAGS) -o $@
libxtables_la_DEPENDENCIES =
am_libxtables_la_OBJECTS = xtables.lo
libxtables_la_OBJECTS = $(am_libxtables_la_OBJECTS)
libxtables_la_LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(libxtables_la_LDFLAGS) $(LDFLAGS) -o $@
@ENABLE_SHARED_TRUE@am__EXEEXT_1 = iptables-xml$(EXEEXT)
binPROGRAMS_INSTALL = $(INSTALL_PROGRAM)
@ENABLE_IPV4_TRUE@@ENABLE_STATIC_TRUE@am__EXEEXT_2 = iptables-static$(EXEEXT)
@ENABLE_IPV6_TRUE@@ENABLE_STATIC_TRUE@am__EXEEXT_3 = ip6tables-static$(EXEEXT)
@ENABLE_IPV4_TRUE@@ENABLE_SHARED_TRUE@am__EXEEXT_4 =  \
@ENABLE_IPV4_TRUE@@ENABLE_SHARED_TRUE@	iptables$(EXEEXT) \
@ENABLE_IPV4_TRUE@@ENABLE_SHARED_TRUE@	iptables-multi$(EXEEXT) \
@ENABLE_IPV4_TRUE@@ENABLE_SHARED_TRUE@	iptables-restore$(EXEEXT) \
@ENABLE_IPV4_TRUE@@ENABLE_SHARED_TRUE@	iptables-save$(EXEEXT)
@ENABLE_IPV6_TRUE@@ENABLE_SHARED_TRUE@am__EXEEXT_5 =  \
@ENABLE_IPV6_TRUE@@ENABLE_SHARED_TRUE@	ip6tables$(EXEEXT) \
@ENABLE_IPV6_TRUE@@ENABLE_SHARED_TRUE@	ip6tables-multi$(EXEEXT) \
@ENABLE_IPV6_TRUE@@ENABLE_SHARED_TRUE@	ip6tables-restore$(EXEEXT) \
@ENABLE_IPV6_TRUE@@ENABLE_SHARED_TRUE@	ip6tables-save$(EXEEXT)
sbinPROGRAMS_INSTALL = $(INSTALL_PROGRAM)
PROGRAMS = $(bin_PROGRAMS) $(noinst_PROGRAMS) $(sbin_PROGRAMS)
am_ip6tables_OBJECTS = ip6tables-standalone.$(OBJEXT) \
	ip6tables.$(OBJEXT)
ip6tables_OBJECTS = $(am_ip6tables_OBJECTS)
ip6tables_DEPENDENCIES = libiptc/libiptc.la extensions/libext6.a \
	libxtables.la
ip6tables_LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(ip6tables_LDFLAGS) $(LDFLAGS) -o $@
am_ip6tables_multi_OBJECTS =  \
	ip6tables_multi-ip6tables-multi.$(OBJEXT) \
	ip6tables_multi-ip6tables-save.$(OBJEXT) \
	ip6tables_multi-ip6tables-restore.$(OBJEXT) \
	ip6tables_multi-ip6tables-standalone.$(OBJEXT) \
	ip6tables_multi-ip6tables.$(OBJEXT)
ip6tables_multi_OBJECTS = $(am_ip6tables_multi_OBJECTS)
am__DEPENDENCIES_1 = libiptc/libiptc.la extensions/libext6.a \
	libxtables.la
ip6tables_multi_DEPENDENCIES = $(am__DEPENDENCIES_1)
ip6tables_multi_LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(ip6tables_multi_CFLAGS) \
	$(CFLAGS) $(ip6tables_multi_LDFLAGS) $(LDFLAGS) -o $@
am_ip6tables_restore_OBJECTS = ip6tables-restore.$(OBJEXT) \
	ip6tables.$(OBJEXT)
ip6tables_restore_OBJECTS = $(am_ip6tables_restore_OBJECTS)
ip6tables_restore_DEPENDENCIES = $(am__DEPENDENCIES_1)
ip6tables_restore_LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(ip6tables_restore_LDFLAGS) $(LDFLAGS) -o $@
am_ip6tables_save_OBJECTS = ip6tables-save.$(OBJEXT) \
	ip6tables.$(OBJEXT)
ip6tables_save_OBJECTS = $(am_ip6tables_save_OBJECTS)
ip6tables_save_DEPENDENCIES = $(am__DEPENDENCIES_1)
ip6tables_save_LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(ip6tables_save_LDFLAGS) $(LDFLAGS) -o $@
am__objects_1 = ip6tables_static-ip6tables-multi.$(OBJEXT) \
	ip6tables_static-ip6tables-save.$(OBJEXT) \
	ip6tables_static-ip6tables-restore.$(OBJEXT) \
	ip6tables_static-ip6tables-standalone.$(OBJEXT) \
	ip6tables_static-ip6tables.$(OBJEXT)
am_ip6tables_static_OBJECTS = $(am__objects_1) \
	ip6tables_static-xtables.$(OBJEXT)
ip6tables_static_OBJECTS = $(am_ip6tables_static_OBJECTS)
ip6tables_static_DEPENDENCIES = libiptc/libiptc.la \
	extensions/libext6.a
ip6tables_static_LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(ip6tables_static_CFLAGS) \
	$(CFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
am_iptables_OBJECTS = iptables-standalone.$(OBJEXT) iptables.$(OBJEXT)
iptables_OBJECTS = $(am_iptables_OBJECTS)
iptables_DEPENDENCIES = libiptc/libiptc.la extensions/libext4.a \
	libxtables.la
iptables_LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) \
	--mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) $(iptables_LDFLAGS) \
	$(LDFLAGS) -o $@
am_iptables_multi_OBJECTS = iptables_multi-iptables-multi.$(OBJEXT) \
	iptables_multi-iptables-save.$(OBJEXT) \
	iptables_multi-iptables-restore.$(OBJEXT) \
	iptables_multi-iptables-xml.$(OBJEXT) \
	iptables_multi-iptables-standalone.$(OBJEXT) \
	iptables_multi-iptables.$(OBJEXT)
iptables_multi_OBJECTS = $(am_iptables_multi_OBJECTS)
am__DEPENDENCIES_2 = libiptc/libiptc.la extensions/libext4.a \
	libxtables.la
iptables_multi_DEPENDENCIES = $(am__DEPENDENCIES_2)
iptables_multi_LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(iptables_multi_CFLAGS) \
	$(CFLAGS) $(iptables_multi_LDFLAGS) $(LDFLAGS) -o $@
am_iptables_restore_OBJECTS = iptables-restore.$(OBJEXT) \
	iptables.$(OBJEXT)
iptables_restore_OBJECTS = $(am_iptables_restore_OBJECTS)
iptables_restore_DEPENDENCIES = $(am__DEPENDENCIES_2)
iptables_restore_LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(iptables_restore_LDFLAGS) $(LDFLAGS) -o $@
am_iptables_save_OBJECTS = iptables-save.$(OBJEXT) iptables.$(OBJEXT)
iptables_save_OBJECTS = $(am_iptables_save_OBJECTS)
iptables_save_DEPENDENCIES = $(am__DEPENDENCIES_2)
iptables_save_LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(iptables_save_LDFLAGS) $(LDFLAGS) -o $@
am__objects_2 = iptables_static-iptables-multi.$(OBJEXT) \
	iptables_static-iptables-save.$(OBJEXT) \
	iptables_static-iptables-restore.$(OBJEXT) \
	iptables_static-iptables-xml.$(OBJEXT) \
	iptables_static-iptables-standalone.$(OBJEXT) \
	iptables_static-iptables.$(OBJEXT)
am_iptables_static_OBJECTS = $(am__objects_2) \
	iptables_static-xtables.$(OBJEXT)
iptables_static_OBJECTS = $(am_iptables_static_OBJECTS)
iptables_static_DEPENDENCIES = libiptc/libiptc.la extensions/libext4.a
iptables_static_LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(iptables_static_CFLAGS) \
	$(CFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
am_iptables_xml_OBJECTS = iptables-xml.$(OBJEXT)
iptables_xml_OBJECTS = $(am_iptables_xml_OBJECTS)
iptables_xml_DEPENDENCIES = libxtables.la
DEFAULT_INCLUDES = -I.@am__isrc@
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__depfiles_maybe = depfiles
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) \
	--mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
CCLD = $(CC)
LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) \
	--mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) $(AM_LDFLAGS) \
	$(LDFLAGS) -o $@
SOURCES = $(libiptc_libiptc_la_SOURCES) $(libxtables_la_SOURCES) \
	$(ip6tables_SOURCES) $(ip6tables_multi_SOURCES) \
	$(ip6tables_restore_SOURCES) $(ip6tables_save_SOURCES) \
	$(ip6tables_static_SOURCES) $(iptables_SOURCES) \
	$(iptables_multi_SOURCES) $(iptables_restore_SOURCES) \
	$(iptables_save_SOURCES) $(iptables_static_SOURCES) \
	$(iptables_xml_SOURCES)
DIST_SOURCES = $(libiptc_libiptc_la_SOURCES) $(libxtables_la_SOURCES) \
	$(ip6tables_SOURCES) $(ip6tables_multi_SOURCES) \
	$(ip6tables_restore_SOURCES) $(ip6tables_save_SOURCES) \
	$(ip6tables_static_SOURCES) $(iptables_SOURCES) \
	$(iptables_multi_SOURCES) $(iptables_restore_SOURCES) \
	$(iptables_save_SOURCES) $(iptables_static_SOURCES) \
	$(iptables_xml_SOURCES)
RECURSIVE_TARGETS = all-recursive check-recursive dvi-recursive \
	html-recursive info-recursive install-data-recursive \
	install-dvi-recursive install-exec-recursive \
	install-html-recursive install-info-recursive \
	install-pdf-recursive install-ps-recursive install-recursive \
	installcheck-recursive installdirs-recursive pdf-recursive \
	ps-recursive uninstall-recursive
man8dir = $(mandir)/man8
NROFF = nroff
MANS = $(man_MANS)
pkgconfigDATA_INSTALL = $(INSTALL_DATA)
DATA = $(pkgconfig_DATA)
RECURSIVE_CLEAN_TARGETS = mostlyclean-recursive clean-recursive	\
  distclean-recursive maintainer-clean-recursive
ETAGS = etags
CTAGS = ctags
DIST_SUBDIRS = extensions include libipq
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
distdir = $(PACKAGE)-$(VERSION)
top_distdir = $(distdir)
am__remove_distdir = \
  { test ! -d $(distdir) \
    || { find $(distdir) -type d ! -perm -200 -exec chmod u+w {} ';' \
         && rm -fr $(distdir); }; }
DIST_ARCHIVES = $(distdir).tar.gz
GZIP_ENV = --best
distuninstallcheck_listfiles = find . -type f -print
distcleancheck_listfiles = find . -type f -print
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CXX = @CXX@
CXXCPP = @CXXCPP@
CXXDEPMODE = @CXXDEPMODE@
CXXFLAGS = @CXXFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DSYMUTIL = @DSYMUTIL@
ECHO = @ECHO@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
F77 = @F77@
FFLAGS = @FFLAGS@
GREP = @GREP@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LDFLAGS = @LDFLAGS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
MAKEINFO = @MAKEINFO@
MKDIR_P = @MKDIR_P@
NMEDIT = @NMEDIT@
OBJEXT = @OBJEXT@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@

PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
VERSION = @VERSION@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_CC = @ac_ct_CC@
ac_ct_CXX = @ac_ct_CXX@
ac_ct_F77 = @ac_ct_F77@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
blacklist_modules = @blacklist_modules@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
kbuilddir = @kbuilddir@
kinclude_CFLAGS = @kinclude_CFLAGS@
ksourcedir = @ksourcedir@
libdir = @libdir@
libexecdir = @libexecdir@
libxtables_vage = @libxtables_vage@
libxtables_vcurrent = @libxtables_vcurrent@
libxtables_vmajor = @libxtables_vmajor@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
pkgconfigdir = @pkgconfigdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
regular_CFLAGS = @regular_CFLAGS@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@

top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
xtlibdir = @xtlibdir@
ACLOCAL_AMFLAGS = -I m4
AUTOMAKE_OPTIONS = foreign subdir-objects
AM_CFLAGS = ${regular_CFLAGS} -I${top_builddir}/include -I${top_srcdir}/include ${kinclude_CFLAGS}
SUBDIRS = extensions $(am__append_1) $(am__append_2)

# libiptc
lib_LTLIBRARIES = libiptc/libiptc.la libxtables.la
libiptc_libiptc_la_SOURCES = libiptc/libip4tc.c libiptc/libip6tc.c
libiptc_libiptc_la_LDFLAGS = -version-info 0:0:0
libxtables_la_SOURCES = xtables.c
libxtables_la_LDFLAGS = -version-info ${libxtables_vcurrent}:0:${libxtables_vage}
libxtables_la_LIBADD = -ldl

# iptables, dynamic
iptables_SOURCES = iptables-standalone.c iptables.c
iptables_LDFLAGS = -rdynamic
iptables_LDADD = libiptc/libiptc.la extensions/libext4.a libxtables.la -lm
iptables_xml_LDADD = libxtables.la
iptables_multi_SOURCES = iptables-multi.c iptables-save.c \
                            iptables-restore.c iptables-xml.c \
                            iptables-standalone.c iptables.c

iptables_multi_CFLAGS = ${AM_CFLAGS} -DIPTABLES_MULTI
iptables_multi_LDFLAGS = ${iptables_LDFLAGS}
iptables_multi_LDADD = ${iptables_LDADD}
iptables_restore_SOURCES = iptables-restore.c iptables.c
iptables_restore_LDFLAGS = ${iptables_LDFLAGS}
iptables_restore_LDADD = ${iptables_LDADD}
iptables_save_SOURCES = iptables-save.c iptables.c
iptables_save_LDFLAGS = ${iptables_LDFLAGS}
iptables_save_LDADD = ${iptables_LDADD}

# iptables-multi, semi-static
iptables_static_SOURCES = ${iptables_multi_SOURCES} xtables.c
iptables_static_CFLAGS = ${iptables_multi_CFLAGS} -DNO_SHARED_LIBS=1
iptables_static_LDADD = libiptc/libiptc.la extensions/libext4.a -lm
iptables_xml_SOURCES = iptables-xml.c

# ip6tables, dynamic
ip6tables_SOURCES = ip6tables-standalone.c ip6tables.c
ip6tables_LDFLAGS = -rdynamic
ip6tables_LDADD = libiptc/libiptc.la extensions/libext6.a libxtables.la -lm
ip6tables_multi_SOURCES = ip6tables-multi.c ip6tables-save.c \
                            ip6tables-restore.c ip6tables-standalone.c \
                            ip6tables.c

ip6tables_multi_CFLAGS = ${AM_CFLAGS} -DIPTABLES_MULTI
ip6tables_multi_LDFLAGS = ${ip6tables_LDFLAGS}
ip6tables_multi_LDADD = ${ip6tables_LDADD}
ip6tables_restore_SOURCES = ip6tables-restore.c ip6tables.c
ip6tables_restore_LDFLAGS = ${ip6tables_LDFLAGS}
ip6tables_restore_LDADD = ${ip6tables_LDADD}
ip6tables_save_SOURCES = ip6tables-save.c ip6tables.c
ip6tables_save_LDFLAGS = ${ip6tables_LDFLAGS}
ip6tables_save_LDADD = ${ip6tables_LDADD}

# iptables-multi, semi-static
ip6tables_static_SOURCES = ${ip6tables_multi_SOURCES} xtables.c
ip6tables_static_CFLAGS = ${ip6tables_multi_CFLAGS} -DNO_SHARED_LIBS=1
ip6tables_static_LDADD = libiptc/libiptc.la extensions/libext6.a -lm
man_MANS = iptables.8 iptables-restore.8 iptables-save.8 \
                   iptables-xml.8 ip6tables.8 ip6tables-restore.8 \
                   ip6tables-save.8

CLEANFILES = iptables.8 ip6tables.8
pkgconfig_DATA = libiptc.pc xtables.pc
all: config.h
	$(MAKE) $(AM_MAKEFLAGS) all-recursive

.SUFFIXES:
.SUFFIXES: .c .lo .o .obj
am--refresh:
	@:
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      echo ' cd $(srcdir) && $(AUTOMAKE) --foreign '; \
	      cd $(srcdir) && $(AUTOMAKE) --foreign  \
		&& exit 0; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign  Makefile'; \
	cd $(top_srcdir) && \
	  $(AUTOMAKE) --foreign  Makefile
.PRECIOUS: Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    echo ' $(SHELL) ./config.status'; \
	    $(SHELL) ./config.status;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $@ $(am__depfiles_maybe)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $@ $(am__depfiles_maybe);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	$(SHELL) ./config.status --recheck

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(srcdir) && $(AUTOCONF)
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(srcdir) && $(ACLOCAL) $(ACLOCAL_AMFLAGS)

config.h: stamp-h1
	@if test ! -f $@; then \
	  rm -f stamp-h1; \
	  $(MAKE) $(AM_MAKEFLAGS) stamp-h1; \
	else :; fi

stamp-h1: $(srcdir)/config.h.in $(top_builddir)/config.status
	@rm -f stamp-h1
	cd $(top_builddir) && $(SHELL) ./config.status config.h
$(srcdir)/config.h.in:  $(am__configure_deps) 
	cd $(top_srcdir) && $(AUTOHEADER)
	rm -f stamp-h1
	touch $@

distclean-hdr:
	-rm -f config.h stamp-h1
extensions/GNUmakefile: $(top_builddir)/config.status $(top_srcdir)/extensions/GNUmakefile.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
include/iptables/internal.h: $(top_builddir)/config.status $(top_srcdir)/include/iptables/internal.h.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
libiptc.pc: $(top_builddir)/config.status $(srcdir)/libiptc.pc.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
xtables.pc: $(top_builddir)/config.status $(srcdir)/xtables.pc.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
install-libLTLIBRARIES: $(lib_LTLIBRARIES)
	@$(NORMAL_INSTALL)
	test -z "$(libdir)" || $(MKDIR_P) "$(DESTDIR)$(libdir)"
	@list='$(lib_LTLIBRARIES)'; for p in $$list; do \
	  if test -f $$p; then \
	    f=$(am__strip_dir) \
	    echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(libLTLIBRARIES_INSTALL) $(INSTALL_STRIP_FLAG) '$$p' '$(DESTDIR)$(libdir)/$$f'"; \
	    $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(libLTLIBRARIES_INSTALL) $(INSTALL_STRIP_FLAG) "$$p" "$(DESTDIR)$(libdir)/$$f"; \
	  else :; fi; \
	done

uninstall-libLTLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(lib_LTLIBRARIES)'; for p in $$list; do \
	  p=$(am__strip_dir) \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f '$(DESTDIR)$(libdir)/$$p'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f "$(DESTDIR)$(libdir)/$$p"; \
	done

clean-libLTLIBRARIES:
	-test -z "$(lib_LTLIBRARIES)" || rm -f $(lib_LTLIBRARIES)
	@list='$(lib_LTLIBRARIES)'; for p in $$list; do \
	  dir="`echo $$p | sed -e 's|/[^/]*$$||'`"; \
	  test "$$dir" != "$$p" || dir=.; \
	  echo "rm -f \"$${dir}/so_locations\""; \
	  rm -f "$${dir}/so_locations"; \
	done
libiptc/$(am__dirstamp):
	@$(MKDIR_P) libiptc
	@: > libiptc/$(am__dirstamp)
libiptc/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) libiptc/$(DEPDIR)
	@: > libiptc/$(DEPDIR)/$(am__dirstamp)
libiptc/libip4tc.lo: libiptc/$(am__dirstamp) \
	libiptc/$(DEPDIR)/$(am__dirstamp)
libiptc/libip6tc.lo: libiptc/$(am__dirstamp) \
	libiptc/$(DEPDIR)/$(am__dirstamp)
libiptc/libiptc.la: $(libiptc_libiptc_la_OBJECTS) $(libiptc_libiptc_la_DEPENDENCIES) libiptc/$(am__dirstamp)
	$(libiptc_libiptc_la_LINK) -rpath $(libdir) $(libiptc_libiptc_la_OBJECTS) $(libiptc_libiptc_la_LIBADD) $(LIBS)
libxtables.la: $(libxtables_la_OBJECTS) $(libxtables_la_DEPENDENCIES) 
	$(libxtables_la_LINK) -rpath $(libdir) $(libxtables_la_OBJECTS) $(libxtables_la_LIBADD) $(LIBS)
install-binPROGRAMS: $(bin_PROGRAMS)
	@$(NORMAL_INSTALL)
	test -z "$(bindir)" || $(MKDIR_P) "$(DESTDIR)$(bindir)"
	@list='$(bin_PROGRAMS)'; for p in $$list; do \
	  p1=`echo $$p|sed 's/$(EXEEXT)$$//'`; \
	  if test -f $$p \
	     || test -f $$p1 \
	  ; then \
	    f=`echo "$$p1" | sed 's,^.*/,,;$(transform);s/$$/$(EXEEXT)/'`; \
	   echo " $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(binPROGRAMS_INSTALL) '$$p' '$(DESTDIR)$(bindir)/$$f'"; \
	   $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(binPROGRAMS_INSTALL) "$$p" "$(DESTDIR)$(bindir)/$$f" || exit 1; \
	  else :; fi; \
	done

uninstall-binPROGRAMS:
	@$(NORMAL_UNINSTALL)
	@list='$(bin_PROGRAMS)'; for p in $$list; do \
	  f=`echo "$$p" | sed 's,^.*/,,;s/$(EXEEXT)$$//;$(transform);s/$$/$(EXEEXT)/'`; \
	  echo " rm -f '$(DESTDIR)$(bindir)/$$f'"; \
	  rm -f "$(DESTDIR)$(bindir)/$$f"; \
	done

clean-binPROGRAMS:
	@list='$(bin_PROGRAMS)'; for p in $$list; do \
	  f=`echo $$p|sed 's/$(EXEEXT)$$//'`; \
	  echo " rm -f $$p $$f"; \
	  rm -f $$p $$f ; \
	done

clean-noinstPROGRAMS:
	@list='$(noinst_PROGRAMS)'; for p in $$list; do \
	  f=`echo $$p|sed 's/$(EXEEXT)$$//'`; \
	  echo " rm -f $$p $$f"; \
	  rm -f $$p $$f ; \
	done
install-sbinPROGRAMS: $(sbin_PROGRAMS)
	@$(NORMAL_INSTALL)
	test -z "$(sbindir)" || $(MKDIR_P) "$(DESTDIR)$(sbindir)"
	@list='$(sbin_PROGRAMS)'; for p in $$list; do \
	  p1=`echo $$p|sed 's/$(EXEEXT)$$//'`; \
	  if test -f $$p \
	     || test -f $$p1 \
	  ; then \
	    f=`echo "$$p1" | sed 's,^.*/,,;$(transform);s/$$/$(EXEEXT)/'`; \
	   echo " $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(sbinPROGRAMS_INSTALL) '$$p' '$(DESTDIR)$(sbindir)/$$f'"; \
	   $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(sbinPROGRAMS_INSTALL) "$$p" "$(DESTDIR)$(sbindir)/$$f" || exit 1; \
	  else :; fi; \
	done

uninstall-sbinPROGRAMS:
	@$(NORMAL_UNINSTALL)
	@list='$(sbin_PROGRAMS)'; for p in $$list; do \
	  f=`echo "$$p" | sed 's,^.*/,,;s/$(EXEEXT)$$//;$(transform);s/$$/$(EXEEXT)/'`; \
	  echo " rm -f '$(DESTDIR)$(sbindir)/$$f'"; \
	  rm -f "$(DESTDIR)$(sbindir)/$$f"; \
	done

clean-sbinPROGRAMS:
	@list='$(sbin_PROGRAMS)'; for p in $$list; do \
	  f=`echo $$p|sed 's/$(EXEEXT)$$//'`; \
	  echo " rm -f $$p $$f"; \
	  rm -f $$p $$f ; \
	done
ip6tables$(EXEEXT): $(ip6tables_OBJECTS) $(ip6tables_DEPENDENCIES) 
	@rm -f ip6tables$(EXEEXT)
	$(ip6tables_LINK) $(ip6tables_OBJECTS) $(ip6tables_LDADD) $(LIBS)
ip6tables-multi$(EXEEXT): $(ip6tables_multi_OBJECTS) $(ip6tables_multi_DEPENDENCIES) 
	@rm -f ip6tables-multi$(EXEEXT)
	$(ip6tables_multi_LINK) $(ip6tables_multi_OBJECTS) $(ip6tables_multi_LDADD) $(LIBS)
ip6tables-restore$(EXEEXT): $(ip6tables_restore_OBJECTS) $(ip6tables_restore_DEPENDENCIES) 
	@rm -f ip6tables-restore$(EXEEXT)
	$(ip6tables_restore_LINK) $(ip6tables_restore_OBJECTS) $(ip6tables_restore_LDADD) $(LIBS)
ip6tables-save$(EXEEXT): $(ip6tables_save_OBJECTS) $(ip6tables_save_DEPENDENCIES) 
	@rm -f ip6tables-save$(EXEEXT)
	$(ip6tables_save_LINK) $(ip6tables_save_OBJECTS) $(ip6tables_save_LDADD) $(LIBS)
ip6tables-static$(EXEEXT): $(ip6tables_static_OBJECTS) $(ip6tables_static_DEPENDENCIES) 
	@rm -f ip6tables-static$(EXEEXT)
	$(ip6tables_static_LINK) $(ip6tables_static_OBJECTS) $(ip6tables_static_LDADD) $(LIBS)
iptables$(EXEEXT): $(iptables_OBJECTS) $(iptables_DEPENDENCIES) 
	@rm -f iptables$(EXEEXT)
	$(iptables_LINK) $(iptables_OBJECTS) $(iptables_LDADD) $(LIBS)
iptables-multi$(EXEEXT): $(iptables_multi_OBJECTS) $(iptables_multi_DEPENDENCIES) 
	@rm -f iptables-multi$(EXEEXT)
	$(iptables_multi_LINK) $(iptables_multi_OBJECTS) $(iptables_multi_LDADD) $(LIBS)
iptables-restore$(EXEEXT): $(iptables_restore_OBJECTS) $(iptables_restore_DEPENDENCIES) 
	@rm -f iptables-restore$(EXEEXT)
	$(iptables_restore_LINK) $(iptables_restore_OBJECTS) $(iptables_restore_LDADD) $(LIBS)
iptables-save$(EXEEXT): $(iptables_save_OBJECTS) $(iptables_save_DEPENDENCIES) 
	@rm -f iptables-save$(EXEEXT)
	$(iptables_save_LINK) $(iptables_save_OBJECTS) $(iptables_save_LDADD) $(LIBS)
iptables-static$(EXEEXT): $(iptables_static_OBJECTS) $(iptables_static_DEPENDENCIES) 
	@rm -f iptables-static$(EXEEXT)
	$(iptables_static_LINK) $(iptables_static_OBJECTS) $(iptables_static_LDADD) $(LIBS)
iptables-xml$(EXEEXT): $(iptables_xml_OBJECTS) $(iptables_xml_DEPENDENCIES) 
	@rm -f iptables-xml$(EXEEXT)
	$(LINK) $(iptables_xml_OBJECTS) $(iptables_xml_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)
	-rm -f libiptc/libip4tc.$(OBJEXT)
	-rm -f libiptc/libip4tc.lo
	-rm -f libiptc/libip6tc.$(OBJEXT)
	-rm -f libiptc/libip6tc.lo

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ip6tables-restore.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ip6tables-save.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ip6tables-standalone.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ip6tables.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ip6tables_multi-ip6tables-multi.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ip6tables_multi-ip6tables-restore.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ip6tables_multi-ip6tables-save.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ip6tables_multi-ip6tables-standalone.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ip6tables_multi-ip6tables.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ip6tables_static-ip6tables-multi.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ip6tables_static-ip6tables-restore.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ip6tables_static-ip6tables-save.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ip6tables_static-ip6tables-standalone.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ip6tables_static-ip6tables.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ip6tables_static-xtables.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/iptables-restore.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/iptables-save.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/iptables-standalone.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/iptables-xml.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/iptables.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/iptables_multi-iptables-multi.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/iptables_multi-iptables-restore.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/iptables_multi-iptables-save.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/iptables_multi-iptables-standalone.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/iptables_multi-iptables-xml.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/iptables_multi-iptables.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/iptables_static-iptables-multi.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/iptables_static-iptables-restore.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/iptables_static-iptables-save.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/iptables_static-iptables-standalone.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/iptables_static-iptables-xml.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/iptables_static-iptables.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/iptables_static-xtables.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/xtables.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@libiptc/$(DEPDIR)/libip4tc.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@libiptc/$(DEPDIR)/libip6tc.Plo@am__quote@

.c.o:
@am__fastdepCC_TRUE@	depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	mv -f $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCC_TRUE@	mv -f $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
@am__fastdepCC_TRUE@	depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCC_TRUE@	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	mv -f $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(LTCOMPILE) -c -o $@ $<

ip6tables_multi-ip6tables-multi.o: ip6tables-multi.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_multi_CFLAGS) $(CFLAGS) -MT ip6tables_multi-ip6tables-multi.o -MD -MP -MF $(DEPDIR)/ip6tables_multi-ip6tables-multi.Tpo -c -o ip6tables_multi-ip6tables-multi.o `test -f 'ip6tables-multi.c' || echo '$(srcdir)/'`ip6tables-multi.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_multi-ip6tables-multi.Tpo $(DEPDIR)/ip6tables_multi-ip6tables-multi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ip6tables-multi.c' object='ip6tables_multi-ip6tables-multi.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_multi_CFLAGS) $(CFLAGS) -c -o ip6tables_multi-ip6tables-multi.o `test -f 'ip6tables-multi.c' || echo '$(srcdir)/'`ip6tables-multi.c

ip6tables_multi-ip6tables-multi.obj: ip6tables-multi.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_multi_CFLAGS) $(CFLAGS) -MT ip6tables_multi-ip6tables-multi.obj -MD -MP -MF $(DEPDIR)/ip6tables_multi-ip6tables-multi.Tpo -c -o ip6tables_multi-ip6tables-multi.obj `if test -f 'ip6tables-multi.c'; then $(CYGPATH_W) 'ip6tables-multi.c'; else $(CYGPATH_W) '$(srcdir)/ip6tables-multi.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_multi-ip6tables-multi.Tpo $(DEPDIR)/ip6tables_multi-ip6tables-multi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ip6tables-multi.c' object='ip6tables_multi-ip6tables-multi.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_multi_CFLAGS) $(CFLAGS) -c -o ip6tables_multi-ip6tables-multi.obj `if test -f 'ip6tables-multi.c'; then $(CYGPATH_W) 'ip6tables-multi.c'; else $(CYGPATH_W) '$(srcdir)/ip6tables-multi.c'; fi`

ip6tables_multi-ip6tables-save.o: ip6tables-save.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_multi_CFLAGS) $(CFLAGS) -MT ip6tables_multi-ip6tables-save.o -MD -MP -MF $(DEPDIR)/ip6tables_multi-ip6tables-save.Tpo -c -o ip6tables_multi-ip6tables-save.o `test -f 'ip6tables-save.c' || echo '$(srcdir)/'`ip6tables-save.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_multi-ip6tables-save.Tpo $(DEPDIR)/ip6tables_multi-ip6tables-save.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ip6tables-save.c' object='ip6tables_multi-ip6tables-save.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_multi_CFLAGS) $(CFLAGS) -c -o ip6tables_multi-ip6tables-save.o `test -f 'ip6tables-save.c' || echo '$(srcdir)/'`ip6tables-save.c

ip6tables_multi-ip6tables-save.obj: ip6tables-save.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_multi_CFLAGS) $(CFLAGS) -MT ip6tables_multi-ip6tables-save.obj -MD -MP -MF $(DEPDIR)/ip6tables_multi-ip6tables-save.Tpo -c -o ip6tables_multi-ip6tables-save.obj `if test -f 'ip6tables-save.c'; then $(CYGPATH_W) 'ip6tables-save.c'; else $(CYGPATH_W) '$(srcdir)/ip6tables-save.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_multi-ip6tables-save.Tpo $(DEPDIR)/ip6tables_multi-ip6tables-save.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ip6tables-save.c' object='ip6tables_multi-ip6tables-save.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_multi_CFLAGS) $(CFLAGS) -c -o ip6tables_multi-ip6tables-save.obj `if test -f 'ip6tables-save.c'; then $(CYGPATH_W) 'ip6tables-save.c'; else $(CYGPATH_W) '$(srcdir)/ip6tables-save.c'; fi`

ip6tables_multi-ip6tables-restore.o: ip6tables-restore.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_multi_CFLAGS) $(CFLAGS) -MT ip6tables_multi-ip6tables-restore.o -MD -MP -MF $(DEPDIR)/ip6tables_multi-ip6tables-restore.Tpo -c -o ip6tables_multi-ip6tables-restore.o `test -f 'ip6tables-restore.c' || echo '$(srcdir)/'`ip6tables-restore.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_multi-ip6tables-restore.Tpo $(DEPDIR)/ip6tables_multi-ip6tables-restore.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ip6tables-restore.c' object='ip6tables_multi-ip6tables-restore.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_multi_CFLAGS) $(CFLAGS) -c -o ip6tables_multi-ip6tables-restore.o `test -f 'ip6tables-restore.c' || echo '$(srcdir)/'`ip6tables-restore.c

ip6tables_multi-ip6tables-restore.obj: ip6tables-restore.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_multi_CFLAGS) $(CFLAGS) -MT ip6tables_multi-ip6tables-restore.obj -MD -MP -MF $(DEPDIR)/ip6tables_multi-ip6tables-restore.Tpo -c -o ip6tables_multi-ip6tables-restore.obj `if test -f 'ip6tables-restore.c'; then $(CYGPATH_W) 'ip6tables-restore.c'; else $(CYGPATH_W) '$(srcdir)/ip6tables-restore.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_multi-ip6tables-restore.Tpo $(DEPDIR)/ip6tables_multi-ip6tables-restore.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ip6tables-restore.c' object='ip6tables_multi-ip6tables-restore.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_multi_CFLAGS) $(CFLAGS) -c -o ip6tables_multi-ip6tables-restore.obj `if test -f 'ip6tables-restore.c'; then $(CYGPATH_W) 'ip6tables-restore.c'; else $(CYGPATH_W) '$(srcdir)/ip6tables-restore.c'; fi`

ip6tables_multi-ip6tables-standalone.o: ip6tables-standalone.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_multi_CFLAGS) $(CFLAGS) -MT ip6tables_multi-ip6tables-standalone.o -MD -MP -MF $(DEPDIR)/ip6tables_multi-ip6tables-standalone.Tpo -c -o ip6tables_multi-ip6tables-standalone.o `test -f 'ip6tables-standalone.c' || echo '$(srcdir)/'`ip6tables-standalone.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_multi-ip6tables-standalone.Tpo $(DEPDIR)/ip6tables_multi-ip6tables-standalone.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ip6tables-standalone.c' object='ip6tables_multi-ip6tables-standalone.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_multi_CFLAGS) $(CFLAGS) -c -o ip6tables_multi-ip6tables-standalone.o `test -f 'ip6tables-standalone.c' || echo '$(srcdir)/'`ip6tables-standalone.c

ip6tables_multi-ip6tables-standalone.obj: ip6tables-standalone.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_multi_CFLAGS) $(CFLAGS) -MT ip6tables_multi-ip6tables-standalone.obj -MD -MP -MF $(DEPDIR)/ip6tables_multi-ip6tables-standalone.Tpo -c -o ip6tables_multi-ip6tables-standalone.obj `if test -f 'ip6tables-standalone.c'; then $(CYGPATH_W) 'ip6tables-standalone.c'; else $(CYGPATH_W) '$(srcdir)/ip6tables-standalone.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_multi-ip6tables-standalone.Tpo $(DEPDIR)/ip6tables_multi-ip6tables-standalone.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ip6tables-standalone.c' object='ip6tables_multi-ip6tables-standalone.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_multi_CFLAGS) $(CFLAGS) -c -o ip6tables_multi-ip6tables-standalone.obj `if test -f 'ip6tables-standalone.c'; then $(CYGPATH_W) 'ip6tables-standalone.c'; else $(CYGPATH_W) '$(srcdir)/ip6tables-standalone.c'; fi`

ip6tables_multi-ip6tables.o: ip6tables.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_multi_CFLAGS) $(CFLAGS) -MT ip6tables_multi-ip6tables.o -MD -MP -MF $(DEPDIR)/ip6tables_multi-ip6tables.Tpo -c -o ip6tables_multi-ip6tables.o `test -f 'ip6tables.c' || echo '$(srcdir)/'`ip6tables.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_multi-ip6tables.Tpo $(DEPDIR)/ip6tables_multi-ip6tables.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ip6tables.c' object='ip6tables_multi-ip6tables.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_multi_CFLAGS) $(CFLAGS) -c -o ip6tables_multi-ip6tables.o `test -f 'ip6tables.c' || echo '$(srcdir)/'`ip6tables.c

ip6tables_multi-ip6tables.obj: ip6tables.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_multi_CFLAGS) $(CFLAGS) -MT ip6tables_multi-ip6tables.obj -MD -MP -MF $(DEPDIR)/ip6tables_multi-ip6tables.Tpo -c -o ip6tables_multi-ip6tables.obj `if test -f 'ip6tables.c'; then $(CYGPATH_W) 'ip6tables.c'; else $(CYGPATH_W) '$(srcdir)/ip6tables.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_multi-ip6tables.Tpo $(DEPDIR)/ip6tables_multi-ip6tables.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ip6tables.c' object='ip6tables_multi-ip6tables.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_multi_CFLAGS) $(CFLAGS) -c -o ip6tables_multi-ip6tables.obj `if test -f 'ip6tables.c'; then $(CYGPATH_W) 'ip6tables.c'; else $(CYGPATH_W) '$(srcdir)/ip6tables.c'; fi`

ip6tables_static-ip6tables-multi.o: ip6tables-multi.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -MT ip6tables_static-ip6tables-multi.o -MD -MP -MF $(DEPDIR)/ip6tables_static-ip6tables-multi.Tpo -c -o ip6tables_static-ip6tables-multi.o `test -f 'ip6tables-multi.c' || echo '$(srcdir)/'`ip6tables-multi.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_static-ip6tables-multi.Tpo $(DEPDIR)/ip6tables_static-ip6tables-multi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ip6tables-multi.c' object='ip6tables_static-ip6tables-multi.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -c -o ip6tables_static-ip6tables-multi.o `test -f 'ip6tables-multi.c' || echo '$(srcdir)/'`ip6tables-multi.c

ip6tables_static-ip6tables-multi.obj: ip6tables-multi.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -MT ip6tables_static-ip6tables-multi.obj -MD -MP -MF $(DEPDIR)/ip6tables_static-ip6tables-multi.Tpo -c -o ip6tables_static-ip6tables-multi.obj `if test -f 'ip6tables-multi.c'; then $(CYGPATH_W) 'ip6tables-multi.c'; else $(CYGPATH_W) '$(srcdir)/ip6tables-multi.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_static-ip6tables-multi.Tpo $(DEPDIR)/ip6tables_static-ip6tables-multi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ip6tables-multi.c' object='ip6tables_static-ip6tables-multi.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -c -o ip6tables_static-ip6tables-multi.obj `if test -f 'ip6tables-multi.c'; then $(CYGPATH_W) 'ip6tables-multi.c'; else $(CYGPATH_W) '$(srcdir)/ip6tables-multi.c'; fi`

ip6tables_static-ip6tables-save.o: ip6tables-save.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -MT ip6tables_static-ip6tables-save.o -MD -MP -MF $(DEPDIR)/ip6tables_static-ip6tables-save.Tpo -c -o ip6tables_static-ip6tables-save.o `test -f 'ip6tables-save.c' || echo '$(srcdir)/'`ip6tables-save.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_static-ip6tables-save.Tpo $(DEPDIR)/ip6tables_static-ip6tables-save.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ip6tables-save.c' object='ip6tables_static-ip6tables-save.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -c -o ip6tables_static-ip6tables-save.o `test -f 'ip6tables-save.c' || echo '$(srcdir)/'`ip6tables-save.c

ip6tables_static-ip6tables-save.obj: ip6tables-save.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -MT ip6tables_static-ip6tables-save.obj -MD -MP -MF $(DEPDIR)/ip6tables_static-ip6tables-save.Tpo -c -o ip6tables_static-ip6tables-save.obj `if test -f 'ip6tables-save.c'; then $(CYGPATH_W) 'ip6tables-save.c'; else $(CYGPATH_W) '$(srcdir)/ip6tables-save.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_static-ip6tables-save.Tpo $(DEPDIR)/ip6tables_static-ip6tables-save.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ip6tables-save.c' object='ip6tables_static-ip6tables-save.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -c -o ip6tables_static-ip6tables-save.obj `if test -f 'ip6tables-save.c'; then $(CYGPATH_W) 'ip6tables-save.c'; else $(CYGPATH_W) '$(srcdir)/ip6tables-save.c'; fi`

ip6tables_static-ip6tables-restore.o: ip6tables-restore.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -MT ip6tables_static-ip6tables-restore.o -MD -MP -MF $(DEPDIR)/ip6tables_static-ip6tables-restore.Tpo -c -o ip6tables_static-ip6tables-restore.o `test -f 'ip6tables-restore.c' || echo '$(srcdir)/'`ip6tables-restore.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_static-ip6tables-restore.Tpo $(DEPDIR)/ip6tables_static-ip6tables-restore.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ip6tables-restore.c' object='ip6tables_static-ip6tables-restore.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -c -o ip6tables_static-ip6tables-restore.o `test -f 'ip6tables-restore.c' || echo '$(srcdir)/'`ip6tables-restore.c

ip6tables_static-ip6tables-restore.obj: ip6tables-restore.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -MT ip6tables_static-ip6tables-restore.obj -MD -MP -MF $(DEPDIR)/ip6tables_static-ip6tables-restore.Tpo -c -o ip6tables_static-ip6tables-restore.obj `if test -f 'ip6tables-restore.c'; then $(CYGPATH_W) 'ip6tables-restore.c'; else $(CYGPATH_W) '$(srcdir)/ip6tables-restore.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_static-ip6tables-restore.Tpo $(DEPDIR)/ip6tables_static-ip6tables-restore.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ip6tables-restore.c' object='ip6tables_static-ip6tables-restore.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -c -o ip6tables_static-ip6tables-restore.obj `if test -f 'ip6tables-restore.c'; then $(CYGPATH_W) 'ip6tables-restore.c'; else $(CYGPATH_W) '$(srcdir)/ip6tables-restore.c'; fi`

ip6tables_static-ip6tables-standalone.o: ip6tables-standalone.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -MT ip6tables_static-ip6tables-standalone.o -MD -MP -MF $(DEPDIR)/ip6tables_static-ip6tables-standalone.Tpo -c -o ip6tables_static-ip6tables-standalone.o `test -f 'ip6tables-standalone.c' || echo '$(srcdir)/'`ip6tables-standalone.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_static-ip6tables-standalone.Tpo $(DEPDIR)/ip6tables_static-ip6tables-standalone.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ip6tables-standalone.c' object='ip6tables_static-ip6tables-standalone.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -c -o ip6tables_static-ip6tables-standalone.o `test -f 'ip6tables-standalone.c' || echo '$(srcdir)/'`ip6tables-standalone.c

ip6tables_static-ip6tables-standalone.obj: ip6tables-standalone.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -MT ip6tables_static-ip6tables-standalone.obj -MD -MP -MF $(DEPDIR)/ip6tables_static-ip6tables-standalone.Tpo -c -o ip6tables_static-ip6tables-standalone.obj `if test -f 'ip6tables-standalone.c'; then $(CYGPATH_W) 'ip6tables-standalone.c'; else $(CYGPATH_W) '$(srcdir)/ip6tables-standalone.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_static-ip6tables-standalone.Tpo $(DEPDIR)/ip6tables_static-ip6tables-standalone.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ip6tables-standalone.c' object='ip6tables_static-ip6tables-standalone.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -c -o ip6tables_static-ip6tables-standalone.obj `if test -f 'ip6tables-standalone.c'; then $(CYGPATH_W) 'ip6tables-standalone.c'; else $(CYGPATH_W) '$(srcdir)/ip6tables-standalone.c'; fi`

ip6tables_static-ip6tables.o: ip6tables.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -MT ip6tables_static-ip6tables.o -MD -MP -MF $(DEPDIR)/ip6tables_static-ip6tables.Tpo -c -o ip6tables_static-ip6tables.o `test -f 'ip6tables.c' || echo '$(srcdir)/'`ip6tables.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_static-ip6tables.Tpo $(DEPDIR)/ip6tables_static-ip6tables.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ip6tables.c' object='ip6tables_static-ip6tables.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -c -o ip6tables_static-ip6tables.o `test -f 'ip6tables.c' || echo '$(srcdir)/'`ip6tables.c

ip6tables_static-ip6tables.obj: ip6tables.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -MT ip6tables_static-ip6tables.obj -MD -MP -MF $(DEPDIR)/ip6tables_static-ip6tables.Tpo -c -o ip6tables_static-ip6tables.obj `if test -f 'ip6tables.c'; then $(CYGPATH_W) 'ip6tables.c'; else $(CYGPATH_W) '$(srcdir)/ip6tables.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_static-ip6tables.Tpo $(DEPDIR)/ip6tables_static-ip6tables.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ip6tables.c' object='ip6tables_static-ip6tables.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -c -o ip6tables_static-ip6tables.obj `if test -f 'ip6tables.c'; then $(CYGPATH_W) 'ip6tables.c'; else $(CYGPATH_W) '$(srcdir)/ip6tables.c'; fi`

ip6tables_static-xtables.o: xtables.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -MT ip6tables_static-xtables.o -MD -MP -MF $(DEPDIR)/ip6tables_static-xtables.Tpo -c -o ip6tables_static-xtables.o `test -f 'xtables.c' || echo '$(srcdir)/'`xtables.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_static-xtables.Tpo $(DEPDIR)/ip6tables_static-xtables.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='xtables.c' object='ip6tables_static-xtables.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -c -o ip6tables_static-xtables.o `test -f 'xtables.c' || echo '$(srcdir)/'`xtables.c

ip6tables_static-xtables.obj: xtables.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -MT ip6tables_static-xtables.obj -MD -MP -MF $(DEPDIR)/ip6tables_static-xtables.Tpo -c -o ip6tables_static-xtables.obj `if test -f 'xtables.c'; then $(CYGPATH_W) 'xtables.c'; else $(CYGPATH_W) '$(srcdir)/xtables.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ip6tables_static-xtables.Tpo $(DEPDIR)/ip6tables_static-xtables.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='xtables.c' object='ip6tables_static-xtables.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(ip6tables_static_CFLAGS) $(CFLAGS) -c -o ip6tables_static-xtables.obj `if test -f 'xtables.c'; then $(CYGPATH_W) 'xtables.c'; else $(CYGPATH_W) '$(srcdir)/xtables.c'; fi`

iptables_multi-iptables-multi.o: iptables-multi.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -MT iptables_multi-iptables-multi.o -MD -MP -MF $(DEPDIR)/iptables_multi-iptables-multi.Tpo -c -o iptables_multi-iptables-multi.o `test -f 'iptables-multi.c' || echo '$(srcdir)/'`iptables-multi.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_multi-iptables-multi.Tpo $(DEPDIR)/iptables_multi-iptables-multi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables-multi.c' object='iptables_multi-iptables-multi.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -c -o iptables_multi-iptables-multi.o `test -f 'iptables-multi.c' || echo '$(srcdir)/'`iptables-multi.c

iptables_multi-iptables-multi.obj: iptables-multi.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -MT iptables_multi-iptables-multi.obj -MD -MP -MF $(DEPDIR)/iptables_multi-iptables-multi.Tpo -c -o iptables_multi-iptables-multi.obj `if test -f 'iptables-multi.c'; then $(CYGPATH_W) 'iptables-multi.c'; else $(CYGPATH_W) '$(srcdir)/iptables-multi.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_multi-iptables-multi.Tpo $(DEPDIR)/iptables_multi-iptables-multi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables-multi.c' object='iptables_multi-iptables-multi.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -c -o iptables_multi-iptables-multi.obj `if test -f 'iptables-multi.c'; then $(CYGPATH_W) 'iptables-multi.c'; else $(CYGPATH_W) '$(srcdir)/iptables-multi.c'; fi`

iptables_multi-iptables-save.o: iptables-save.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -MT iptables_multi-iptables-save.o -MD -MP -MF $(DEPDIR)/iptables_multi-iptables-save.Tpo -c -o iptables_multi-iptables-save.o `test -f 'iptables-save.c' || echo '$(srcdir)/'`iptables-save.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_multi-iptables-save.Tpo $(DEPDIR)/iptables_multi-iptables-save.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables-save.c' object='iptables_multi-iptables-save.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -c -o iptables_multi-iptables-save.o `test -f 'iptables-save.c' || echo '$(srcdir)/'`iptables-save.c

iptables_multi-iptables-save.obj: iptables-save.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -MT iptables_multi-iptables-save.obj -MD -MP -MF $(DEPDIR)/iptables_multi-iptables-save.Tpo -c -o iptables_multi-iptables-save.obj `if test -f 'iptables-save.c'; then $(CYGPATH_W) 'iptables-save.c'; else $(CYGPATH_W) '$(srcdir)/iptables-save.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_multi-iptables-save.Tpo $(DEPDIR)/iptables_multi-iptables-save.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables-save.c' object='iptables_multi-iptables-save.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -c -o iptables_multi-iptables-save.obj `if test -f 'iptables-save.c'; then $(CYGPATH_W) 'iptables-save.c'; else $(CYGPATH_W) '$(srcdir)/iptables-save.c'; fi`

iptables_multi-iptables-restore.o: iptables-restore.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -MT iptables_multi-iptables-restore.o -MD -MP -MF $(DEPDIR)/iptables_multi-iptables-restore.Tpo -c -o iptables_multi-iptables-restore.o `test -f 'iptables-restore.c' || echo '$(srcdir)/'`iptables-restore.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_multi-iptables-restore.Tpo $(DEPDIR)/iptables_multi-iptables-restore.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables-restore.c' object='iptables_multi-iptables-restore.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -c -o iptables_multi-iptables-restore.o `test -f 'iptables-restore.c' || echo '$(srcdir)/'`iptables-restore.c

iptables_multi-iptables-restore.obj: iptables-restore.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -MT iptables_multi-iptables-restore.obj -MD -MP -MF $(DEPDIR)/iptables_multi-iptables-restore.Tpo -c -o iptables_multi-iptables-restore.obj `if test -f 'iptables-restore.c'; then $(CYGPATH_W) 'iptables-restore.c'; else $(CYGPATH_W) '$(srcdir)/iptables-restore.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_multi-iptables-restore.Tpo $(DEPDIR)/iptables_multi-iptables-restore.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables-restore.c' object='iptables_multi-iptables-restore.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -c -o iptables_multi-iptables-restore.obj `if test -f 'iptables-restore.c'; then $(CYGPATH_W) 'iptables-restore.c'; else $(CYGPATH_W) '$(srcdir)/iptables-restore.c'; fi`

iptables_multi-iptables-xml.o: iptables-xml.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -MT iptables_multi-iptables-xml.o -MD -MP -MF $(DEPDIR)/iptables_multi-iptables-xml.Tpo -c -o iptables_multi-iptables-xml.o `test -f 'iptables-xml.c' || echo '$(srcdir)/'`iptables-xml.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_multi-iptables-xml.Tpo $(DEPDIR)/iptables_multi-iptables-xml.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables-xml.c' object='iptables_multi-iptables-xml.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -c -o iptables_multi-iptables-xml.o `test -f 'iptables-xml.c' || echo '$(srcdir)/'`iptables-xml.c

iptables_multi-iptables-xml.obj: iptables-xml.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -MT iptables_multi-iptables-xml.obj -MD -MP -MF $(DEPDIR)/iptables_multi-iptables-xml.Tpo -c -o iptables_multi-iptables-xml.obj `if test -f 'iptables-xml.c'; then $(CYGPATH_W) 'iptables-xml.c'; else $(CYGPATH_W) '$(srcdir)/iptables-xml.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_multi-iptables-xml.Tpo $(DEPDIR)/iptables_multi-iptables-xml.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables-xml.c' object='iptables_multi-iptables-xml.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -c -o iptables_multi-iptables-xml.obj `if test -f 'iptables-xml.c'; then $(CYGPATH_W) 'iptables-xml.c'; else $(CYGPATH_W) '$(srcdir)/iptables-xml.c'; fi`

iptables_multi-iptables-standalone.o: iptables-standalone.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -MT iptables_multi-iptables-standalone.o -MD -MP -MF $(DEPDIR)/iptables_multi-iptables-standalone.Tpo -c -o iptables_multi-iptables-standalone.o `test -f 'iptables-standalone.c' || echo '$(srcdir)/'`iptables-standalone.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_multi-iptables-standalone.Tpo $(DEPDIR)/iptables_multi-iptables-standalone.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables-standalone.c' object='iptables_multi-iptables-standalone.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -c -o iptables_multi-iptables-standalone.o `test -f 'iptables-standalone.c' || echo '$(srcdir)/'`iptables-standalone.c

iptables_multi-iptables-standalone.obj: iptables-standalone.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -MT iptables_multi-iptables-standalone.obj -MD -MP -MF $(DEPDIR)/iptables_multi-iptables-standalone.Tpo -c -o iptables_multi-iptables-standalone.obj `if test -f 'iptables-standalone.c'; then $(CYGPATH_W) 'iptables-standalone.c'; else $(CYGPATH_W) '$(srcdir)/iptables-standalone.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_multi-iptables-standalone.Tpo $(DEPDIR)/iptables_multi-iptables-standalone.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables-standalone.c' object='iptables_multi-iptables-standalone.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -c -o iptables_multi-iptables-standalone.obj `if test -f 'iptables-standalone.c'; then $(CYGPATH_W) 'iptables-standalone.c'; else $(CYGPATH_W) '$(srcdir)/iptables-standalone.c'; fi`

iptables_multi-iptables.o: iptables.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -MT iptables_multi-iptables.o -MD -MP -MF $(DEPDIR)/iptables_multi-iptables.Tpo -c -o iptables_multi-iptables.o `test -f 'iptables.c' || echo '$(srcdir)/'`iptables.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_multi-iptables.Tpo $(DEPDIR)/iptables_multi-iptables.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables.c' object='iptables_multi-iptables.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -c -o iptables_multi-iptables.o `test -f 'iptables.c' || echo '$(srcdir)/'`iptables.c

iptables_multi-iptables.obj: iptables.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -MT iptables_multi-iptables.obj -MD -MP -MF $(DEPDIR)/iptables_multi-iptables.Tpo -c -o iptables_multi-iptables.obj `if test -f 'iptables.c'; then $(CYGPATH_W) 'iptables.c'; else $(CYGPATH_W) '$(srcdir)/iptables.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_multi-iptables.Tpo $(DEPDIR)/iptables_multi-iptables.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables.c' object='iptables_multi-iptables.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_multi_CFLAGS) $(CFLAGS) -c -o iptables_multi-iptables.obj `if test -f 'iptables.c'; then $(CYGPATH_W) 'iptables.c'; else $(CYGPATH_W) '$(srcdir)/iptables.c'; fi`

iptables_static-iptables-multi.o: iptables-multi.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -MT iptables_static-iptables-multi.o -MD -MP -MF $(DEPDIR)/iptables_static-iptables-multi.Tpo -c -o iptables_static-iptables-multi.o `test -f 'iptables-multi.c' || echo '$(srcdir)/'`iptables-multi.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_static-iptables-multi.Tpo $(DEPDIR)/iptables_static-iptables-multi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables-multi.c' object='iptables_static-iptables-multi.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -c -o iptables_static-iptables-multi.o `test -f 'iptables-multi.c' || echo '$(srcdir)/'`iptables-multi.c

iptables_static-iptables-multi.obj: iptables-multi.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -MT iptables_static-iptables-multi.obj -MD -MP -MF $(DEPDIR)/iptables_static-iptables-multi.Tpo -c -o iptables_static-iptables-multi.obj `if test -f 'iptables-multi.c'; then $(CYGPATH_W) 'iptables-multi.c'; else $(CYGPATH_W) '$(srcdir)/iptables-multi.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_static-iptables-multi.Tpo $(DEPDIR)/iptables_static-iptables-multi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables-multi.c' object='iptables_static-iptables-multi.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -c -o iptables_static-iptables-multi.obj `if test -f 'iptables-multi.c'; then $(CYGPATH_W) 'iptables-multi.c'; else $(CYGPATH_W) '$(srcdir)/iptables-multi.c'; fi`

iptables_static-iptables-save.o: iptables-save.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -MT iptables_static-iptables-save.o -MD -MP -MF $(DEPDIR)/iptables_static-iptables-save.Tpo -c -o iptables_static-iptables-save.o `test -f 'iptables-save.c' || echo '$(srcdir)/'`iptables-save.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_static-iptables-save.Tpo $(DEPDIR)/iptables_static-iptables-save.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables-save.c' object='iptables_static-iptables-save.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -c -o iptables_static-iptables-save.o `test -f 'iptables-save.c' || echo '$(srcdir)/'`iptables-save.c

iptables_static-iptables-save.obj: iptables-save.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -MT iptables_static-iptables-save.obj -MD -MP -MF $(DEPDIR)/iptables_static-iptables-save.Tpo -c -o iptables_static-iptables-save.obj `if test -f 'iptables-save.c'; then $(CYGPATH_W) 'iptables-save.c'; else $(CYGPATH_W) '$(srcdir)/iptables-save.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_static-iptables-save.Tpo $(DEPDIR)/iptables_static-iptables-save.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables-save.c' object='iptables_static-iptables-save.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -c -o iptables_static-iptables-save.obj `if test -f 'iptables-save.c'; then $(CYGPATH_W) 'iptables-save.c'; else $(CYGPATH_W) '$(srcdir)/iptables-save.c'; fi`

iptables_static-iptables-restore.o: iptables-restore.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -MT iptables_static-iptables-restore.o -MD -MP -MF $(DEPDIR)/iptables_static-iptables-restore.Tpo -c -o iptables_static-iptables-restore.o `test -f 'iptables-restore.c' || echo '$(srcdir)/'`iptables-restore.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_static-iptables-restore.Tpo $(DEPDIR)/iptables_static-iptables-restore.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables-restore.c' object='iptables_static-iptables-restore.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -c -o iptables_static-iptables-restore.o `test -f 'iptables-restore.c' || echo '$(srcdir)/'`iptables-restore.c

iptables_static-iptables-restore.obj: iptables-restore.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -MT iptables_static-iptables-restore.obj -MD -MP -MF $(DEPDIR)/iptables_static-iptables-restore.Tpo -c -o iptables_static-iptables-restore.obj `if test -f 'iptables-restore.c'; then $(CYGPATH_W) 'iptables-restore.c'; else $(CYGPATH_W) '$(srcdir)/iptables-restore.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_static-iptables-restore.Tpo $(DEPDIR)/iptables_static-iptables-restore.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables-restore.c' object='iptables_static-iptables-restore.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -c -o iptables_static-iptables-restore.obj `if test -f 'iptables-restore.c'; then $(CYGPATH_W) 'iptables-restore.c'; else $(CYGPATH_W) '$(srcdir)/iptables-restore.c'; fi`

iptables_static-iptables-xml.o: iptables-xml.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -MT iptables_static-iptables-xml.o -MD -MP -MF $(DEPDIR)/iptables_static-iptables-xml.Tpo -c -o iptables_static-iptables-xml.o `test -f 'iptables-xml.c' || echo '$(srcdir)/'`iptables-xml.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_static-iptables-xml.Tpo $(DEPDIR)/iptables_static-iptables-xml.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables-xml.c' object='iptables_static-iptables-xml.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -c -o iptables_static-iptables-xml.o `test -f 'iptables-xml.c' || echo '$(srcdir)/'`iptables-xml.c

iptables_static-iptables-xml.obj: iptables-xml.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -MT iptables_static-iptables-xml.obj -MD -MP -MF $(DEPDIR)/iptables_static-iptables-xml.Tpo -c -o iptables_static-iptables-xml.obj `if test -f 'iptables-xml.c'; then $(CYGPATH_W) 'iptables-xml.c'; else $(CYGPATH_W) '$(srcdir)/iptables-xml.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_static-iptables-xml.Tpo $(DEPDIR)/iptables_static-iptables-xml.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables-xml.c' object='iptables_static-iptables-xml.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -c -o iptables_static-iptables-xml.obj `if test -f 'iptables-xml.c'; then $(CYGPATH_W) 'iptables-xml.c'; else $(CYGPATH_W) '$(srcdir)/iptables-xml.c'; fi`

iptables_static-iptables-standalone.o: iptables-standalone.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -MT iptables_static-iptables-standalone.o -MD -MP -MF $(DEPDIR)/iptables_static-iptables-standalone.Tpo -c -o iptables_static-iptables-standalone.o `test -f 'iptables-standalone.c' || echo '$(srcdir)/'`iptables-standalone.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_static-iptables-standalone.Tpo $(DEPDIR)/iptables_static-iptables-standalone.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables-standalone.c' object='iptables_static-iptables-standalone.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -c -o iptables_static-iptables-standalone.o `test -f 'iptables-standalone.c' || echo '$(srcdir)/'`iptables-standalone.c

iptables_static-iptables-standalone.obj: iptables-standalone.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -MT iptables_static-iptables-standalone.obj -MD -MP -MF $(DEPDIR)/iptables_static-iptables-standalone.Tpo -c -o iptables_static-iptables-standalone.obj `if test -f 'iptables-standalone.c'; then $(CYGPATH_W) 'iptables-standalone.c'; else $(CYGPATH_W) '$(srcdir)/iptables-standalone.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_static-iptables-standalone.Tpo $(DEPDIR)/iptables_static-iptables-standalone.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables-standalone.c' object='iptables_static-iptables-standalone.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -c -o iptables_static-iptables-standalone.obj `if test -f 'iptables-standalone.c'; then $(CYGPATH_W) 'iptables-standalone.c'; else $(CYGPATH_W) '$(srcdir)/iptables-standalone.c'; fi`

iptables_static-iptables.o: iptables.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -MT iptables_static-iptables.o -MD -MP -MF $(DEPDIR)/iptables_static-iptables.Tpo -c -o iptables_static-iptables.o `test -f 'iptables.c' || echo '$(srcdir)/'`iptables.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_static-iptables.Tpo $(DEPDIR)/iptables_static-iptables.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables.c' object='iptables_static-iptables.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -c -o iptables_static-iptables.o `test -f 'iptables.c' || echo '$(srcdir)/'`iptables.c

iptables_static-iptables.obj: iptables.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -MT iptables_static-iptables.obj -MD -MP -MF $(DEPDIR)/iptables_static-iptables.Tpo -c -o iptables_static-iptables.obj `if test -f 'iptables.c'; then $(CYGPATH_W) 'iptables.c'; else $(CYGPATH_W) '$(srcdir)/iptables.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_static-iptables.Tpo $(DEPDIR)/iptables_static-iptables.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='iptables.c' object='iptables_static-iptables.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -c -o iptables_static-iptables.obj `if test -f 'iptables.c'; then $(CYGPATH_W) 'iptables.c'; else $(CYGPATH_W) '$(srcdir)/iptables.c'; fi`

iptables_static-xtables.o: xtables.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -MT iptables_static-xtables.o -MD -MP -MF $(DEPDIR)/iptables_static-xtables.Tpo -c -o iptables_static-xtables.o `test -f 'xtables.c' || echo '$(srcdir)/'`xtables.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_static-xtables.Tpo $(DEPDIR)/iptables_static-xtables.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='xtables.c' object='iptables_static-xtables.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -c -o iptables_static-xtables.o `test -f 'xtables.c' || echo '$(srcdir)/'`xtables.c

iptables_static-xtables.obj: xtables.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -MT iptables_static-xtables.obj -MD -MP -MF $(DEPDIR)/iptables_static-xtables.Tpo -c -o iptables_static-xtables.obj `if test -f 'xtables.c'; then $(CYGPATH_W) 'xtables.c'; else $(CYGPATH_W) '$(srcdir)/xtables.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/iptables_static-xtables.Tpo $(DEPDIR)/iptables_static-xtables.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='xtables.c' object='iptables_static-xtables.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(iptables_static_CFLAGS) $(CFLAGS) -c -o iptables_static-xtables.obj `if test -f 'xtables.c'; then $(CYGPATH_W) 'xtables.c'; else $(CYGPATH_W) '$(srcdir)/xtables.c'; fi`

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
	-rm -rf libiptc/.libs libiptc/_libs

distclean-libtool:
	-rm -f libtool
install-man8: $(man8_MANS) $(man_MANS)
	@$(NORMAL_INSTALL)
	test -z "$(man8dir)" || $(MKDIR_P) "$(DESTDIR)$(man8dir)"
	@list='$(man8_MANS) $(dist_man8_MANS) $(nodist_man8_MANS)'; \
	l2='$(man_MANS) $(dist_man_MANS) $(nodist_man_MANS)'; \
	for i in $$l2; do \
	  case "$$i" in \
	    *.8*) list="$$list $$i" ;; \
	  esac; \
	done; \
	for i in $$list; do \
	  if test -f $(srcdir)/$$i; then file=$(srcdir)/$$i; \
	  else file=$$i; fi; \
	  ext=`echo $$i | sed -e 's/^.*\\.//'`; \
	  case "$$ext" in \
	    8*) ;; \
	    *) ext='8' ;; \
	  esac; \
	  inst=`echo $$i | sed -e 's/\\.[0-9a-z]*$$//'`; \
	  inst=`echo $$inst | sed -e 's/^.*\///'`; \
	  inst=`echo $$inst | sed '$(transform)'`.$$ext; \
	  echo " $(INSTALL_DATA) '$$file' '$(DESTDIR)$(man8dir)/$$inst'"; \
	  $(INSTALL_DATA) "$$file" "$(DESTDIR)$(man8dir)/$$inst"; \
	done
uninstall-man8:
	@$(NORMAL_UNINSTALL)
	@list='$(man8_MANS) $(dist_man8_MANS) $(nodist_man8_MANS)'; \
	l2='$(man_MANS) $(dist_man_MANS) $(nodist_man_MANS)'; \
	for i in $$l2; do \
	  case "$$i" in \
	    *.8*) list="$$list $$i" ;; \
	  esac; \
	done; \
	for i in $$list; do \
	  ext=`echo $$i | sed -e 's/^.*\\.//'`; \
	  case "$$ext" in \
	    8*) ;; \
	    *) ext='8' ;; \
	  esac; \
	  inst=`echo $$i | sed -e 's/\\.[0-9a-z]*$$//'`; \
	  inst=`echo $$inst | sed -e 's/^.*\///'`; \
	  inst=`echo $$inst | sed '$(transform)'`.$$ext; \
	  echo " rm -f '$(DESTDIR)$(man8dir)/$$inst'"; \
	  rm -f "$(DESTDIR)$(man8dir)/$$inst"; \
	done
install-pkgconfigDATA: $(pkgconfig_DATA)
	@$(NORMAL_INSTALL)
	test -z "$(pkgconfigdir)" || $(MKDIR_P) "$(DESTDIR)$(pkgconfigdir)"
	@list='$(pkgconfig_DATA)'; for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  f=$(am__strip_dir) \
	  echo " $(pkgconfigDATA_INSTALL) '$$d$$p' '$(DESTDIR)$(pkgconfigdir)/$$f'"; \
	  $(pkgconfigDATA_INSTALL) "$$d$$p" "$(DESTDIR)$(pkgconfigdir)/$$f"; \
	done

uninstall-pkgconfigDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(pkgconfig_DATA)'; for p in $$list; do \
	  f=$(am__strip_dir) \
	  echo " rm -f '$(DESTDIR)$(pkgconfigdir)/$$f'"; \
	  rm -f "$(DESTDIR)$(pkgconfigdir)/$$f"; \
	done

# This directory's subdirectories are mostly independent; you can cd
# into them and run `make' without going through this Makefile.
# To change the values of `make' variables: instead of editing Makefiles,
# (1) if the variable is set in `config.status', edit `config.status'
#     (which will cause the Makefiles to be regenerated when you run `make');
# (2) otherwise, pass the desired values on the `make' command line.
$(RECURSIVE_TARGETS):
	@failcom='exit 1'; \
	for f in x $$MAKEFLAGS; do \
	  case $$f in \
	    *=* | --[!k]*);; \
	    *k*) failcom='fail=yes';; \
	  esac; \
	done; \
	dot_seen=no; \
	target=`echo $@ | sed s/-recursive//`; \
	list='$(SUBDIRS)'; for subdir in $$list; do \
	  echo "Making $$target in $$subdir"; \
	  if test "$$subdir" = "."; then \
	    dot_seen=yes; \
	    local_target="$$target-am"; \
	  else \
	    local_target="$$target"; \
	  fi; \
	  (cd $$subdir && $(MAKE) $(AM_MAKEFLAGS) $$local_target) \
	  || eval $$failcom; \
	done; \
	if test "$$dot_seen" = "no"; then \
	  $(MAKE) $(AM_MAKEFLAGS) "$$target-am" || exit 1; \
	fi; test -z "$$fail"

$(RECURSIVE_CLEAN_TARGETS):
	@failcom='exit 1'; \
	for f in x $$MAKEFLAGS; do \
	  case $$f in \
	    *=* | --[!k]*);; \
	    *k*) failcom='fail=yes';; \
	  esac; \
	done; \
	dot_seen=no; \
	case "$@" in \
	  distclean-* | maintainer-clean-*) list='$(DIST_SUBDIRS)' ;; \
	  *) list='$(SUBDIRS)' ;; \
	esac; \
	rev=''; for subdir in $$list; do \
	  if test "$$subdir" = "."; then :; else \
	    rev="$$subdir $$rev"; \
	  fi; \
	done; \
	rev="$$rev ."; \
	target=`echo $@ | sed s/-recursive//`; \
	for subdir in $$rev; do \
	  echo "Making $$target in $$subdir"; \
	  if test "$$subdir" = "."; then \
	    local_target="$$target-am"; \
	  else \
	    local_target="$$target"; \
	  fi; \
	  (cd $$subdir && $(MAKE) $(AM_MAKEFLAGS) $$local_target) \
	  || eval $$failcom; \
	done && test -z "$$fail"
tags-recursive:
	list='$(SUBDIRS)'; for subdir in $$list; do \
	  test "$$subdir" = . || (cd $$subdir && $(MAKE) $(AM_MAKEFLAGS) tags); \
	done
ctags-recursive:
	list='$(SUBDIRS)'; for subdir in $$list; do \
	  test "$$subdir" = . || (cd $$subdir && $(MAKE) $(AM_MAKEFLAGS) ctags); \
	done

ID: $(HEADERS) $(SOURCES) $(LISP) $(TAGS_FILES)
	list='$(SOURCES) $(HEADERS) $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '{ files[$$0] = 1; nonemtpy = 1; } \
	      END { if (nonempty) { for (i in files) print i; }; }'`; \
	mkid -fID $$unique
tags: TAGS

TAGS: tags-recursive $(HEADERS) $(SOURCES) config.h.in $(TAGS_DEPENDENCIES) \
		$(TAGS_FILES) $(LISP)
	tags=; \
	here=`pwd`; \
	if ($(ETAGS) --etags-include --version) >/dev/null 2>&1; then \
	  include_option=--etags-include; \
	  empty_fix=.; \
	else \
	  include_option=--include; \
	  empty_fix=; \
	fi; \
	list='$(SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    test ! -f $$subdir/TAGS || \
	      tags="$$tags $$include_option=$$here/$$subdir/TAGS"; \
	  fi; \
	done; \
	list='$(SOURCES) $(HEADERS) config.h.in $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '{ files[$$0] = 1; nonempty = 1; } \
	      END { if (nonempty) { for (i in files) print i; }; }'`; \
	if test -z "$(ETAGS_ARGS)$$tags$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	    $$tags $$unique; \
	fi
ctags: CTAGS
CTAGS: ctags-recursive $(HEADERS) $(SOURCES) config.h.in $(TAGS_DEPENDENCIES) \
		$(TAGS_FILES) $(LISP)
	tags=; \
	list='$(SOURCES) $(HEADERS) config.h.in $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '{ files[$$0] = 1; nonempty = 1; } \
	      END { if (nonempty) { for (i in files) print i; }; }'`; \
	test -z "$(CTAGS_ARGS)$$tags$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$tags $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && cd $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) $$here

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(DISTFILES)
	$(am__remove_distdir)
	test -d $(distdir) || mkdir $(distdir)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -pR $(srcdir)/$$file $(distdir)$$dir || exit 1; \
	    fi; \
	    cp -pR $$d/$$file $(distdir)$$dir || exit 1; \
	  else \
	    test -f $(distdir)/$$file \
	    || cp -p $$d/$$file $(distdir)/$$file \
	    || exit 1; \
	  fi; \
	done
	list='$(DIST_SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    test -d "$(distdir)/$$subdir" \
	    || $(MKDIR_P) "$(distdir)/$$subdir" \
	    || exit 1; \
	    distdir=`$(am__cd) $(distdir) && pwd`; \
	    top_distdir=`$(am__cd) $(top_distdir) && pwd`; \
	    (cd $$subdir && \
	      $(MAKE) $(AM_MAKEFLAGS) \
	        top_distdir="$$top_distdir" \
	        distdir="$$distdir/$$subdir" \
		am__remove_distdir=: \
		am__skip_length_check=: \
	        distdir) \
	      || exit 1; \
	  fi; \
	done
	-find $(distdir) -type d ! -perm -777 -exec chmod a+rwx {} \; -o \
	  ! -type d ! -perm -444 -links 1 -exec chmod a+r {} \; -o \
	  ! -type d ! -perm -400 -exec chmod a+r {} \; -o \
	  ! -type d ! -perm -444 -exec $(install_sh) -c -m a+r {} {} \; \
	|| chmod -R a+r $(distdir)
dist-gzip: distdir
	tardir=$(distdir) && $(am__tar) | GZIP=$(GZIP_ENV) gzip -c >$(distdir).tar.gz
	$(am__remove_distdir)

dist-bzip2: distdir
	tardir=$(distdir) && $(am__tar) | bzip2 -9 -c >$(distdir).tar.bz2
	$(am__remove_distdir)

dist-lzma: distdir
	tardir=$(distdir) && $(am__tar) | lzma -9 -c >$(distdir).tar.lzma
	$(am__remove_distdir)

dist-tarZ: distdir
	tardir=$(distdir) && $(am__tar) | compress -c >$(distdir).tar.Z
	$(am__remove_distdir)

dist-shar: distdir
	shar $(distdir) | GZIP=$(GZIP_ENV) gzip -c >$(distdir).shar.gz
	$(am__remove_distdir)

dist-zip: distdir
	-rm -f $(distdir).zip
	zip -rq $(distdir).zip $(distdir)
	$(am__remove_distdir)

dist dist-all: distdir
	tardir=$(distdir) && $(am__tar) | GZIP=$(GZIP_ENV) gzip -c >$(distdir).tar.gz
	$(am__remove_distdir)

# This target untars the dist file and tries a VPATH configuration.  Then
# it guarantees that the distribution is self-contained by making another
# tarfile.
distcheck: dist
	case '$(DIST_ARCHIVES)' in \
	*.tar.gz*) \
	  GZIP=$(GZIP_ENV) gunzip -c $(distdir).tar.gz | $(am__untar) ;;\
	*.tar.bz2*) \
	  bunzip2 -c $(distdir).tar.bz2 | $(am__untar) ;;\
	*.tar.lzma*) \
	  unlzma -c $(distdir).tar.lzma | $(am__untar) ;;\
	*.tar.Z*) \
	  uncompress -c $(distdir).tar.Z | $(am__untar) ;;\
	*.shar.gz*) \
	  GZIP=$(GZIP_ENV) gunzip -c $(distdir).shar.gz | unshar ;;\
	*.zip*) \
	  unzip $(distdir).zip ;;\
	esac
	chmod -R a-w $(distdir); chmod a+w $(distdir)
	mkdir $(distdir)/_build
	mkdir $(distdir)/_inst
	chmod a-w $(distdir)
	dc_install_base=`$(am__cd) $(distdir)/_inst && pwd | sed -e 's,^[^:\\/]:[\\/],/,'` \
	  && dc_destdir="$${TMPDIR-/tmp}/am-dc-$$$$/" \
	  && cd $(distdir)/_build \
	  && ../configure --srcdir=.. --prefix="$$dc_install_base" \
	    $(DISTCHECK_CONFIGURE_FLAGS) \
	  && $(MAKE) $(AM_MAKEFLAGS) \
	  && $(MAKE) $(AM_MAKEFLAGS) dvi \
	  && $(MAKE) $(AM_MAKEFLAGS) check \
	  && $(MAKE) $(AM_MAKEFLAGS) install \
	  && $(MAKE) $(AM_MAKEFLAGS) installcheck \
	  && $(MAKE) $(AM_MAKEFLAGS) uninstall \
	  && $(MAKE) $(AM_MAKEFLAGS) distuninstallcheck_dir="$$dc_install_base" \
	        distuninstallcheck \
	  && chmod -R a-w "$$dc_install_base" \
	  && ({ \
	       (cd ../.. && umask 077 && mkdir "$$dc_destdir") \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" install \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" uninstall \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" \
	            distuninstallcheck_dir="$$dc_destdir" distuninstallcheck; \
	      } || { rm -rf "$$dc_destdir"; exit 1; }) \
	  && rm -rf "$$dc_destdir" \
	  && $(MAKE) $(AM_MAKEFLAGS) dist \
	  && rm -rf $(DIST_ARCHIVES) \
	  && $(MAKE) $(AM_MAKEFLAGS) distcleancheck
	$(am__remove_distdir)
	@(echo "$(distdir) archives ready for distribution: "; \
	  list='$(DIST_ARCHIVES)'; for i in $$list; do echo $$i; done) | \
	  sed -e 1h -e 1s/./=/g -e 1p -e 1x -e '$$p' -e '$$x'
distuninstallcheck:
	@cd $(distuninstallcheck_dir) \
	&& test `$(distuninstallcheck_listfiles) | wc -l` -le 1 \
	   || { echo "ERROR: files left after uninstall:" ; \
	        if test -n "$(DESTDIR)"; then \
	          echo "  (check DESTDIR support)"; \
	        fi ; \
	        $(distuninstallcheck_listfiles) ; \
	        exit 1; } >&2
distcleancheck: distclean
	@if test '$(srcdir)' = . ; then \
	  echo "ERROR: distcleancheck can only run from a VPATH build" ; \
	  exit 1 ; \
	fi
	@test `$(distcleancheck_listfiles) | wc -l` -eq 0 \
	  || { echo "ERROR: files left in build directory after distclean:" ; \
	       $(distcleancheck_listfiles) ; \
	       exit 1; } >&2
check-am: all-am
check: check-recursive
all-am: Makefile $(LTLIBRARIES) $(PROGRAMS) $(MANS) $(DATA) config.h
install-binPROGRAMS: install-libLTLIBRARIES

installdirs: installdirs-recursive
installdirs-am:
	for dir in "$(DESTDIR)$(libdir)" "$(DESTDIR)$(bindir)" "$(DESTDIR)$(sbindir)" "$(DESTDIR)$(man8dir)" "$(DESTDIR)$(pkgconfigdir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-recursive
install-exec: install-exec-recursive
install-data: install-data-recursive
uninstall: uninstall-recursive

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-recursive
install-strip:
	$(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	  install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	  `test -z '$(STRIP)' || \
	    echo "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'"` install
mostlyclean-generic:

clean-generic:
	-test -z "$(CLEANFILES)" || rm -f $(CLEANFILES)

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-rm -f libiptc/$(DEPDIR)/$(am__dirstamp)
	-rm -f libiptc/$(am__dirstamp)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-recursive

clean-am: clean-binPROGRAMS clean-generic clean-libLTLIBRARIES \
	clean-libtool clean-noinstPROGRAMS clean-sbinPROGRAMS \
	mostlyclean-am

distclean: distclean-recursive
	-rm -f $(am__CONFIG_DISTCLEAN_FILES)
	-rm -rf ./$(DEPDIR) libiptc/$(DEPDIR)
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-hdr distclean-libtool distclean-tags

dvi: dvi-recursive

dvi-am:

html: html-recursive

info: info-recursive

info-am:

install-data-am: install-man install-pkgconfigDATA

install-dvi: install-dvi-recursive

install-exec-am: install-binPROGRAMS install-libLTLIBRARIES \
	install-sbinPROGRAMS
	@$(NORMAL_INSTALL)
	$(MAKE) $(AM_MAKEFLAGS) install-exec-hook

install-html: install-html-recursive

install-info: install-info-recursive

install-man: install-man8

install-pdf: install-pdf-recursive

install-ps: install-ps-recursive

installcheck-am:

maintainer-clean: maintainer-clean-recursive
	-rm -f $(am__CONFIG_DISTCLEAN_FILES)
	-rm -rf $(top_srcdir)/autom4te.cache
	-rm -rf ./$(DEPDIR) libiptc/$(DEPDIR)
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-recursive

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-recursive

pdf-am:

ps: ps-recursive

ps-am:

uninstall-am: uninstall-binPROGRAMS uninstall-libLTLIBRARIES \
	uninstall-man uninstall-pkgconfigDATA uninstall-sbinPROGRAMS

uninstall-man: uninstall-man8

.MAKE: $(RECURSIVE_CLEAN_TARGETS) $(RECURSIVE_TARGETS) install-am \
	install-exec-am install-strip

.PHONY: $(RECURSIVE_CLEAN_TARGETS) $(RECURSIVE_TARGETS) CTAGS GTAGS \
	all all-am am--refresh check check-am clean clean-binPROGRAMS \
	clean-generic clean-libLTLIBRARIES clean-libtool \
	clean-noinstPROGRAMS clean-sbinPROGRAMS ctags ctags-recursive \
	dist dist-all dist-bzip2 dist-gzip dist-lzma dist-shar \
	dist-tarZ dist-zip distcheck distclean distclean-compile \
	distclean-generic distclean-hdr distclean-libtool \
	distclean-tags distcleancheck distdir distuninstallcheck dvi \
	dvi-am html html-am info info-am install install-am \
	install-binPROGRAMS install-data install-data-am install-dvi \
	install-dvi-am install-exec install-exec-am install-exec-hook \
	install-html install-html-am install-info install-info-am \
	install-libLTLIBRARIES install-man install-man8 install-pdf \
	install-pdf-am install-pkgconfigDATA install-ps install-ps-am \
	install-sbinPROGRAMS install-strip installcheck \
	installcheck-am installdirs installdirs-am maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	tags tags-recursive uninstall uninstall-am \
	uninstall-binPROGRAMS uninstall-libLTLIBRARIES uninstall-man \
	uninstall-man8 uninstall-pkgconfigDATA uninstall-sbinPROGRAMS


iptables.8: ${srcdir}/iptables.8.in extensions/matches4.man extensions/targets4.man
	${AM_VERBOSE_GEN} sed -e 's/@PACKAGE_AND_VERSION@/${PACKAGE} ${PACKAGE_VERSION}/g' -e '/@MATCH@/ r extensions/matches4.man' -e '/@TARGET@/ r extensions/targets4.man' $< >$@;

ip6tables.8: ${srcdir}/ip6tables.8.in extensions/matches6.man extensions/targets6.man
	${AM_VERBOSE_GEN} sed -e 's/@PACKAGE_AND_VERSION@/${PACKAGE} ${PACKAGE_VERSION}/g' -e '/@MATCH@/ r extensions/matches6.man' -e '/@TARGET@/ r extensions/targets6.man' $< >$@;

.PHONY: tarball
tarball:
	rm -Rf /tmp/${PACKAGE_TARNAME}-${PACKAGE_VERSION};
	pushd ${top_srcdir} && git archive --prefix=${PACKAGE_TARNAME}-${PACKAGE_VERSION}/ HEAD | tar -C /tmp -x && popd;
	pushd /tmp/${PACKAGE_TARNAME}-${PACKAGE_VERSION} && ./autogen.sh && popd;
	tar -C /tmp -cjf ${PACKAGE_TARNAME}-${PACKAGE_VERSION}.tar.bz2 --owner=root --group=root ${PACKAGE_TARNAME}-${PACKAGE_VERSION}/;
	rm -Rf /tmp/${PACKAGE_TARNAME}-${PACKAGE_VERSION};

config.status: extensions/GNUmakefile.in \
	include/xtables.h.in include/iptables/internal.h.in

# Using if..fi avoids an ugly "error (ignored)" message :)
install-exec-hook:
	-if test -z "${DESTDIR}"; then /sbin/ldconfig; fi;
# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
