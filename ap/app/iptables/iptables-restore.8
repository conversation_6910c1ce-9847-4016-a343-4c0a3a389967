.TH IPTABLES-RESTORE 8 "Jan 04, 2001" "" ""
.\"
.\" Man page written by <PERSON> <<EMAIL>>
.\" It is based on the iptables man page.
.\"
.\"	This program is free software; you can redistribute it and/or modify
.\"	it under the terms of the GNU General Public License as published by
.\"	the Free Software Foundation; either version 2 of the License, or
.\"	(at your option) any later version.
.\"
.\"	This program is distributed in the hope that it will be useful,
.\"	but WITHOUT ANY WARRANTY; without even the implied warranty of
.\"	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
.\"	GNU General Public License for more details.
.\"
.\"	You should have received a copy of the GNU General Public License
.\"	along with this program; if not, write to the Free Software
.\"	Foundation, Inc., 675 Mass Ave, Cambridge, MA 02139, USA.
.\"
.\"
.SH NAME
iptables-restore \- Restore IP Tables
.SH SYNOPSIS
.BR "iptables-restore " "[-c] [-n]"
.br
.SH DESCRIPTION
.PP
.B iptables-restore
is used to restore IP Tables from data specified on STDIN. Use 
I/O redirection provided by your shell to read from a file
.TP
\fB\-c\fR, \fB\-\-counters\fR
restore the values of all packet and byte counters
.TP
\fB\-n\fR, \fB\-\-noflush\fR 
don't flush the previous contents of the table. If not specified, 
.B iptables-restore
flushes (deletes) all previous contents of the respective IP Table.
.SH BUGS
None known as of iptables-1.2.1 release
.SH AUTHOR
Harald Welte <<EMAIL>>
.SH SEE ALSO
.BR iptables-save "(8), " iptables "(8) "
.PP
The iptables-HOWTO, which details more iptables usage, the NAT-HOWTO,
which details NAT, and the netfilter-hacking-HOWTO which details the
internals.
