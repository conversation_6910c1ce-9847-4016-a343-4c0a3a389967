/**
 * @file zte_web_util.c
 * @brief goform mesage processing
 *
 * Copyright (C) 2017 Sanechips Technology Co., Ltd.
 * <AUTHOR> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 *
 */


/*******************************************************************************
 *                           Include header files                              *
 ******************************************************************************/

#include	<stdio.h>
#include	<stdlib.h>
#include	<sys/ioctl.h>
#include	<arpa/inet.h>
#include	<net/if.h>
#include	<net/route.h>
#ifdef CONFIG_DEFAULTS_KERNEL_2_6_21
#include	<linux/types.h>
#include	<linux/socket.h>
#endif
#include    <string.h>
#include    <dirent.h>
#include	"webs.h"
//#include	"linux/autoconf.h"
#include    <sys/time.h>
#include    <signal.h>
#include    "zte_web_interface.h"
#include    "zte_web_get_fw_para.h"

/*******************************************************************************
 *                             Macro definitions                               *
 ******************************************************************************/

#define MAX_MOBILE_KEY_WORD_SIZE    20  /*�ؼ��ֳ����ݶ�20*/
#define MAX_PAGE_PATH_SIZE          128
//#define MAX_GOFORM_TABLE_SIZE       256


// 根据IP地址获取对应的MAC地址
int get_client_mac_by_ip(const char *ip, char *mac_buf, int buf_size)
{
	FILE *fp;
	char line[256];
	char arp_ip[32], arp_mac[32];
	int found = 0;

	if (!ip || !mac_buf || buf_size < 18) {
		return -1;
	}

	// 读取ARP表获取MAC地址
	fp = fopen("/proc/net/arp", "r");
	if (!fp) {
		printf("corem failed to open /proc/net/arp\n");
		return -1;
	}

	// 跳过标题行
	if (fgets(line, sizeof(line), fp) == NULL) {
		fclose(fp);
		return -1;
	}

	// 逐行读取ARP表
	while (fgets(line, sizeof(line), fp) != NULL) {
		// ARP表格式: IP地址 HW类型 标志 HW地址 掩码 设备
		if (sscanf(line, "%31s %*s %*s %31s", arp_ip, arp_mac) == 2) {
			if (strcmp(arp_ip, ip) == 0) {
				strncpy(mac_buf, arp_mac, buf_size - 1);
				mac_buf[buf_size - 1] = '\0';
				found = 1;
				break;
			}
		}
	}

	fclose(fp);
	return found ? 0 : -1;
}

#ifdef ZXIC_ONELINK_TEST

#define TEST_ONELINK_REGISTER_PAGE "https://wireless.cmonelink.com/wirelessAuthentication/authentication?terminalMac=12-34-56-78-FF-BB&mac=00-1A-2B-3d-4D-5E&iccid=898604891523D0022173&redirectTo=https://www.baidu.com"

const char* zte_web_get_onelink_register_page(webs_t wp)
{
	static char onelink_url[512] = {0};
	char terminal_mac[32] = {0};
	char wifi_mac[32] = {0};
	char iccid[32] = {0};

	// 获取终端真实MAC地址（从wp参数中的IP地址解析）
	if (wp && wp->ipaddr) {
		if (get_client_mac_by_ip(wp->ipaddr, terminal_mac, sizeof(terminal_mac)) != 0) {
			// 如果无法获取，使用默认值
			strcpy(terminal_mac, "12-34-56-78-FF-BB");
		} else {
			// 转换MAC地址格式为 xx-xx-xx-xx-xx-xx
			char *p = terminal_mac;
			while (*p) {
				if (*p == ':') *p = '-';
				p++;
			}
		}
	} else {
		strcpy(terminal_mac, "12-34-56-78-FF-BB");
	}

	// 获取mifi路由器MAC地址（通过nv wifi_mac）
	if (cfg_get_item("wifi_mac", wifi_mac, sizeof(wifi_mac)) != 0) {
		// 尝试备用的NV项
		if (cfg_get_item("at_wifi_mac", wifi_mac, sizeof(wifi_mac)) != 0) {
			// 如果都无法获取，使用默认值
			strcpy(wifi_mac, "00-1A-2B-3d-4D-5E");
		}
	}

	// 转换MAC地址格式为 xx-xx-xx-xx-xx-xx
	if (strlen(wifi_mac) > 0) {
		char *p = wifi_mac;
		while (*p) {
			if (*p == ':') *p = '-';
			p++;
		}
	}

	// 获取ICCID（通过nv ziccid）
	if (cfg_get_item("ziccid", iccid, sizeof(iccid)) != 0) {
		// 如果无法获取，使用默认值
		strcpy(iccid, "898604891523D0022173");
	}

#if defined(QRZL_ONE_LINK_CUSTOMER_MY)
	char one_link_customers_get_token_url[512] = {0};
	cfg_get_item("ONE_LINK_customers_get_token_url", one_link_customers_get_token_url, sizeof(one_link_customers_get_token_url));

    // MAC - 转换wifi_mac为无分隔符格式
    char convered_mac[33] = {0};
    convert_mac_format(wifi_mac, convered_mac, sizeof(convered_mac), '\0');

    // terminalMac - 转换terminal_mac为无分隔符格式
    char convered_terminalMac[33] = {0};
    convert_mac_format(terminal_mac, convered_terminalMac, sizeof(convered_terminalMac), '\0');

    // iccid
    char current_iccid[22] = {0};
	snprintf(current_iccid, sizeof(current_iccid), "%s", iccid);

	char sn[22] = {0};
    cfg_get_item("sn", sn, sizeof(sn));

	char sim_select[22] = {0};
    cfg_get_item("sim_select", sim_select, sizeof(sim_select));

    // sequence
    char sequence[2] = {0};
	if(strcmp(sim_select, "RSIM_only") == 0) {
		snprintf(sequence, sizeof(sequence), "%s", "3");
	} else if(strcmp(sim_select, "ESIM1_only") == 0) {
		snprintf(sequence, sizeof(sequence), "%s", "1");
	} else if(strcmp(sim_select, "ESIM2_only") == 0) {
		snprintf(sequence, sizeof(sequence), "%s", "2");
	} else {
		snprintf(sequence, sizeof(sequence), "%s", "0");
	}

    char lan_ip[30] = {0};
    cfg_get_item("lan_ipaddr", lan_ip, sizeof(lan_ip));

    snprintf(onelink_url, sizeof(onelink_url), "%s/boss-web/wx-login.html?mac=%s&terminalMac=%s&phoneNum=%s&iccid=%s&sn=%s&sequence=%s&deviceIp=http://%s/Api/codeToken",
                one_link_customers_get_token_url, convered_mac, convered_terminalMac, "", current_iccid, sn, sequence , lan_ip);

    printf("corem OneLink MY URL: %s\n", onelink_url);
#else
	// 构造OneLink认证页面URL
	snprintf(onelink_url, sizeof(onelink_url),
		"https://wireless.cmonelink.com/wirelessAuthentication/authentication?terminalMac=%s&mac=%s&iccid=%s&redirectTo=https://www.baidu.com",
		terminal_mac, wifi_mac, iccid);

	printf("corem OneLink URL: %s\n", onelink_url);
#endif


	return onelink_url;
}
#endif

/*******************************************************************************
 *                        Local function declarations                          *
 ******************************************************************************/
static int getSysUptime(int eid, webs_t wp, int argc, char_t **argv);
static int zte_goform_whitelist_check(webs_t wp);
static int zte_goform_blacklist_check(webs_t wp);
/*******************************************************************************
 *                         Local variable definitions                          *
 ******************************************************************************/
/****Define the HEADER and FOOTER of web page****/
static const char * const zte_web_page_header =
    "<head>"
    "<title>-</title>"
    "</head>"
    "<body bgcolor=white>"
    "<p>"
    "<font size=3 face=arial>"
    "<p>"
    "<font size=2 face=arial>"
    ;

static const char * const zte_web_page_footer =
    "<p>"
    "<p>"
    "<font face=arial></b></font><br>"
    "<script> "
    " timer = setTimeout('location.replace(\"%s\")', 100) "
    "</script> "
    "<p>"
    "</body>"
    ;

static const char * const zte_web_page_top =
    "<p>"
    "<p>"
    "<font face=arial></b></font><br>"
    "<script> "
    " timer = setTimeout('top.location.replace(\"%s\")', 100) "
    "</script> "
    "<p>"
    "</body>"
    ;
//multi_dataΪ�գ����ѵ�½���ܻ�ȡ��,���б������������Ӿ�����NV
static web_goform_type web_goform_get_cmd_table[] = {
	/**********  ��ͬ ***********/
	//{CMD_GET_DEVICE_MODE, zte_get_device_mode},//debug_enable
	//{CMD_GET_POWERON_TIME, zte_get_poweron_time},//����ʱ��
	//{CMD_GET_LAN_DEV_INFO, zte_get_lan_dev_info},//��ȡ������Ϣ
	{CMD_GET_USER_MAC_ADDR, zte_lan_user_mac_get},
	{CMD_CONNECTION_MODE, zte_get_connection_mode},
	/**********  PBM module  ***********/
	{CMD_PBM_DATA_INFO, zte_get_pbm_data},
	{CMD_PBM_DATA_TOTAL, zte_get_pbm_data_total},
	{CMD_PBM_CAPACITY_INFO, zte_get_pbm_parameter_info},
	/**********  sms module  ***********/
	{CMD_SMS_PAGE_DATA, zte_get_sms_data},
	{CMD_SMS_DATA_TOTAL, zte_get_sms_data_total},
	{CMD_SMS_PARAMETER_INFO, zte_get_sms_parameter_info},
	{CMD_SMS_STATUS_INFO, zte_get_sms_cmd_status_info},
	{CMD_SMS_CAPACITY_INFO, zte_get_sms_capacity_info},
	{CMD_SMS_STATUS_RPT_DATA, zte_get_sms_status_rpt_data},
	{CMD_SMS_UNREAD_COUNT, zte_get_sms_unread_count},
	{CMD_BROADCAST_DATA, zte_get_boradcast_data},
	/**********  wifi module  ***********/
	{CMD_WIFI_STATION_LIST, zte_wlan_get_station_list},
	{CMD_WIFI_WPS_AP_PIN, zte_wlan_get_wps_pin},
	{CMD_WIFI_WPS_AP_DEF_PIN, zte_wlan_get_wps_defpin},
	/**********  net module  ***********/
	{CMD_HOSTNAME_LIST, zte_get_hostnamelist},
	{CMD_CURRENT_STATICADDR_LIST, zte_get_current_static_addr_list},
	{CMD_LAN_STATION_LIST, zte_get_lan_station_list},
	{CMD_CHILDREN_DEVICE_LIST, zte_get_children_device_list},
	{CMD_WHITE_SITE_LIST, zte_get_white_site_list},
	/**********  DDNS  ***********/
	//{CMD_GETDDNS_STATUS, zte_get_ddns_status},
	/**********  USSD  ***********/
	{CMD_USSD_DATA_INFO, zte_get_ussd_data_info},
	/**********  FOTA  ***********/
	{FOTA_UPGRADE_RESULT, zte_fota_get_upgrade_result}, //��ȡ����Ҫ���⴦�� �����Բ��ܸĳ�ֱ�Ӷ�NV

	/**********  httpshare  ***********/
	{CMD_HTTPSHARE_GETCARD_VAULE, zte_httpShare_getcard_value},
	{CMD_HTTPSHARE_GETCARD_NMEA, zte_httpShare_getcard_name},
	{CMD_HTTPSHARE_AUTH_GET, zte_httpShare_auth_get},
#if defined(JCV_FEATURE_ESIM_RSIM_SWITCH) || defined(JCV_FEATURE_SIM_HOTSWAP_SUPPORT)
	/********** get esim switch, iccid lock  ***********/
	{GOFORM_GET_SIM_SELECT, zte_get_sim_select},
	{GOFORM_GET_ICCID_LOCK_INFO, zte_get_iccid_lock_info},
	{GOFORM_GET_ESIM_STATUS, zte_get_esim_status},
#endif
};


/*******************************************************************************
*                        Global variable definitions                          *
******************************************************************************/
//extern web_goform_type web_goform_set_cmd_table[MAX_GOFORM_TABLE_SIZE];
web_goform_type web_goform_set_cmd_table[] = {
	/**********  management module ***********/
	{GOFORM_MGMT_SET_WEB_LANGUAGE, zte_mgmt_set_language},                      //webui������ʾ����
#ifndef WEBS_SECURITY
	{GOFORM_MGMT_SET_DEVICEMODE, zte_mgmt_set_devicemode},                      //����ģʽ
#endif
	{GOFORM_MGMT_LOGIN_IP, zte_mgmt_login},                                     //��½
	{GOFORM_MGMT_LOGOUT_IP, zte_mgmt_logout},                                   //�ǳ�
	{GOFORM_MGMT_CHANGE_PASSWORD, zte_mgmt_change_password},                    //�����޸�
	{GOFORM_MGMT_CHANGE_ACCOUNT, zte_mgmt_change_account},                      //�˺��޸�
	{GOFORM_MGMT_QUICK_SETUP, zte_quick_setup},                                 //��������
	{GOFORM_MGMT_RESTORE_FACTORY_SETTINGS, zte_mgmt_restore},                   //�ָ���������
	{GOFORM_MGMT_REBOOT, zte_goform_mgmt_reboot_process},                       //REBOOT
	{GOFORM_MGMT_POWEROFF, zte_mgmt_poweroff},                                  //poweroff
	{GOFORM_MGMT_POWER_ON_SPEED, zte_mgmt_control_power_on_speed},              //���ٿ��ػ�����
	//{GOFORM_MGMT_SET_EXTERNAL_NV, zte_goform_set_external_nv},
	{GOFORM_MGMT_SNTP, zte_goform_mgmt_sntp_process},                           //SNTP����
	{GOFORM_SNTP_GETDATASTATIC, zte_goform_sntp_getdatastatic_process},         //����serverУ׼ʱ��
	//{GOFORM_MGMT_SYSLOG, zte_goform_mgmt_syslog_process},                       //ϵͳLOG

	/**********  wan module start***********/
	{GOFORM_WAN_SET_NETWORK, zte_wan_set_network},                              //�ֶ�����ע��
	{GOFORM_WAN_SET_CONNECTION_MODE, zte_wan_set_connection_mode},              //���ò���ģʽ
	{GOFORM_WAN_CONNECT_NETWORK, zte_wan_connect_network},                      //PDP ��������
	{GOFORM_WAN_DISCONNECT_NETWORK, zte_wan_disconnect_network},                //PDP ���ŶϿ�����
	{GOFORM_WAN_SET_BEARER_PREFERENCE, zte_wan_network_select},                 //����������ʽMSG_CMD_NET_SELECT_REQ
	{GOFORM_WAN_SCAN_NETWORK, zte_wan_scan_network},                            //�ֶ�����
	{GOFORM_WAN_UNLOCK_NETWORK, zte_mgmt_unlock_network},                       //��������
	//{GOFORM_WAN_LOCK_FREQUENCY, zte_wan_lock_frequency_process},                //LTE���������Ƶ����AT��δ����
	/**********  apn module ***********/
	{GOFORM_WAN_APN_PROC_EX, zte_form_multi_apn_proc_ex},                        //APN����
	/**********  pin,puk module ***********/
	{GOFORM_MGMT_ENTER_PIN, zte_mgmt_pin_input},                                 //��֤PIN������
	{GOFORM_MGMT_DISABLE_PIN, zte_mgmt_disable_pin},                             //�ر�PIN
	{GOFORM_MGMT_ENABLE_PIN, zte_mgmt_pin_enable_or_modify},                     //�������޸�PIN
	{GOFORM_MGMT_ENTER_PUK, zte_mgmt_puk_input},                                 //PUK
	{GOFORM_MGMT_AUTO_PIN, zte_mgmt_auto_pin},                                   //�Զ�PIN
	/**********  statistics module ***********/
	{GOFORM_WAN_DATA_LIMIT_SETTING, zte_wan_data_limit_setting},                    //������������
	{GOFORM_WAN_DATA_FLOW_CALIBRATION_MANUAL, zte_wan_data_flow_calibration_manual},//�ֶ�У׼����
	{GOFORM_WAN_RESET_DATA_COUNTER, zte_wan_data_statistics_clear_process},         //���������Ϣ
	/**********  ussd module ***********/
	{GOFORM_USSD_PROCESS, zte_goform_ussd_process},                               //USSD����
	/**********  wan module end***********/

	/**********  wifi module ***********/
	{GOFORM_WLAN_SET, zte_wlan_basic_set},                                        //WIFI ��������
	{GOFORM_WLAN_MAC_FILTER, zte_wlan_mac_filter_set},                            //����mac���˹���
	{GOFORM_WLAN_WPS_SET, zte_wlan_wps_mode_set},                                 //WPS
	{GOFORM_WLAN_SSID1_SET, zte_wlan_web_ssid1_set},                              //SSID1
	{GOFORM_WLAN_SSID2_SET, zte_wlan_ssid2_set},                                  //SSID2
	{GOFORM_WLAN_WIFI_SLEEP_SET, zte_wlan_sleep_set},                             //����
	{GOFORM_WLAN_SET_TSW, zte_wlan_set_tsw},                                      //��ʱ���߻���
	{GOFORM_WLAN_WIFI_COVERAGE_SET, zte_wlan_coverage_set},                       //���Ƿ�Χ
	{GOFORM_WLAN_WIFI_STA_CONTROL, zte_wlan_station_set},                         //wifi station
	{GOFORM_WLAN_WIFI_SPOT_PROFILE_UPDATE, zte_wlan_station_spot_profile_update}, //wifi station
	{GOFORM_WLAN_SET_STA_CON, zte_wlan_station_connect},                          //wifi station
	{GOFORM_WLAN_SET_STA_DISCON, zte_wlan_station_disconnect},                    //wifi station
	{GOFORM_WLAN_SET_STA_REFRESH, zte_wlan_station_refresh},                      //wifi station

	/**********  router module ***********/
	{GOFORM_ROUTER_DEL_IP_PORT_FILETER, zte_fw_ipport_filter_del},
	{GOFORM_ROUTER_ADD_IP_PORT_FILETER_V4V6, zte_fw_ipport_filter_add},           /*for v4 and v6*/
	{GOFORM_ROUTER_DEL_IP_PORT_FILETER_V4V6, zte_fw_ipport_filter_del_v4_v6},     /*for v4 and v6*/
	{GOFORM_ROUTER_ADD_PORT_FORWARE, zte_fw_port_forward_add},
	{GOFORM_ROUTER_DEL_PORT_FORWARE, zte_fw_port_forward_del},
	{GOFORM_ROUTER_ADD_PORT_MAP, zte_fw_port_map_add},
	{GOFORM_ROUTER_DEL_PORT_MAP, zte_fw_port_map_del},
	{GOFORM_ROUTER_BASIC_SETTING, zte_fw_basic_setting},
	{GOFORM_ROUTER_FORWARD_SETTING, zte_fw_forward_setting},
	{GOFORM_ROUTER_SYSTEM_SECURITY, zte_fw_sys_security_setting},
	{GOFORM_ROUTER_DHCP_SETTING, zte_dhcp_set},
	{GOFORM_ROUTER_STATIC_DHCP_SETTING, zte_static_dhcp_set},
	{GOFORM_ROUTER_UPNP_SETTING, zte_fw_upnp_set},                                //CPE�ں���·��������
	{GOFORM_ROUTER_DMZ_SETTING, zte_fw_dmz},
	{GOFORM_ROUTER_EDIT_HOSTNAME, zte_edit_hostname},
	{GOFORM_URL_FILTER_DELETE, zte_goform_url_filter_delete_process},             //URL ����
	{GOFORM_URL_FILTER_ADD, zte_goform_url_filter_add_process},
	{GOFORM_BIND_STATIC_ADDRESS_SET, zte_set_bind_static_address},
	{GOFORM_BIND_STATIC_ADDRESS_ADD, zte_bind_static_address_add},
	{GOFORM_BIND_STATIC_ADDRESS_DEL, zte_bind_static_address_del},
	{GOFORM_ADD_CHILDREN_DEVICE, zte_add_children_device},                        //�ҳ�����
	{GOFORM_DEL_CHILDREN_DEVICE, zte_del_children_device},
	{GOFORM_ADD_WHITE_SITE, zte_add_white_site},
	{GOFORM_REMOVE_WHITE_SITE, zte_remove_white_site},
	{GOFORM_PARENT_CONTROL_SET, zte_parent_control_set},
	//{GOFORM_DDNS, zte_goform_ddns},
	//{GOFORM_DNS_MODE_SET, zte_dns_mode_set},									 //�����ֶ�DNS mode
	//{GOFORM_DNS_SERVER_SET, zte_dns_server_set},								 //�����ֶ�DNS server
	/**********  wan pppoe ***********/
	{GOFORM_SET_OPERATION_MODE, zte_goform_set_operation_mode},                   //RJ45ģʽ�л�����
	{GOFORM_SET_WAN_GATEWAYMODE_PPPOE, zte_goform_set_wan_gatewaymode_PPPOE},
	{GOFORM_SET_WAN_GATEWAYMODE_DHCP, zte_goform_set_wan_gatewaymode_DHCP},
	{GOFORM_SET_WAN_GATEWAYMODE_STATIC, zte_goform_set_wan_gatewaymode_STATIC},
	{GOFORM_SET_WAN_GATEWAYMODE_AUTO, zte_goform_set_wan_gatewaymode_AUTO},

	/**********  httpShare module ***********/
	{GOFORM_HTTPSHARE_ENTERFOLD, zte_httpShare_enterFold},                        //get SD's filelist
	{GOFORM_HTTPSHARE_NEW, zte_httpShare_new},                                    //creat a new folder
	{GOFORM_HTTPSHARE_DEL, zte_httpShare_del},                                    //delete file or folder
	{GOFORM_HTTPSHARE_AUTH_SET, zte_httpShare_auth_set},                          //httpshare setting
	{GOFORM_HTTPSHARE_MODE_SET, zte_httpShare_modeset},                           //set SD card's mode:usb mode or httpshare mode
	{GOFORM_HTTPSHARE_FILE_RENAME, zte_httpShare_rename},                         //rename the file
	{GOFORM_HTTPSHARE_CHECK_FILE, zte_httpShare_check_file},                      //check file exists

	/**********  PBM module ***********/
	{GOFORM_PBM_CONTACT_ADD, zte_goform_pbm_contact_add_process},
	{GOFORM_PBM_CONTACT_DEL, zte_goform_pbm_contact_del_process},

	/**********  sms module ***********/
	{GOFORM_SMS_SET_MSG_CENTER, zte_sms_set_message_center},
	{GOFORM_SMS_DELETE_SMS, zte_sms_delete_msg_process},
	{GOFORM_SMS_DELETE_SMS_ALL, zte_goform_sms_alldel_msg_process},
	{GOFORM_SMS_MOVE_TO_SIM, zte_sms_move_to_sim_msg_process},
	{GOFORM_SMS_SAVE_SMS, zte_sms_save_msg_process},
	{GOFORM_SMS_SEND_SMS, zte_sms_send_msg_process},
	{GOFORM_SMS_SET_MSG_READ, zte_sms_view_msg_process},

	/**********  fota module ***********/
	{GOFORM_SET_FOTAAUTOUPDATE, zte_fota_update},                                  //�û�ѡ���Ƿ����������������ȡ��
	{GOFORM_SET_FOTASETTINGS, zte_fota_settings},                                  //�Զ����

	/**********  ping test   ***********/
	//{GOFORM_PING_DIAGNOSTICS_START, zte_ping_diagnostics_start},					// ping����⹦�ܿ���
	//{GOFORM_PING_DIAGNOSTICS_STOP, zte_ping_diagnostics_stop},						// ping����⹦�ܹر�

	/********** �ͻ����ο����ӿ������������� ***********/
	{GOFORM_SET_SAMPLE, zte_goform_set_sample},                                    //�ͻ����ο����ӿ�ʾ��
#ifdef ENABLE_QRZL_APP
	{GOFORM_SET_NET_BAND, qrzil_app_set_band},
	{GOFORM_ALK_LOGIN, qrzl_app_alk_login},
	{GOFORM_ALK_SET_SSID_PASSWORD, qrzl_wlan_web_ssid1_set},
#endif
#if defined(JCV_FEATURE_ESIM_RSIM_SWITCH) || defined(JCV_FEATURE_SIM_HOTSWAP_SUPPORT)
	/********** set esim switch  and iccid lock***********/
	{GOFORM_SET_SIM_SELECT, zte_mgmt_set_sim_select_switch},
	{GOFORM_UNLOCK_SIM_SELECT, zte_mgmt_unlock_sim_select_switch},
	{GOFORM_SET_ICCID_LOCK_INFO, zte_mgmt_set_iccid_lock_info},
#endif
};

/*******************************************************************************
 *                      Local function implementations                         *
 ******************************************************************************/

//WH://��ȡϵͳ���һ���ϵ���������ʱ��
static int getSysUptime(int eid, webs_t wp, int argc, char_t **argv)
{
	struct tm *utime;
	time_t usecs;

	char syn_system_total[20] = {0};
	char syn_after_time[20] = {0};
	int ltime = 0;
	int ntime = 0;

	cfg_get_item("syn_system_total", syn_system_total, sizeof(syn_system_total));

	sscanf(syn_system_total, "%d", &ltime);
	if(ltime < 0 || ltime >  INT_MAX-1)  // kw 3
	{
		ltime = 0;
	}	
	ltime += JAN_2000;

	cfg_get_item("syn_after_time", syn_after_time, sizeof(syn_after_time));

	sscanf(syn_after_time, "%d", &ntime);
	if(ntime < 0 || ntime >  INT_MAX-1)  // kw 3
	{
		ntime = 0;
	}	
	time(&usecs);
	if (ntime == 0) {
		utime = localtime(&usecs);
	} else {
		time_t use;
		use = (time_t)(usecs - ntime + ltime);
		utime = localtime(&use);
	}
	if(utime == NULL)
		return -1;
	if (utime->tm_yday > 0) {
		return websWrite(wp, T("%d day%s, %d hour%s, %d min%s, %d sec%s"),
		                 utime->tm_yday, (utime->tm_yday == 1) ? "" : "s",
		                 utime->tm_hour, ((utime->tm_hour == 1) || (utime->tm_hour == 0)) ? "" : "s",
		                 utime->tm_min, ((utime->tm_min == 1) || (utime->tm_min == 0)) ? "" : "s",
		                 utime->tm_sec, ((utime->tm_sec == 1) || (utime->tm_sec == 0)) ? "" : "s");
	} else if (utime->tm_hour > 0) {
		return websWrite(wp, T("%d hour%s, %d min%s, %d sec%s"),
		                 utime->tm_hour, (utime->tm_hour == 1) ? "" : "s",
		                 utime->tm_min, ((utime->tm_min == 1) || (utime->tm_min == 0)) ? "" : "s",
		                 utime->tm_sec, ((utime->tm_sec == 1) || (utime->tm_sec == 0)) ? "" : "s");
	} else if (utime->tm_min > 0) {
		return websWrite(wp, T("%d min%s, %d sec%s"),
		                 utime->tm_min, (utime->tm_min == 1) ? "" : "s",
		                 utime->tm_sec, ((utime->tm_sec == 1) || (utime->tm_sec == 0)) ? "" : "s");
	} else {
		return websWrite(wp, T("%d sec%s"),
		                 utime->tm_sec, ((utime->tm_sec == 1) || (utime->tm_sec == 0)) ? "" : "s");
	}

}

static int zte_goform_whitelist_check(webs_t wp)
{

	char login_info[NV_ITEM_STRING_LEN_20] = {0};
	char_t nv_ipaddr[40] = {0};
	//zte_topsw_state_e_type status = ZTE_NVIO_MAX;
	char_t *ip_address = NULL;

	int result = FALSE;

	zte_web_read(NV_LOGINFO, login_info);

	//get request ip addr
	ip_address = websGetRequestIpaddr(wp);
#if 0 // kw 3 	 NVARIANT_CONDITION.UNREACH
	if (NULL == ip_address) {
		slog(MISC_PRINT, SLOG_ERR, "zte_goform_whitelist_check: ip_address is null.\n"); /*lint !e26*/
		return FALSE;
	}
#endif	
	(void)zte_web_read(NV_USER_IP_ADDR, nv_ipaddr);
	if ((0 == strncmp(login_info, "ok", sizeof(login_info))) && (0 == strncmp(ip_address, nv_ipaddr, sizeof(nv_ipaddr)))) {
		result = TRUE; //to update the timemark
	}
	if (result == TRUE) {
		if (!zte_mgmt_login_timemark_set()) {
			slog(MISC_PRINT, SLOG_ERR, "[ERROR]zte_goform_whitelist_check -> timemark set error .\n"); /*lint !e26*/
		}
	}

	return result;
}

/******************************************************
* Function: zte_goform_blacklist_check
* Description:goform entry for handling set cmd
* Input:   HTTP Request Info
* Output:
* Return:
* Others:
* Modify Date       Version    Author         Modification
* 2013/01/21      V1.0        Liuyingnan          create
*******************************************************/
static int zte_goform_blacklist_check(webs_t wp)
{
	//char login_info[NV_ITEM_STRING_LEN_20] = {0};
	char_t *zte_goform_id = NULL;
	//zte_web_read(NV_LOGINFO, login_info);

	zte_goform_id = websGetVar(wp, T("goformId"), T(""));
	if (zte_goform_id == NULL) {
		slog(MISC_PRINT, SLOG_ERR, "zte_goform_whitelist_check: zte_goform_id is null.\n"); /*lint !e26*/
		return -1;
	}

	if ((0 == strncmp(GOFORM_MGMT_SET_WEB_LANGUAGE, zte_goform_id, strlen(GOFORM_MGMT_SET_WEB_LANGUAGE)))
	    || (0 == strncmp(GOFORM_MGMT_LOGIN_IP, zte_goform_id, strlen(GOFORM_MGMT_LOGIN_IP)))
	    || (0 == strncmp(GOFORM_MGMT_ENTER_PIN, zte_goform_id, strlen(GOFORM_MGMT_ENTER_PIN)))
	    || (0 == strncmp(GOFORM_MGMT_ENTER_PUK, zte_goform_id, strlen(GOFORM_MGMT_ENTER_PUK)))
	    || (0 == strncmp(GOFORM_HTTPSHARE_CHECK_FILE, zte_goform_id, strlen(GOFORM_HTTPSHARE_CHECK_FILE)))
	    || (0 == strncmp(GOFORM_HTTPSHARE_ENTERFOLD, zte_goform_id, strlen(GOFORM_HTTPSHARE_ENTERFOLD)))
	    || (0 == strncmp(GOFORM_HTTPSHARE_FILE_RENAME, zte_goform_id, strlen(GOFORM_HTTPSHARE_FILE_RENAME)))
	    || (0 == strncmp(GOFORM_HTTPSHARE_NEW, zte_goform_id, strlen(GOFORM_HTTPSHARE_NEW)))
	    || (0 == strncmp(GOFORM_HTTPSHARE_DEL, zte_goform_id, strlen(GOFORM_HTTPSHARE_DEL)))
		|| (0 == strncmp(GOFORM_ALK_LOGIN, zte_goform_id, strlen(GOFORM_ALK_LOGIN)))
	    || (0 == strncmp(GOFORM_ALK_SET_SSID_PASSWORD, zte_goform_id, strlen(GOFORM_ALK_SET_SSID_PASSWORD)))
#if defined(JCV_FEATURE_ESIM_RSIM_SWITCH) || defined(JCV_FEATURE_SIM_HOTSWAP_SUPPORT)
	    || (0 == strncmp(GOFORM_SET_SIM_SELECT, zte_goform_id, strlen(GOFORM_SET_SIM_SELECT)))
	    || (0 == strncmp(GOFORM_SET_ICCID_LOCK_INFO, zte_goform_id, strlen(GOFORM_SET_ICCID_LOCK_INFO)))
#endif
#ifndef WEBS_SECURITY
	    || (0 == strncmp(GOFORM_MGMT_LOGOUT_IP, zte_goform_id, strlen(GOFORM_MGMT_LOGOUT_IP)))
	    || (0 == strncmp(GOFORM_MGMT_SET_DEVICEMODE, zte_goform_id, strlen(GOFORM_MGMT_SET_DEVICEMODE)))
	    || (0 == strncmp(GOFORM_MGMT_REBOOT, zte_goform_id, strlen(GOFORM_MGMT_REBOOT)))
#endif
	    ) {
		slog(MISC_PRINT, SLOG_DEBUG, "zte_goform_whitelist_check: zte_goform_id:[%s] is OK.\n", zte_goform_id); /*lint !e26*/
		return 1;
	} else {
		slog(MISC_PRINT, SLOG_ERR, "zte_goform_whitelist_check: zte_goform_id:[%s] is black.\n", zte_goform_id); /*lint !e26*/
		return -1;
	}

}

static int isMobileReq(webs_t wp)
{
	int  iMobileKW = 0;
	const char MOBILE_KEYWORD[][MAX_MOBILE_KEY_WORD_SIZE] = {"Mobile", "mobile", "MOBILE"};

	if (wp->userAgent == NULL) {
		return 0;
	}
	for (iMobileKW = 0; iMobileKW < sizeof(MOBILE_KEYWORD) / MAX_MOBILE_KEY_WORD_SIZE; iMobileKW++) {
		if (strstr(wp->userAgent, MOBILE_KEYWORD[iMobileKW]) != NULL) {
			return 1;
		}
	}
	return 0;
}
static int isPageExist(char *page)
{
	char *defaultDir    = websGetDefaultDir();
	char *fullPagePath  = NULL;
	int  fullPathSize   = 0;
	int  hasPageExist   = 0;

	if (defaultDir == NULL || page == NULL) {
		return 0;
	}
	fullPathSize = strlen(defaultDir) + strlen(page) + 2;
	fmtAlloc(&fullPagePath, fullPathSize, T("%s/%s"), defaultDir, page);
	hasPageExist = (access(fullPagePath, F_OK) == 0);
	bfreeSafe(B_L, fullPagePath);
	return hasPageExist;
}

int is_print_str(char *str, int len)
{
	int i = 0;
	if (str == NULL || len <= 0 || strlen(str) < len) {
		return 0;
	}
	for (i = 0; i < len; i++) {
		if (!isprint(str[i])) {
			return 0;
		}
		if(str[i] == '%' && (i+2) < len && isxdigit(str[i+1]) && isxdigit(str[i+2]))
		{
			if(str[i+1] < '2' || str[i+1] > '7' ||(str[i+1] == '7' && (str[i+2] == 'f' || str[i+2] == 'F')))
			{
				return 0;
			}
		}
	}
	return 1;
}

/*******************************************************************************
 *                      Global function implementations                        *
 ******************************************************************************/

/******************************************************
* Function: void zte_web_init()
* Description:  the entry of zte code in goahead
* Input:
* Output:
* Return:
* Others:
* Modify Date    Version   Author         Modification
* 2010/8/5         V1.0     LiuWei        create
* 2010/11/4        V1.1     ZYT           modify
*******************************************************/
void zte_web_init()
{
	cfg_set(NV_LOGINFO, "");
	websFormDefine(T("proc_get"), zte_goform_get_cmd_process);
	websFormDefine(T("proc_post"), zte_goform_set_cmd_process);
	zte_init_login_psw_time(); //WH:��ʼ������½����
}

/*
 * description: parse va and do system
 */
#if 0 
int doSystem(char_t *fmt, ...)
{
	va_list	vargs;
	char_t	*cmd = NULL;
	int	rc = 0;

	va_start(vargs, fmt);/*lint !e530*/
	if (fmtValloc(&cmd, WEBS_BUFSIZE, fmt, vargs) >= WEBS_BUFSIZE) {
		trace(0, T("doSystem: lost data, buffer overflow\n"));
	}
	va_end(vargs);

	if (cmd) {
		trace(0, T("%s\n"), cmd);
		rc = system(cmd);
		bfree(B_L, cmd);
	}
	return rc;
}
#endif
/******************************************************
* Function: void zte_webs_feedback_top(webs_t wp, char *fileName)
* Description:  feed back web page at top location
* Input:
* Output:
* Return:
* Others:
* Modify Date    Version   Author         Modification
* 2010/9/10         V1.0     LiuWei        create
* 2010/11/15        V1.1     zyt           modify
*******************************************************/
void zte_webs_feedback_top(webs_t i_wp, char *i_pageName)
{
	char dest_page[64] = {0};
	char language[64] = {0};

	if (NULL == i_pageName) {
		slog(MISC_PRINT, SLOG_ERR, "zte_webs_feedback_top para null.\n"); /*lint !e26*/
		return;
	}

	cfg_get_item(NV_LANGUAGE, language, sizeof(language));
	websWrite(i_wp, T("HTTP/1.1 200 OK\n"));
	websWrite(i_wp, T("Pragma: no-cache\n"));
	websWrite(i_wp, T("Cache-control: no-cache\n"));
	websWrite(i_wp, T("Connection: Close\n"));
	websWrite(i_wp, T("Content-Type: text/html\n"));
	websWrite(i_wp, T("Set-Cookie: mLangage=%s;"" path=/;"" Expires=Mon, 1 Jan 2050 00:00:00 GMT\n"), language);
	websWrite(i_wp, T("\n"));
	websWrite(i_wp, T("<html>\n"));
	websWrite(i_wp, T(zte_web_page_header));
	websWrite(i_wp, T(zte_web_page_top), i_pageName);
	websWrite(i_wp, T("</html>"));
}



/******************************************************
* Function: zte_goform_get_cmd_process()
* Description:goform entry from web pages to get fw para, call the related functions according to the cmd
* Input:   HTTP Request Info
* Output:
* Return:
* Others:
* Modify Date      Version    Author         Modification
* 2012/08/08      V1.0         liuyingnan           create
*******************************************************/
void zte_goform_get_cmd_process(webs_t wp, char *path, char *query)
{
	char_t *cmd = NULL;
	char_t *multi_data = NULL;
	char login_info[NV_ITEM_STRING_LEN_20] = {0};
	UINT32 ticks = 0;
	int goform_index = 0;
	int max_goform_index = (int)(sizeof(web_goform_get_cmd_table) / sizeof(web_goform_type));

	zte_get_login_status_value(wp, login_info);

	cmd = websGetVar(wp, T("cmd"), T(""));
	multi_data = websGetVar(wp, T("multi_data"), T(""));

	slog(MISC_PRINT, SLOG_DEBUG, "[goahead] zte_goform_get_cmd_process multi_data = %s, cmd = %s\n", multi_data, cmd);

	zte_mgmt_login_timeout_check();

	if (0 == strcmp(cmd, "")) {
		websDone(wp, 0);
		return;
	}
#ifdef WEBINSPECT_FIX		
	if (query && (strstr(query, "%3d%2f")
		|| strstr(query, "%3c%2f")
		|| strstr(query, "=/")
		|| strstr(query, "</")
		|| strstr(query, "%27")
		|| strstr(query, "'")
#ifdef WEBS_SECURITY
		|| strstr(query, "%2f")
		|| strstr(query, "/")
		|| strstr(query, "%5c")
		|| strstr(query, "\\")
		|| strstr(query, "%3c")
		|| strstr(query, "<")
		|| strstr(query, "%3e")
		|| strstr(query, ">")
		|| strstr(query, "%28")
		|| strstr(query, "(")
		|| strstr(query, "%29")
		|| strstr(query, ")")
		|| strstr(query, "%25")
#endif
#endif		
		)) {
		//slog(MISC_PRINT, SLOG_ERR,"[goahead]e qry:%s\n",query);
		slog(MISC_PRINT, SLOG_ERR,"[goahead]e 1\n");
		websDone(wp, 0);
		return;
	}
	if (strstr(cmd, "=/") || strstr(cmd, "</")
#ifdef WEBS_SECURITY
	|| strstr(cmd, "/") || strstr(cmd, "\\") || strstr(cmd, "<") || strstr(cmd, ">")
#endif	
		|| strstr(cmd, "'")) {
		//slog(MISC_PRINT, SLOG_ERR,"[goahead]e cmd:%s\n",cmd);
		slog(MISC_PRINT, SLOG_ERR,"[goahead]e 2\n");
		websDone(wp, 0);
		return;
	}
#ifdef WEBS_SECURITY
	char token[COOKIE_SESSION_SIZE+1] = {0};
	char cook_id[COOKIE_SESSION_SIZE+1] = {0};
	(void)zte_web_read(NV_COOKIE_ID, cook_id);
	(void)zte_web_read(NV_WEB_TOKEN, token);
	if (strlen(cook_id) && wp->cookie && strlen(wp->cookie) > strlen(token)
		&& (strstr(wp->cookie, token) == NULL)) {
		//slog(MISC_PRINT, SLOG_ERR,"[goahead]e cmd:%s\n",cmd);
		//slog(MISC_PRINT, SLOG_ERR,"[goahead]e token:%s\n",token);
		//slog(MISC_PRINT, SLOG_ERR,"[goahead]e cookie:%s\n",wp->cookie);
		slog(MISC_PRINT, SLOG_ERR,"[goahead]e 3\n");
		websDone(wp, 0);
		cfg_set(NV_WEB_TOKEN, "");
		return;
	}
	if(query && !is_print_str(query,strlen(query)))
	{
		slog(MISC_PRINT, SLOG_ERR,"[goahead]g print_str\n");
		websDone(wp, 0);
		return;
	}
#endif

	if ((0 == strcmp("ok", login_info)) 
#if defined(JCV_FEATURE_ESIM_RSIM_SWITCH) || defined(JCV_FEATURE_SIM_HOTSWAP_SUPPORT)
	|| (0 == strcmp(GOFORM_GET_SIM_SELECT, cmd))
	|| (0 == strcmp(GOFORM_GET_ICCID_LOCK_INFO, cmd))
	|| (0 == strcmp(GOFORM_GET_ESIM_STATUS, cmd))
#endif
	|| (0 == strcmp(GOFORM_HTTPSHARE_GETCARD_VAULE, cmd))) {
		if (0 == strcmp(multi_data, "")) {
			for (goform_index = 0; goform_index < max_goform_index; goform_index++) {
				if (0 == strcmp(web_goform_get_cmd_table[goform_index].goform_id, cmd)) {
					(*(web_goform_get_cmd_table[goform_index].proc_func))(wp);
					websDone(wp, 200);
					return;
				}
			}
			zte_web_get_para_xml(wp, (char_t *)cmd);
		} else {
			zte_get_request_process_xml(wp, cmd);
		}
	} else {
		if (0 == strcmp(multi_data, "")) {
			zte_web_get_para_nologin(wp, (char_t *)cmd);
		} else {
			zte_get_request_process_nologin(wp, (char_t *)cmd);
		}
	}
	websDone(wp, 0);
	return;

}

/******************************************************
* Function: zte_goform_set_cmd_process()
* Description:goform entry from web pages to set firmware para
* Input:   HTTP Request Info
* Output:
* Return:
* Others:
* Modify Date      Version    Author         Modification
* 2012/08/08      V1.0         liuyingnan           create
*******************************************************/

void zte_goform_set_cmd_process(webs_t wp, char *path, char *query)
{
	char_t *zte_goform_id = NULL;
	int goform_index = 0;
	int max_goform_index = (int)(sizeof(web_goform_set_cmd_table) / sizeof(web_goform_type));
	int blacklist_result = 0;
	char login_info[NV_ITEM_STRING_LEN_20] = {0};

	zte_get_login_status_value(wp, login_info);
	zte_goform_id = websGetVar(wp, T("goformId"), T(""));

    slog(MISC_PRINT, SLOG_DEBUG,"[goahead] zte_goform_set_cmd_process zte_goform_id = %s,login_info=%s!\n", zte_goform_id, login_info);
#ifdef WEBINSPECT_FIX		
		if (query && (strstr(query, ")(") 
/*			|| strstr(query, "%26%26")
			|| strstr(query, "&&") 
			|| strstr(query, "%7C%7C") 
			|| strstr(query, "||")
			|| strstr(query, "%3B")
			|| strstr(query, ";")*/
			)) {
			//slog(MISC_PRINT, SLOG_ERR,"[goahead]e sqry:%s\n",query);
			slog(MISC_PRINT, SLOG_ERR,"[goahead]e 4\n");
			websDone(wp, 0);
			return;
		}
#ifdef WEBS_SECURITY
		if (!(wp->flags & WEBS_POST_REQUEST)||(query && (strstr(query, "%2f") 
			|| strstr(query, "/") 
			|| strstr(query, "%5c") 
			|| strstr(query, "\\")
			|| strstr(query, "%3c") 
			|| strstr(query, "<")
			|| strstr(query, "%3e") 
			|| strstr(query, ">")
			|| strstr(query, "%27")
			|| strstr(query, "'")
			|| strstr(query, "%28")
			|| strstr(query, "(")
			|| strstr(query, "%29")
			|| strstr(query, ")")
			|| strstr(query, "%25")
			))) {
			//slog(MISC_PRINT, SLOG_ERR,"[goahead]e qry:%s\n",query);
			slog(MISC_PRINT, SLOG_ERR,"[goahead]e 5\n");
			websDone(wp, 0);
			return;
		}
		char token[COOKIE_SESSION_SIZE+1] = {0};
		char cook_id[COOKIE_SESSION_SIZE+1] = {0};
		(void)zte_web_read(NV_COOKIE_ID, cook_id);
		(void)zte_web_read(NV_WEB_TOKEN, token);
		if (strlen(cook_id) && wp->cookie && strlen(wp->cookie) > strlen(token)
			&& (strstr(wp->cookie, token) == NULL)) {
			//slog(MISC_PRINT, SLOG_ERR,"[goahead]e goform:%s\n",zte_goform_id);
			//slog(MISC_PRINT, SLOG_ERR,"[goahead]e token:%s\n",token);
			//slog(MISC_PRINT, SLOG_ERR,"[goahead]e cookie:%s\n",wp->cookie);
			slog(MISC_PRINT, SLOG_ERR,"[goahead]e 6\n");
			websDone(wp, 0);
			cfg_set(NV_WEB_TOKEN, "");
			return;
		}
		if(query && !is_print_str(query,strlen(query)))
		{
			slog(MISC_PRINT, SLOG_ERR,"[goahead]s print_str\n");
			zte_write_result_to_web(wp, FAILURE);
			websDone(wp, 200);
			return;
		}
#endif	
#endif	

	if (0 != strcmp("ok", login_info)) {
		blacklist_result = zte_goform_blacklist_check(wp);
		if (blacklist_result < 0) {
            slog(MISC_PRINT, SLOG_ERR,"[ERROR]zte_goform_blacklist_check -> black goform id:[%s].\n", zte_goform_id);
			zte_write_result_to_web(wp, FAILURE);
			websDone(wp, 200);
			return;
		}
	}

	zte_mgmt_login_timemark_set();

	for (goform_index = 0; goform_index < max_goform_index; goform_index++) {
		if (0 == strcmp(web_goform_set_cmd_table[goform_index].goform_id, zte_goform_id)) {
			(void)zte_goform_whitelist_check(wp);//not to check the return value, only A has logged in,then update the timemark
			(*(web_goform_set_cmd_table[goform_index].proc_func))(wp);
			break;
		}
	}

	if (goform_index == max_goform_index) {
        slog(MISC_PRINT, SLOG_ERR,"[ERROR]zte_goform_set_cmd_process -> unknown goform id:[%s].\n", zte_goform_id);
		zte_write_result_to_web(wp, FAILURE);
	}

	websDone(wp, 200);
	return;
}

zte_topsw_state_e_type zte_nvconfig_read(char *item, char *data, int dataLen)
{
	if (NULL == item || NULL == data) {
		slog(MISC_PRINT, SLOG_ERR, "input is NULL."); /*lint !e26*/
		return ZTE_NVIO_FAIL;
	}

	if (dataLen > NV_ITEM_VALUE_MAX_LEN) {
		slog(MISC_PRINT, SLOG_ERR, "data of [%s] too long.", item); /*lint !e26*/
		return ZTE_NVIO_FAIL;
	}
    // kw 3
	if(0 == cfg_get_item(item, data, dataLen))
	{
    	return ZTE_NVIO_DONE;
	}

	return ZTE_NVIO_FAIL;
}

zte_topsw_state_e_type zte_nvconfig_write(char *item, char *data, int dataLen)
{
	if (NULL == item || NULL == data) {
		slog(MISC_PRINT, SLOG_ERR, "input is NULL."); /*lint !e26*/
		return ZTE_NVIO_FAIL;
	}

	if ((strlen(data) + 1) > NV_ITEM_VALUE_MAX_LEN || dataLen > NV_ITEM_VALUE_MAX_LEN) {
		slog(MISC_PRINT, SLOG_ERR, "value is too long, name=%s.", item); /*lint !e26*/
		return ZTE_NVIO_FAIL;
	}

	if(0 == cfg_set(item, data))
	{
	    return ZTE_NVIO_DONE;
	}

	return ZTE_NVIO_FAIL;
}

void zte_init_login_psw_time(void)
{
	(void)zte_web_write("psw_fail_num_str", LOGIN_FAIL_TIMES);
}

void zte_goform_set_external_nv(webs_t wp)
{
	char_t *nv_name = NULL;
	char_t *nv_value = NULL;
	nv_name = websGetVar(wp, T("external_nv_name"), T(""));
	nv_value = websGetVar(wp, T("external_nv_value"), T(""));

	if (nv_value == NULL || nv_name == NULL) {
		zte_write_result_to_web(wp, FAILURE);
		return;
	}

	if (0 != strncmp(nv_name, "external", 8)) {
		zte_write_result_to_web(wp, FAILURE);
		return;
	}

	cfg_set(nv_name, nv_value);
	zte_write_result_to_web(wp, SUCCESS);
}

/**************************************************************************
* ��������: zte_web_get_login_page
* ��������: ����Ӧ�ն������
* ����˵��: (IN)
*           (OUT)
* �� �� ֵ:
* ����˵��:
**************************************************************************/
const char* zte_web_get_login_page(webs_t wp)
{
	if (isMobileReq(wp) && isPageExist(ZTE_WEB_MOBILE_PAGE_LOGIN_NAME)) {
		return ZTE_WEB_MOBILE_PAGE_LOGIN_NAME;
	} else {
		return ZTE_WEB_PAGE_LOGIN_NAME;
	}
}



