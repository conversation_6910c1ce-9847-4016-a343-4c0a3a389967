#include	"wsIntrn.h"

static char_t	*websDefaultPage;			/* Default page name */
static char_t	*websDefaultDir;			/* Default Web page directory */

static void websCgiDownLoadWriteEvent(webs_t wp);
static void websDefaultWriteEvent(webs_t wp);
extern int get_client_mac_by_ip(const char *ip, char *mac_buf, int buf_size);

#ifdef ZXIC_ONELINK_TEST
// 函数声明
extern int cfg_get_item(const char *name, char *value, int len);
static int is_valid_mac_address(const char *mac);
void block_unauthenticated_mac(const char *mac);
#endif

#ifdef QRZL_WIFIDOG_ONELINK
// WiFiDog认证服务器接口函数声明
int wifidog_auth_login_handler(webs_t wp, char_t *urlPrefix, char_t *webDir, int arg, char_t *url, char_t *path, char_t *query);
int wifidog_auth_portal_handler(webs_t wp, char_t *urlPrefix, char_t *webDir, int arg, char_t *url, char_t *path, char_t *query);
int wifidog_auth_msg_handler(webs_t wp, char_t *urlPrefix, char_t *webDir, int arg, char_t *url, char_t *path, char_t *query);
int wifidog_auth_ping_handler(webs_t wp, char_t *urlPrefix, char_t *webDir, int arg, char_t *url, char_t *path, char_t *query);
int wifidog_auth_auth_handler(webs_t wp, char_t *urlPrefix, char_t *webDir, int arg, char_t *url, char_t *path, char_t *query);
int wifidog_goform_handler(webs_t wp, char_t *urlPrefix, char_t *webDir, int arg, char_t *url, char_t *path, char_t *query);

// 日志级别定义
#define LOG_WARNING 1
#define LOG_DEBUG   2
#define LOG_INFO    3
#define LOG_ERR     4

// 简单的debug函数实现
#define debug(level, fmt, ...) printf("[DEBUG] " fmt "\n", ##__VA_ARGS__)

// 函数声明
extern int convert_mac_format(const char *src_mac, char *dst_mac, int dst_size, char separator);
#endif

static int web_check_url(char *buf, char *nv_name, int is_ipv6)
{
	if(buf && nv_name) {
		char url[40] = {0};
		char full_url[50] = {0};
		cfg_get_item(nv_name, url, sizeof(url));
		if (websSSLIsOpen()){
			if(is_ipv6)
				snprintf(full_url, sizeof(full_url),"https://[%s]",url);
			else
				snprintf(full_url, sizeof(full_url),"https://%s",url);
		} else {
			if(is_ipv6)
				snprintf(full_url, sizeof(full_url),"http://[%s]",url);
			else
				snprintf(full_url, sizeof(full_url),"http://%s",url);
		}
		if(strlen(buf) > strlen(full_url) && strstr(buf,full_url)) {
			return 1;
		}
	}
	return 0;
}

int websDefaultHandler(webs_t wp, char_t *urlPrefix, char_t *webDir, int arg,
						char_t *url, char_t *path, char_t *query)
{
	websStatType	sbuf;
	char_t			*lpath, *tmp, *date;
	int				bytes, flags, nchars;
	char 			wifi_png__path[256] = {0};
	char 			*wifi_ptr = NULL;
	char 			wifi_root_dir[20] = {0};
	a_assert(websValid(wp));
	a_assert(url && *url);
	a_assert(path);
	a_assert(query);
#ifdef WEBINSPECT_FIX	
	if (strstr(query,"txtPwd")) {
		websDone(wp, 0);
		return 1;
	}
#ifdef WEBS_SECURITY
	if (strstr(query,"_method")) {
		printf("websDH: qry=%s\n",query);
		websError(wp, 405, T(""));
		return 1;
	}
#endif
#endif	

	flags = websGetRequestFlags(wp);

	if (websValidateUrl(wp, path) < 0) 
   {
      websError(wp, 500, T("Invalid URL"));
		return 1;
	}
	lpath = websGetRequestLpath(wp);
		
//qrcode_ssid_wifikey.png   qrcode_multi_ssid_wifikey.png
	if((strstr(lpath,"pic/qrcode_ssid_wifikey.png")!=NULL)||(strstr(lpath,"pic/qrcode_multi_ssid_wifikey.png")!=NULL))
	{
		cfg_get_item("wifi_root_dir",wifi_root_dir,sizeof(wifi_root_dir));
		wifi_ptr=strstr(lpath,"pic/qrcode_");
		wifi_ptr+=4;
		//printf("[wifi_png]wifi_ptr:%s\n",wifi_ptr);
		snprintf(wifi_png__path,sizeof(wifi_png__path),"%s/wifi/%s",wifi_root_dir,wifi_ptr);
		//printf("[wifi_png]wifi_png_path:%s\n",wifi_png__path);
		lpath=wifi_png__path;
	}
	
    if(strstr(lpath,"web/messages"))
	{
		//lpath="/var/log/webshow_messages";
		snprintf(wifi_png__path,sizeof(wifi_png__path),"%s","/var/log/webshow_messages");
		lpath=wifi_png__path;// kw OVERWRITE_CONST_CHAR
    }
	if(strstr(lpath,"/webshow_messages"))
	{
		//lpath="/etc_ro/web/webshow_messages";
		snprintf(wifi_png__path,sizeof(wifi_png__path),"%s","/etc_ro/web/webshow_messages");
		lpath=wifi_png__path;// kw OVERWRITE_CONST_CHAR
    }
	nchars = gstrlen(lpath) - 1;
	if (lpath[nchars] == '/' || lpath[nchars] == '\\') {
		lpath[nchars] = '\0';
	}
	if(lpath != websGetRequestLpath(wp))
	{
		websSetRequestLpath(wp,lpath);
		lpath = websGetRequestLpath(wp);
	}

	if (websPageIsDirectory(lpath)) {
		nchars = gstrlen(path);
		if (path[nchars-1] == '/' || path[nchars-1] == '\\') {
			path[--nchars] = '\0';
		}
		nchars += gstrlen(websDefaultPage) + 2;
		fmtAlloc(&tmp, nchars, T("%s/%s"), path, websDefaultPage);
		websRedirect(wp, tmp);
		bfreeSafe(B_L, tmp);
		return 1;
	}
#ifdef WEBS_SECURITY	
	if (strstr(path,websDefaultPage)){
		if (!(wp->flags & WEBS_SECURE) && websSSLIsOpen()){
			printf("[goahead]no https Redirect\n");
			websRedirect(wp, zte_web_get_login_page(wp));
			return 1;
		}
	} else {
#if 0
		if(wp->cookie == NULL) {
			printf("[goahead]no cookie Redirect\n");
			websRedirect(wp, zte_web_get_login_page(wp));
			return 1;
		}
#endif		
		if(wp->referer)
		{
			if(web_check_url(wp->referer, "LocalDomain", 0) == 0
				&& web_check_url(wp->referer, "lan_ipaddr", 0) == 0
				&& web_check_url(wp->referer, "lan_ipv6addr", 1) == 0) {
				printf("[goahead]referer=%s Redirect\n",wp->referer);
				websRedirect(wp, zte_web_get_login_page(wp));
				return 1;
			}
		}
	}
#endif
/*
 *	Open the document. Stat for later use.
 */

	if (websPageOpen(wp, lpath, path, SOCKET_RDONLY | SOCKET_BINARY, 
		0666) < 0) 
   {
      /* 
       * <http://www.w3.org/Protocols/rfc2616/rfc2616-sec10.html>
       */
#ifdef ZXIC_ONELINK_TEST
		char is_registerd[4] = {0};
		char lan_ipaddr[16] = {0};
		char lan_net_prefix[16] = {0};
		char *last_dot;
		char one_link_authed_mac[1024] = {0};
		int need_redirect = 0;

        printf("corem websDefaultHandler websPageOpen failed, ip:%s ifaddr:%s\n", wp->ipaddr, wp->ifaddr);//corem for test onelink

		cfg_get_item("is_registerd", is_registerd, sizeof(is_registerd));
		cfg_get_item("lan_ipaddr", lan_ipaddr, sizeof(lan_ipaddr));
		cfg_get_item("one_link_authed_mac", one_link_authed_mac, sizeof(one_link_authed_mac));

		// 构建局域网网段前缀 (192.168.100.)
		strncpy(lan_net_prefix, lan_ipaddr, sizeof(lan_net_prefix) - 1);
		last_dot = strrchr(lan_net_prefix, '.');
		if (last_dot) {
			*(last_dot + 1) = '\0';
		}

		printf("corem lan_ipaddr:%s, lan_net_prefix:%s, client_ip:%s\n", lan_ipaddr, lan_net_prefix, wp->ipaddr);

		// 检查是否为局域网用户（192.168.100.x网段，但不是网关地址）
		if (strncmp(wp->ipaddr, lan_net_prefix, strlen(lan_net_prefix)) == 0 &&
			strcmp(wp->ipaddr, lan_ipaddr) != 0) {

			printf("corem WiFi client detected from %s network: %s\n", lan_net_prefix, wp->ipaddr);

			// 获取客户端MAC地址（从ARP表或其他方式）
			char client_mac[32] = {0};
			if (get_client_mac_by_ip(wp->ipaddr, client_mac, sizeof(client_mac)) == 0) {
				printf("corem resolved MAC address: %s\n", client_mac);

				// 检查MAC是否在认证列表中
				if (strlen(one_link_authed_mac) > 0) {
					char search_pattern[40] = {0};
					snprintf(search_pattern, sizeof(search_pattern), "%s;", client_mac);

					if (strstr(one_link_authed_mac, search_pattern) != NULL) {
						printf("corem MAC %s is authenticated, allow access\n", client_mac);
						need_redirect = 0;
					} else {
						printf("corem MAC %s is NOT authenticated, need to block and redirect\n", client_mac);
						need_redirect = 1;

						// 注意：不在这里调用block_unauthenticated_mac，而是在websRedirectOneLinkWeb中调用
						// 确保页面重定向成功后再阻断网络
						block_unauthenticated_mac(client_mac);
					}
				} else {
					printf("corem no authenticated MAC list found, need redirect\n");
					need_redirect = 1;

					// 调用iptables屏蔽该MAC的流量，注意：不在这里调用
					block_unauthenticated_mac(client_mac);
				}
			} else {
				printf("corem failed to resolve MAC address for IP: %s\n", wp->ipaddr);
				need_redirect = 1; // 无法获取MAC时也进行重定向
			}
		}

        if (strncmp(is_registerd, "1", 1)/*not registered*/ || need_redirect)
		{
			websRedirectOneLinkWeb(wp, zte_web_get_onelink_register_page(wp));
			//websRedirect(wp, zte_web_get_login_page(wp));

			return 1;
		}
#endif
		websError(wp, 404, T("Cannot open URL"));
		return 1;
	} 

	if (websPageStat(wp, lpath, path, &sbuf) < 0) {
		websError(wp, 400, T("Cannot stat page for URL"));
		return 1;
	}

	websStats.localHits++;
#ifdef WEBS_IF_MODIFIED_SUPPORT
	if (flags & WEBS_IF_MODIFIED && !(flags & WEBS_ASP)) {
		if (sbuf.mtime <= wp->since) {
			websWrite(wp, T("HTTP/1.0 304 Use local copy\r\n"));

			websWrite(wp, T("Server: %s\r\n"), WEBS_NAME);

			if (flags & WEBS_KEEP_ALIVE) {
				websWrite(wp, T("Connection: keep-alive\r\n"));
			}
			websWrite(wp, T("\r\n"));
			websSetRequestFlags(wp, flags |= WEBS_HEADER_DONE);
			websDone(wp, 304);
			return 1;
		}
	}
#endif

	if ((date = websGetDateString(NULL)) != NULL) {
		websWrite(wp, T("HTTP/1.0 200 OK\r\nDate: %s\r\n"), date);

		websWrite(wp, T("Server: %s\r\n"), WEBS_NAME);
		bfree(B_L, date);
	}
	flags |= WEBS_HEADER_DONE;

	if (flags & WEBS_ASP) {
		bytes = 0;
#ifndef WEBINSPECT_FIX	
		websWrite(wp, T("Pragma: no-cache\r\nCache-Control: no-cache\r\n"));
#endif
	} else {
		if ((date = websGetDateString(&sbuf)) != NULL) {
			websWrite(wp, T("Last-modified: %s\r\n"), date);
			bfree(B_L, date);
		}
		bytes = sbuf.size;
	}
#ifdef WEBINSPECT_FIX		
	websWrite(wp, T("X-Frame-Options: SAMEORIGIN\r\nPragma: no-cache\r\nCache-Control: no-cache\r\n"));
#endif
#ifdef WEBS_SECURITY
	websWrite(wp, T("Expires: 0\n"));
	if (strstr(path,websDefaultPage)){
		char id[33] = {0};
		int ret = web_make_salt_base64(id, sizeof(id));
		if(ret > 0)
		{
			if (websSSLIsOpen())
				websWrite(wp, T("Set-Cookie: id=%s; secure; HttpOnly; SameSite=Lax;\r\n"),id);
			else
				websWrite(wp, T("Set-Cookie: id=%s; HttpOnly; SameSite=Lax;\r\n"),id);
		}
	}
#endif	

	if (bytes) {
#ifdef WEBINSPECT_FIX	
		if (strstr(path,"/tmpl/"))
			websWrite(wp, T("Content-length: %d\r\n"), bytes+86);
		else
#endif		
			websWrite(wp, T("Content-length: %d\r\n"), bytes);
		websSetRequestBytes(wp, bytes);
	}
	websWrite(wp, T("Content-type: %s\r\n"), websGetRequestType(wp));

	if ((flags & WEBS_KEEP_ALIVE) && !(flags & WEBS_ASP)) {
		websWrite(wp, T("Connection: keep-alive\r\n"));
	}
	websWrite(wp, T("\r\n"));

	if (flags & WEBS_HEAD_REQUEST) {
		websDone(wp, 200);
		return 1;
	}

#ifdef WEB_ASP		
	if (flags & WEBS_ASP) {
		if (websAspRequest(wp, lpath) < 0) {
			return 1;
		}
		websDone(wp, 200);
		return 1;
	}
#endif		
#ifdef WEBINSPECT_FIX
	if (strstr(path,"/tmpl/") && bytes) {
		websWrite(wp, T("<script type=\"text/javascript\">if(self!=top){top.location = self.location;}</script>\r\n"));
	}
#endif	
#ifdef WEBS_SSL_SUPPORT
	if (wp->flags & WEBS_SECURE) {
		websDefaultWriteEvent(wp);
	} else {
		websSetRequestSocketHandler(wp, SOCKET_WRITABLE, websDefaultWriteEvent);
	}
#else

	websSetRequestSocketHandler(wp, SOCKET_WRITABLE, websDefaultWriteEvent);
#endif
	return 1;
}

//#ifdef FEATURE_ZTE_WEB_TCARD
//added by guo shoupeng 10124224 for http share 20120110 start
int websCgiDownLoadHandler(webs_t wp, char_t *urlPrefix, char_t *webDir, int arg,
						char_t *url, char_t *path, char_t *query)
{
	//urlPrefix /mmc2
	//url /mmc2/test.file?.........    url = ALL
	//path /mmc2/test.file
	//query ......
	if (zte_check_downloading_file())
	{
		printf("[httpshare]call websCgiDownLoadHandler:system is downloading!.\n");
		websError(wp, 404, T("System is downloading file,please try later!"));
		return 1;
	}
	websStatType	sbuf;
	char_t			*lpath, *tmp, *date;
	int				bytes, flags, nchars;

	a_assert(websValid(wp));
	a_assert(url && *url);
	a_assert(path);
	a_assert(query);
	char_t mmc2_path[4096+1] = {0};

	flags = websGetRequestFlags(wp);

	if (websValidateUrl(wp, path) < 0) 
   {
      websError(wp, 500, T("Invalid URL"));
		return 1;
	}

	lpath = websGetRequestLpath(wp);

	nchars = gstrlen(lpath) - 1;
	if (lpath[nchars] == '/' || lpath[nchars] == '\\') {
		lpath[nchars] = '\0';
	}

	if (websPageIsDirectory(lpath)) {
		nchars = gstrlen(path);
		if (path[nchars-1] == '/' || path[nchars-1] == '\\') {
			path[--nchars] = '\0';
		}
		nchars += gstrlen(websDefaultPage) + 2;
		fmtAlloc(&tmp, nchars, T("%s/%s"), path, websDefaultPage);
		websRedirect(wp, tmp);
		bfreeSafe(B_L, tmp);
		return 1;
	}
//added for download file  start
	
	if(strstr(lpath,"/mmc2"))
	{		
		snprintf(mmc2_path,4096+1,"/etc_rw/config%s",path);
		lpath = mmc2_path;
		path = mmc2_path;
		
		printf("[httpshare]insert download file->%s\n",mmc2_path);
		zte_insert_download_file(mmc2_path);
		websSetRequestLpath(wp,lpath);
	}

//added for download file end

	if (websPageOpen(wp, lpath, path, SOCKET_RDONLY | SOCKET_BINARY, 
		0666) < 0) 
   {
      	printf("[httpshare]file is too big , can't open!\n");
	  	if(strstr(mmc2_path,"/mmc2") != NULL)
		{
			zte_del_download_file();
			printf("[httpshare]websPageClose:del file->%s form sql download.\n",mmc2_path);			
		}
		websError(wp, 404, T("Cannot open URL,File Error!"));
		return 1;
	} 

	if (websPageStat(wp, lpath, path, &sbuf) < 0) {

		if(strstr(mmc2_path,"/mmc2") != NULL)
		{
			zte_del_download_file();
			printf("[httpshare]websPageClose:del file->%s form sql download.\n",mmc2_path);			
		}
	  	websError(wp, 400, T("Cannot stat page for URL"));
		return 1;
	}

	websStats.localHits++;
#ifdef WEBS_IF_MODIFIED_SUPPORT
	if (flags & WEBS_IF_MODIFIED && !(flags & WEBS_ASP)) {
		if (sbuf.mtime <= wp->since) {
			websWrite(wp, T("HTTP/1.0 304 Use local copy\r\n"));

			websWrite(wp, T("Server: %s\r\n"), WEBS_NAME);

			if (flags & WEBS_KEEP_ALIVE) {
				websWrite(wp, T("Connection: keep-alive\r\n"));
			}
			websWrite(wp, T("\r\n"));
			websSetRequestFlags(wp, flags |= WEBS_HEADER_DONE);
			websDone(wp, 304);
			return 1;
		}
	}
#endif

	if ((date = websGetDateString(NULL)) != NULL) {
		websWrite(wp, T("HTTP/1.0 200 OK\r\nDate: %s\r\n"), date);

		websWrite(wp, T("Server: %s\r\n"), WEBS_NAME);
		bfree(B_L, date);
	}
	flags |= WEBS_HEADER_DONE;

 #if 0
	if (flags & WEBS_ASP) {
		bytes = 0;
		websWrite(wp, T("Pragma: no-cache\r\nCache-Control: no-cache\r\n"));

	} else {
#endif	
		if ((date = websGetDateString(&sbuf)) != NULL) {
			websWrite(wp, T("Last-modified: %s\r\n"), date);
			bfree(B_L, date);
		}
		bytes = sbuf.size;
#if 0		
	}
#endif	
#if 0
	if (bytes) {
#endif	
		websWrite(wp, T("Content-length: %d\r\n"), bytes);	       
		websSetRequestBytes(wp, bytes);		
#if 0		
	}	
#endif	
#if 0
	websWrite(wp, T("Content-type: %s\r\n"), websGetRequestType(wp));
#else
	char name[256] = {0};
	int k = 0;
	int i = 0;
	for(i = 0; i < gstrlen(lpath); i++){
		if((lpath[i] == '/') ||  (lpath[i] == '\\')){
			memset(name, 0, sizeof(name));
			k = 0;
			continue;
		}else{
			name[k] = lpath[i];
			k++;
		}		
	}
	name[k] = '\0';

	
	websWrite(wp, T("Content-type: application/octet-stream\r\n"));
	websWrite(wp, T("Content-Disposition: attachment; filename\r\n"));		
#endif

#if 0
	if ((flags & WEBS_KEEP_ALIVE) 
#if 0		
		&& !(flags & WEBS_ASP)
#endif		
		) {
		websWrite(wp, T("Connection: keep-alive\r\n"));
	}
#endif
	websWrite(wp, T("\r\n"));

/*
 *	All done if the browser did a HEAD request
 */
	/*if (flags & WEBS_HEAD_REQUEST) {

		printf("Head request:websDone\n");
		websDone(wp, 200);
		return 1;
	}*/

/*
 *	Evaluate ASP requests
 */
 #if 0
	if (flags & WEBS_ASP) {
		if (websAspRequest(wp, lpath) < 0) {
			return 1;
		}
		websDone(wp, 200);
		return 1;
	}
#endif	

#ifdef WEBS_SSL_SUPPORT
	if (wp->flags & WEBS_SECURE) {
		websDefaultWriteEvent(wp);
	} else {
		websSetRequestSocketHandler(wp, SOCKET_WRITABLE, websCgiDownLoadWriteEvent);
	}
#else
/*
 *	For normal web documents, return the data via background write
 */
	websSetRequestSocketHandler(wp, SOCKET_WRITABLE, websCgiDownLoadWriteEvent);
#endif
	return 1;
}
//added by guo shoupeng 10124224 for http share 20120110 end
//#endif

#ifdef WIN32

static int badPath(char_t* path, char_t* badPath, int badLen)
{
   int retval = 0;
   int len = gstrlen(path);
   int i = 0;

   if (len <= badLen +1)
   {
      for (i = 0; i < badLen; ++i)
      {
         if (badPath[i] != gtolower(path[i]))
         {
            return 0;
         }
      }

      retval = 1;
      if (badLen + 1 == len)
      {
         if (gisalnum(path[len-1]))
         {
            retval = 0;
         }
      }
   }

   return retval;
}


static int isBadWindowsPath(char_t** parts, int partCount)
{
   OSVERSIONINFO version;
   int i;
   version.dwOSVersionInfoSize = sizeof(OSVERSIONINFO);
   if (GetVersionEx(&version))
   {
      if (VER_PLATFORM_WIN32_NT != version.dwPlatformId)
      {
         for (i = 0; i < partCount; ++i)
         {
            if ( 
             (badPath(parts[i], T("con"), 3)) ||
             (badPath(parts[i], T("nul"), 3)) ||
             (badPath(parts[i], T("aux"), 3)) ||
             (badPath(parts[i], T("clock$"), 6)) ||
             (badPath(parts[i], T("config$"), 7)) )
            {
               return 1;
            }
         }
      }
   }

   return 0;
}
#endif

int websValidateUrl(webs_t wp, char_t *path)
{
#define kMaxUrlParts 64
	char_t	*parts[kMaxUrlParts];	/* Array of ptr's to URL parts */
	char_t	*token, *dir, *lpath; 
   int	      i, len, npart;

	a_assert(websValid(wp));
	a_assert(path);

	dir = websGetRequestDir(wp);
	if (/*dir == NULL ||*/ *dir == '\0') {  // kw 3
		return -1;
	}

	path = bstrdup(B_L, path);
	websDecodeUrl(path, path, gstrlen(path));

	len = npart = 0;
	parts[0] = NULL;

   token = gstrchr(path, '\\');
   while (token != NULL)
   {
      *token = '/';
      token = gstrchr(token, '\\');
   }
   
	token = gstrtok(path, T("/"));

	while (token != NULL) 
   {
      if (npart >= kMaxUrlParts)
      {
         bfree(B_L, path);
         return -1;
      }
		if (gstrcmp(token, T("..")) == 0) 
      {
			if (npart > 0) 
         {
				npart--;
			}

		} 
      else if (gstrcmp(token, T(".")) != 0) 
      {
			parts[npart] = token;
			len += gstrlen(token) + 1;
			npart++;
		}
		token = gstrtok(NULL, T("/"));
	}

#ifdef WIN32
   if (isBadWindowsPath(parts, npart))
   {
      bfree(B_L, path);
      return -1;
   }

#endif


	if (npart || (gstrcmp(path, T("/")) == 0) || (path[0] == '\0')) 
   {
		lpath = balloc(B_L, (gstrlen(dir) + 1 + len + 1) * sizeof(char_t));
		if(lpath == NULL){
			bfree(B_L, path);
			return -1;
		}
		gstrcpy(lpath, dir);

		for (i = 0; i < npart; i++) 
      {
			gstrcat(lpath, T("/"));
			gstrcat(lpath, parts[i]);
		}
		websSetRequestLpath(wp, lpath);
		bfree(B_L, path);
		bfree(B_L, lpath);
	} 
   else 
   {
		bfree(B_L, path);
		return -1;
	}
	return 0;
}

//#ifdef FEATURE_ZTE_WEB_TCARD
//added by guo shoupeng 10124224 for http share 20120110 start
int write_bytes = 0;
static void websCgiDownLoadWriteEvent(webs_t wp)
{
 //printf("websCgiDownLoadWriteEvent start\n");
	int		len = -1;
	int wrote, flags, bytes, written;
	char	*buf;
	
	extern int errno;

	a_assert(websValid(wp));

	flags = websGetRequestFlags(wp);

	websSetTimeMark(wp);

	wrote = bytes = 0;
	written = websGetRequestWritten(wp);
	static unsigned int timer = 0;

#if 0
	if ( !(flags & WEBS_ASP)) {
#endif		
		bytes = websGetRequestBytes(wp);

		if ((buf = balloc(B_L, 16*PAGE_READ_BUFSIZE)) == NULL) {
			websError(wp, 200, T("Can't get memory"));
			return; //cov
		} else {
			while ((len = websPageReadData(wp, buf, 16*PAGE_READ_BUFSIZE)) > 0) {
				if ((wrote = websWriteDataNonBlock(wp, buf, len)) < 0) {
					break;
				}
				write_bytes += wrote;
				written += wrote;
				if (wrote != len) {
					websPageSeek(wp, - (len - wrote));
					break;
				}
				if(write_bytes > DOWNLOAD_INTERVAL)
				{
					write_bytes = 0;
					break;
				}

				if( timer ==0)
                {
                    websSetLoginTimemark(wp);
					printf("[httpshare]download reset login state~\n");
                }
				
				timer++;
				timer=timer-(timer>>11<<11);  //timer%2^11
			}

 		    //EOF, done
			if (len == 0) {
				a_assert(written >= bytes);
				written = bytes;
			}
			memset(buf, 0, 16*PAGE_READ_BUFSIZE);//kw
			bfree(B_L, buf);
		}
#if 0		
	}
#endif	

 	if(len < 0)
	{
		printf("[zyl-download-len-error]len->%d, errno->%d\n",len,errno);
	}
	websSetRequestWritten(wp, written);
	if (wrote < 0 || written >= bytes|| len < 0) {
	//if (wrote < 0 || written >= bytes) {
		websDone(wp, 200);
	}
}
//added by guo shoupeng 10124224 for http share 20120110 end
//#endif

static void websDefaultWriteEvent(webs_t wp)
{
	int		len, wrote, flags, bytes, written;
	char	*buf;

	a_assert(websValid(wp));

	flags = websGetRequestFlags(wp);

	websSetTimeMark(wp);

	wrote = bytes = 0;
	written = websGetRequestWritten(wp);

	if ( !(flags & WEBS_ASP)) {
		bytes = websGetRequestBytes(wp);

		if ((buf = balloc(B_L, PAGE_READ_BUFSIZE)) == NULL) {
			websError(wp, 200, T("Can't get memory"));
			return; //cov
		} else {
			while ((len = websPageReadData(wp, buf, PAGE_READ_BUFSIZE)) > 0) {
				if ((wrote = websWriteDataNonBlock(wp, buf, len)) < 0) {
					break;
				}
#ifdef _USE_WEBUI_ZIP
				if (wrote != len)
				{
					int tmplen;
					int leftlen = (len - wrote);
					while(leftlen > 0)
					{
						if((get_sys_uptime() - wp->timestamp) > (WEBS_TIMEOUT/1000))
							break;
						tmplen = websWriteDataNonBlock(wp, buf+wrote, leftlen);
						if(tmplen >= 0)
						{
							//printf("%s write=%d left=%d\n",wp->path,tmplen, leftlen);
							wrote +=tmplen;
							leftlen -=tmplen;
							usleep(1000);
						}
						else
						{
							break;
						}
					}

				}
#endif				
				written += wrote;
				if (wrote != len) {
					websPageSeek(wp, - (len - wrote));
					break;
				}
			}

			if (len == 0) {
				a_assert(written >= bytes);
				written = bytes;
			
			}
			memset(buf, 0, PAGE_READ_BUFSIZE);//kw
			bfree(B_L, buf);
		}
	}

	websSetRequestWritten(wp, written);
	if (wrote < 0 || written >= bytes) {
		websDone(wp, 200);
	}
}

void websDefaultClose()
{
	if (websDefaultPage) {
		bfree(B_L, websDefaultPage);
		websDefaultPage = NULL;
	}
	if (websDefaultDir) {
		bfree(B_L, websDefaultDir);
		websDefaultDir = NULL;
	}
}

char_t *websGetDefaultPage()
{
	return websDefaultPage;
}

char_t *websGetDefaultDir()
{
	return websDefaultDir;
}

void websSetDefaultPage(char_t *page)
{
	a_assert(page && *page);

	if (websDefaultPage) {
		bfree(B_L, websDefaultPage);
	}
	websDefaultPage = bstrdup(B_L, page);
}

void websSetDefaultDir(char_t *dir)
{
	a_assert(dir && *dir);
	if (websDefaultDir) {
		bfree(B_L, websDefaultDir);
	}
	websDefaultDir = bstrdup(B_L, dir);
}

#ifdef ZXIC_ONELINK_TEST
/**
 * 检查MAC地址是否合法
 * @param mac MAC地址字符串
 * @return 1合法，0不合法
 */
static int is_valid_mac_address(const char *mac)
{
	if (!mac || strlen(mac) != 17) {
		return 0;
	}

	// 检查MAC地址格式 (xx:xx:xx:xx:xx:xx)
	int i;
	for (i = 0; i < 17; i++) {
		if (i % 3 == 2) {
			// 应该是冒号
			if (mac[i] != ':') {
				return 0;
			}
		} else {
			// 应该是十六进制字符
			if (!((mac[i] >= '0' && mac[i] <= '9') ||
				  (mac[i] >= 'a' && mac[i] <= 'f') ||
				  (mac[i] >= 'A' && mac[i] <= 'F'))) {
				return 0;
			}
		}
	}

	// 检查是否为无效的MAC地址
	if (strcmp(mac, "00:00:00:00:00:00") == 0) {
		return 0; // 全零MAC地址无效
	}

	if (strcmp(mac, "ff:ff:ff:ff:ff:ff") == 0 || strcmp(mac, "FF:FF:FF:FF:FF:FF") == 0) {
		return 0; // 广播MAC地址无效
	}

	return 1; // 合法
}



/**
 * 屏蔽未认证MAC地址的网络流量
 * @param mac MAC地址字符串
 */
void block_unauthenticated_mac(const char *mac)
{
	char cmd[256];
	static char blocked_macs[1024] = {0}; // 静态变量记录已处理的MAC
	char search_pattern[40] = {0};

	// 检查MAC地址合法性
	if (!is_valid_mac_address(mac)) {
		printf("corem invalid MAC address: %s\n", mac ? mac : "null");
		return;
	}

	// 检查是否已经为这个MAC添加过规则
	snprintf(search_pattern, sizeof(search_pattern), "%s;", mac);
	if (strstr(blocked_macs, search_pattern) != NULL) {
		printf("corem MAC %s already blocked, skip duplicate rules\n", mac);
		return;
	}

	printf("corem blocking traffic for unauthenticated MAC: %s\n", mac);

	// 获取网关地址
	char lan_ipaddr[16] = {0};
	cfg_get_item("lan_ipaddr", lan_ipaddr, sizeof(lan_ipaddr));
	printf("corem gateway address: %s\n", lan_ipaddr);

	// 允许DNS解析（53端口）- 用于域名解析
	snprintf(cmd, sizeof(cmd),
		"iptables -I FORWARD -m mac --mac-source %s -p udp --dport 53 -j ACCEPT",
		mac);
	system(cmd);

	// 允许访问网关地址的所有流量（重要：确保能访问认证页面）
	if (strlen(lan_ipaddr) > 0) {
		snprintf(cmd, sizeof(cmd),
			"iptables -I FORWARD -m mac --mac-source %s -d %s -j ACCEPT",
			mac, lan_ipaddr);
		system(cmd);
		printf("corem allowed access to gateway %s for MAC %s\n", lan_ipaddr, mac);
	}

	// 允许访问本地web界面（HTTP 80端口到本机）
	snprintf(cmd, sizeof(cmd),
		"iptables -I INPUT -m mac --mac-source %s -p tcp --dport 80 -j ACCEPT",
		mac);
	system(cmd);

	// 允许访问本地web界面（HTTPS 443端口到本机）
	snprintf(cmd, sizeof(cmd),
		"iptables -I INPUT -m mac --mac-source %s -p tcp --dport 443 -j ACCEPT",
		mac);
	system(cmd);

	// 阻止该MAC地址访问外网的所有流量（FORWARD链）
	snprintf(cmd, sizeof(cmd),
		"iptables -A FORWARD -m mac --mac-source %s -j DROP",
		mac);
	system(cmd);

	// 阻止该MAC地址IPv6访问外网的所有流量（FORWARD链）- 对应add_sta_mac中的IPv6阻断
	snprintf(cmd, sizeof(cmd),
		"ip6tables -I FORWARD -m mac --mac-source %s -j DROP",
		mac);
	system(cmd);

	// 记录已处理的MAC，避免重复添加规则
	if (strlen(blocked_macs) + strlen(search_pattern) < sizeof(blocked_macs) - 1) {
		strcat(blocked_macs, search_pattern);
	}

	printf("corem iptables rules added for MAC: %s\n", mac);
}
#endif


#ifdef QRZL_WIFIDOG_ONELINK
/**
 * WiFiDog认证服务器接口实现
 * 处理WiFiDog的登录请求，重定向到OneLink认证页面
 */
int wifidog_auth_login_handler(webs_t wp, char_t *urlPrefix, char_t *webDir, int arg, char_t *url, char_t *path, char_t *query)
{
	char_t *gw_address = NULL;
	char_t *gw_port = NULL;
	char_t *gw_id = NULL;
	char_t *client_mac = NULL;
	char_t *client_ip = NULL;
	char_t *redirect_url = NULL;

	char onelink_url[1024] = {0};
	char terminal_mac[32] = {0};
	char router_mac[32] = {0};
	char iccid[32] = {0};
	char token_url[256] = {0};
	char domain[128] = {0};

	printf("corem wifidog_auth_login_handler: processing login request\n");

	// 获取WiFiDog传递的参数
	gw_address = websGetVar(wp, T("gw_address"), T(""));
	gw_port = websGetVar(wp, T("gw_port"), T(""));
	gw_id = websGetVar(wp, T("gw_id"), T(""));
	client_mac = websGetVar(wp, T("mac"), T(""));
	client_ip = websGetVar(wp, T("ip"), T(""));
	redirect_url = websGetVar(wp, T("url"), T("http://www.baidu.com"));

	printf("corem login params - gw_address:%s, gw_port:%s, gw_id:%s, mac:%s, ip:%s\n",
		   gw_address, gw_port, gw_id, client_mac, client_ip);

	// 保存WiFiDog参数到配置中，供callback阶段使用
	if (strlen(gw_address) > 0) {
		cfg_set("wifidog_gw_address", gw_address);
	}
	if (strlen(gw_port) > 0) {
		cfg_set("wifidog_gw_port", gw_port);
	}
	if (strlen(gw_id) > 0) {
		cfg_set("wifidog_gw_id", gw_id);
	}
	printf("corem saved wifidog params for callback use\n");

	
#ifdef QRZL_ONE_LINK_CUSTOMER_MY
	// Fallback to OneLink external authentication server
	char one_link_customers_get_token_url[512] = {0};
	cfg_get_item("ONE_LINK_customers_get_token_url", one_link_customers_get_token_url, sizeof(one_link_customers_get_token_url));

	// Only proceed with external redirect if URL is configured
	if (strlen(one_link_customers_get_token_url) == 0) {
		printf("No OneLink URL configured, using default auth server\n");
	} else {
		// 获取设备WiFi MAC地址
		char wifi_mac[18] = {0};
		cfg_get_item("wifi_mac", wifi_mac, sizeof(wifi_mac));

		// 使用传入的客户端MAC地址
		char *terminal_mac_ptr = client_mac;
		if (!terminal_mac_ptr || strlen(terminal_mac_ptr) == 0) {
			terminal_mac_ptr = "00:00:00:00:00:00"; // 默认值
		}

		// MAC - 转换wifi_mac为无分隔符格式
		char convered_mac[33] = {0};
		convert_mac_format(wifi_mac, convered_mac, sizeof(convered_mac), '\0');

		// terminalMac - 转换terminal_mac为无分隔符格式
		char convered_terminalMac[33] = {0};
		convert_mac_format(terminal_mac_ptr, convered_terminalMac, sizeof(convered_terminalMac), '\0');

		// iccid
		char current_iccid[22] = {0};
		cfg_get_item("ziccid", current_iccid, sizeof(current_iccid));

		char sn[22] = {0};
		cfg_get_item("sn", sn, sizeof(sn));

		char sim_select[22] = {0};
		cfg_get_item("sim_select", sim_select, sizeof(sim_select));

		// sequence
		char sequence[2] = {0};
		if(strcmp(sim_select, "RSIM_only") == 0) {
			snprintf(sequence, sizeof(sequence), "%s", "3");
		} else if(strcmp(sim_select, "ESIM1_only") == 0) {
			snprintf(sequence, sizeof(sequence), "%s", "1");
		} else if(strcmp(sim_select, "ESIM2_only") == 0) {
			snprintf(sequence, sizeof(sequence), "%s", "2");
		} else {
			snprintf(sequence, sizeof(sequence), "%s", "0");
		}

		char lan_ip[30] = {0};
		cfg_get_item("lan_ipaddr", lan_ip, sizeof(lan_ip));

		// 构建米亦认证URL
		snprintf(onelink_url, sizeof(onelink_url),
				"%s/boss-web/wx-login.html?mac=%s&terminalMac=%s&phoneNum=%s&iccid=%s&sn=%s&sequence=%s&deviceIp=http://%s/Api/codeToken",
					one_link_customers_get_token_url, convered_mac, convered_terminalMac, "", current_iccid, sn, sequence , lan_ip);
	}
#else
	// 获取终端真实MAC地址（从wp中获取，这里使用client_ip查找）
	if (get_client_mac_by_ip(client_ip, terminal_mac, sizeof(terminal_mac)) != 0) {
		// 如果无法获取，使用WiFiDog传递的MAC
		strncpy(terminal_mac, client_mac, sizeof(terminal_mac) - 1);
	}

	// 转换MAC地址格式为 xx-xx-xx-xx-xx-xx
	char *p = terminal_mac;
	while (*p) {
		if (*p == ':') *p = '-';
		p++;
	}

	// 获取路由器MAC地址
	if (cfg_get_item("wifi_mac", router_mac, sizeof(router_mac)) != 0) {
		if (cfg_get_item("at_wifi_mac", router_mac, sizeof(router_mac)) != 0) {
			strcpy(router_mac, "00-1A-2B-3d-4D-5E");
		}
	}

	// 转换路由器MAC地址格式
	p = router_mac;
	while (*p) {
		if (*p == ':') *p = '-';
		p++;
	}

	// 获取ICCID
	if (cfg_get_item("ziccid", iccid, sizeof(iccid)) != 0) {
		strcpy(iccid, "898604891523D0022173");
	}

	// 获取认证服务器域名
	if (cfg_get_item("one_link_customers_get_token_url", token_url, sizeof(token_url)) == 0 && strlen(token_url) > 0) {
		// 从URL中提取域名
		char *start = strstr(token_url, "://");
		if (start) {
			start += 3;
			char *end = strchr(start, '/');
			if (end) {
				int len = end - start;
				if (len < sizeof(domain) - 1) {
					strncpy(domain, start, len);
					domain[len] = '\0';
				}
			} else {
				strncpy(domain, start, sizeof(domain) - 1);
			}
		}
	}

	if (strlen(domain) == 0) {
		strcpy(domain, "wireless.cmonelink.com");
	}

	// 构建OneLink认证URL
	snprintf(onelink_url, sizeof(onelink_url),
		"https://%s/wirelessAuthentication/authentication?terminalMac=%s&mac=%s&iccid=%s&redirectTo=%s",
		domain, terminal_mac, router_mac, iccid, redirect_url);
#endif

	printf(" redirecting to OneLink: %s\n", onelink_url);

	// 重定向到OneLink认证页面
	websRedirect(wp, onelink_url);

	return 1;
}

/**
 * WiFiDog认证服务器接口 - Portal页面处理
 * 处理认证成功后的门户页面请求
 */
int wifidog_auth_portal_handler(webs_t wp, char_t *urlPrefix, char_t *webDir, int arg, char_t *url, char_t *path, char_t *query)
{
	char_t *gw_id = websGetVar(wp, T("gw_id"), T(""));
	char_t *client_mac = websGetVar(wp, T("mac"), T(""));
	char_t *token = websGetVar(wp, T("token"), T(""));

	printf("corem wifidog_auth_portal_handler: gw_id:%s, mac:%s, token:%s\n", gw_id, client_mac, token);

	// 返回与auth_success.html完全一致的认证成功页面
	const char *success_html =
		"<!DOCTYPE html>\n"
		"<html lang=\"zh-CN\">\n"
		"<head>\n"
		"  <meta charset=\"UTF-8\">\n"
		"  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n"
		"  <title>认证成功</title>\n"
		"  <style>\n"
		"    body {\n"
		"      margin: 0;\n"
		"      padding: 0;\n"
		"      font-family: \"Segoe UI\", Tahoma, Geneva, Verdana, sans-serif;\n"
		"      background: linear-gradient(to right, #e0f7fa, #ffffff);\n"
		"      display: flex;\n"
		"      justify-content: center;\n"
		"      align-items: center;\n"
		"      min-height: 100vh;\n"
		"    }\n"
		"\n"
		"    .box {\n"
		"      text-align: center;\n"
		"      background-color: #ffffff;\n"
		"      padding: 2em 1.5em;\n"
		"      border-radius: 1.2em;\n"
		"      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);\n"
		"      animation: fadeIn 1s ease-in-out;\n"
		"      width: 90%;\n"
		"      max-width: 400px;\n"
		"      box-sizing: border-box;\n"
		"    }\n"
		"\n"
		"    .icon {\n"
		"      width: 80px;\n"
		"      height: 80px;\n"
		"      margin: 0 auto 1em;\n"
		"    }\n"
		"\n"
		"    .icon svg {\n"
		"      width: 100%;\n"
		"      height: 100%;\n"
		"    }\n"
		"\n"
		"    .box h1 {\n"
		"      font-size: 1.8em;\n"
		"      color: #4CAF50;\n"
		"      margin-bottom: 0.4em;\n"
		"    }\n"
		"\n"
		"    .box h2 {\n"
		"      font-size: 1.1em;\n"
		"      color: #555;\n"
		"      margin-top: 0;\n"
		"    }\n"
		"\n"
		"    @keyframes fadeIn {\n"
		"      from {\n"
		"        opacity: 0;\n"
		"        transform: translateY(-10px);\n"
		"      }\n"
		"      to {\n"
		"        opacity: 1;\n"
		"        transform: translateY(0);\n"
		"      }\n"
		"    }\n"
		"  </style>\n"
		"</head>\n"
		"<body>\n"
		"  <div class=\"box\">\n"
		"    <div class=\"icon\">\n"
		"      <!-- ✅ SVG 圆形打勾图标 -->\n"
		"      <svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\">\n"
		"        <circle cx=\"50\" cy=\"50\" r=\"45\" stroke=\"#4CAF50\" stroke-width=\"8\" fill=\"none\"/>\n"
		"        <polyline points=\"30,55 45,70 75,40\" fill=\"none\" stroke=\"#4CAF50\" stroke-width=\"8\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n"
		"      </svg>\n"
		"    </div>\n"
		"    <h1>认证成功！</h1>\n"
		"    <h2>现在您可以愉快地上网了 😊</h2>\n"
		"  </div>\n"
		"</body>\n"
		"</html>";

	websWrite(wp, T("HTTP/1.1 200 OK\r\n"));
	websWrite(wp, T("Content-Type: text/html; charset=UTF-8\r\n"));
	websWrite(wp, T("Cache-Control: no-cache\r\n"));
	websWrite(wp, T("Connection: close\r\n\r\n"));
	websWrite(wp, success_html);

	websDone(wp, 200);
	return 1;
}

/**
 * WiFiDog认证服务器接口 - 消息页面处理
 * 处理错误消息页面请求
 */
int wifidog_auth_msg_handler(webs_t wp, char_t *urlPrefix, char_t *webDir, int arg, char_t *url, char_t *path, char_t *query)
{
	char_t *message = websGetVar(wp, T("message"), T("Unknown error"));

	printf("corem wifidog_auth_msg_handler: message:%s\n", message);

	// 返回错误页面
	websWrite(wp, T("HTTP/1.1 200 OK\r\n"));
	websWrite(wp, T("Content-Type: text/html\r\n"));
	websWrite(wp, T("Connection: close\r\n\r\n"));
	websWrite(wp, T("<html><head><title>Authentication Error</title></head>"));
	websWrite(wp, T("<body><h1>Authentication Error</h1>"));
	websWrite(wp, T("<p>%s</p>"), message);
	websWrite(wp, T("<p><a href='javascript:history.back()'>Go Back</a></p>"));
	websWrite(wp, T("</body></html>"));

	websDone(wp, 200);
	return 1;
}

/**
 * WiFiDog认证服务器接口 - Ping处理
 * 处理WiFiDog的心跳检测请求
 */
int wifidog_auth_ping_handler(webs_t wp, char_t *urlPrefix, char_t *webDir, int arg, char_t *url, char_t *path, char_t *query)
{
	char_t *gw_id = websGetVar(wp, T("gw_id"), T(""));
	char_t *sys_uptime = websGetVar(wp, T("sys_uptime"), T("0"));
	char_t *sys_memfree = websGetVar(wp, T("sys_memfree"), T("0"));
	char_t *sys_load = websGetVar(wp, T("sys_load"), T("0"));
	char_t *wifidog_uptime = websGetVar(wp, T("wifidog_uptime"), T("0"));

	printf("corem wifidog_auth_ping_handler: gw_id:%s, uptime:%s\n", gw_id, sys_uptime);

	// 返回Pong响应
	websWrite(wp, T("HTTP/1.1 200 OK\r\n"));
	websWrite(wp, T("Content-Type: text/plain\r\n"));
	websWrite(wp, T("Connection: close\r\n\r\n"));
	websWrite(wp, T("Pong"));

	websDone(wp, 200);
	return 1;
}

/**
 * WiFiDog认证服务器接口 - 认证处理
 * 处理WiFiDog的认证验证请求，这是最核心的接口
 */
int wifidog_auth_auth_handler(webs_t wp, char_t *urlPrefix, char_t *webDir, int arg, char_t *url, char_t *path, char_t *query)
{
	char_t *stage = websGetVar(wp, T("stage"), T(""));
	char_t *ip = websGetVar(wp, T("ip"), T(""));
	char_t *mac = websGetVar(wp, T("mac"), T(""));
	char_t *token = websGetVar(wp, T("token"), T(""));
	char_t *incoming = websGetVar(wp, T("incoming"), T("0"));
	char_t *outgoing = websGetVar(wp, T("outgoing"), T("0"));
	char_t *gw_id = websGetVar(wp, T("gw_id"), T(""));

	printf("corem wifidog_auth_auth_handler: stage:%s, ip:%s, mac:%s, token:%s\n",
		   stage, ip, mac, token);

	// 根据不同的stage处理不同的认证阶段
	if (strcmp(stage, "login") == 0) {
		// 登录阶段 - 检查用户是否已经通过OneLink认证

		// 检查认证标志文件
		char auth_flag_file[256] = {0};
		snprintf(auth_flag_file, sizeof(auth_flag_file), "/tmp/wifidog_auth_%s", mac);

		FILE *fp = fopen(auth_flag_file, "r");
		int is_authenticated = 0;

		if (fp) {
			char flag_content[32] = {0};
			if (fgets(flag_content, sizeof(flag_content), fp)) {
				if (strstr(flag_content, "authenticated")) {
					is_authenticated = 1;
				}
			}
			fclose(fp);
		}

		// 如果已认证或有有效token，则允许访问
		if (is_authenticated || strlen(token) > 0) {
			printf("corem auth success for MAC:%s, token:%s, authenticated:%d\n", mac, token, is_authenticated);

			// // 放行该MAC地址的网络访问
			// char cmd[256] = {0};
			// snprintf(cmd, sizeof(cmd), "/sbin/one_link_authenticated.sh allow %s", mac);
			// int ret = system(cmd);
			// if (ret != 0) {
			// 	printf("corem failed to allow MAC:%s, return code:%d\n", mac, ret);
			// } else {
			// 	printf("corem successfully allowed MAC:%s\n", mac);
			// }

			// 清理临时文件
			if (is_authenticated) {
				unlink(auth_flag_file);

				char token_file[256] = {0};
				snprintf(token_file, sizeof(token_file), "/tmp/wifidog_token_%s", mac);
				unlink(token_file);

				printf("corem cleaned up temp files for MAC:%s\n", mac);
			}

			// 返回认证成功
			websWrite(wp, T("HTTP/1.1 200 OK\r\n"));
			websWrite(wp, T("Content-Type: text/plain\r\n"));
			websWrite(wp, T("Connection: close\r\n\r\n"));
			websWrite(wp, T("Auth: 1"));
		} else {
			printf("corem auth failed for MAC:%s, no token and not authenticated\n", mac);

			// 返回认证失败
			websWrite(wp, T("HTTP/1.1 200 OK\r\n"));
			websWrite(wp, T("Content-Type: text/plain\r\n"));
			websWrite(wp, T("Connection: close\r\n\r\n"));
			websWrite(wp, T("Auth: 0"));
		}
	} else if (strcmp(stage, "counters") == 0) {
		// 计数器更新阶段 - 更新用户的流量统计
		printf("corem updating counters for MAC:%s, in:%s, out:%s\n", mac, incoming, outgoing);

		// 返回继续认证状态
		websWrite(wp, T("HTTP/1.1 200 OK\r\n"));
		websWrite(wp, T("Content-Type: text/plain\r\n"));
		websWrite(wp, T("Connection: close\r\n\r\n"));
		websWrite(wp, T("Auth: 1"));
	} else if (strcmp(stage, "logout") == 0) {
		// 登出阶段 - 用户主动登出或超时
		printf("corem logout for MAC:%s\n", mac);

		// 返回登出成功
		websWrite(wp, T("HTTP/1.1 200 OK\r\n"));
		websWrite(wp, T("Content-Type: text/plain\r\n"));
		websWrite(wp, T("Connection: close\r\n\r\n"));
		websWrite(wp, T("Auth: 0"));
	} else {
		// 未知阶段
		printf("corem unknown auth stage:%s for MAC:%s\n", stage, mac);

		websWrite(wp, T("HTTP/1.1 200 OK\r\n"));
		websWrite(wp, T("Content-Type: text/plain\r\n"));
		websWrite(wp, T("Connection: close\r\n\r\n"));
		websWrite(wp, T("Auth: 0"));
	}

	websDone(wp, 200);
	return 1;
}

/**
 * WiFiDog goform统一处理函数
 * 根据goformId参数分发到不同的处理函数
 */
int wifidog_goform_handler(webs_t wp, char_t *urlPrefix, char_t *webDir, int arg, char_t *url, char_t *path, char_t *query)
{
	char_t *goform_id = websGetVar(wp, T("goformId"), T(""));

	printf("corem wifidog_goform_handler: goformId=%s\n", goform_id);

	if (strcmp(goform_id, "ONELINK_LOGIN") == 0) {
		return wifidog_auth_login_handler(wp, urlPrefix, webDir, arg, url, path, query);
	} else if (strcmp(goform_id, "ONELINK_PORTAL") == 0) {
		return wifidog_auth_portal_handler(wp, urlPrefix, webDir, arg, url, path, query);
	} else if (strcmp(goform_id, "ONELINK_MSG") == 0) {
		return wifidog_auth_msg_handler(wp, urlPrefix, webDir, arg, url, path, query);
	} else if (strcmp(goform_id, "ONELINK_PING") == 0) {
		return wifidog_auth_ping_handler(wp, urlPrefix, webDir, arg, url, path, query);
	} else if (strcmp(goform_id, "ONELINK_AUTH") == 0) {
		return wifidog_auth_auth_handler(wp, urlPrefix, webDir, arg, url, path, query);
	} else {
		printf("corem unknown goformId: %s\n", goform_id);

		// 返回错误响应
		websWrite(wp, T("HTTP/1.1 404 Not Found\r\n"));
		websWrite(wp, T("Content-Type: text/plain\r\n"));
		websWrite(wp, T("Connection: close\r\n\r\n"));
		websWrite(wp, T("Unknown goformId: %s"), goform_id);

		websDone(wp, 404);
		return 1;
	}
}
#endif