# /*****************************************************************************
#* ��Ȩ���� (C)2015, ����ͨѶ�ɷ����޹�˾��
#* 
#* �ļ�����:     Makefile
#* �ļ���ʶ:     Makefile
#* ����ժҪ:     Makefile of ZTE applications
#* ʹ�÷���:     void
#* 
#* �޸�����        �汾��      �޸ı��        �޸���          �޸�����
#* -----------------------------------------------------------------------------
#* 2015/02/10      V1.0        Create          ������          ����
#* 
# ******************************************************************************/

#*******************************************************************************
# include ZTE application makefile
#*******************************************************************************
include $(zte_app_mak)

#*******************************************************************************
# execute
#*******************************************************************************
EXEC    = goahead


# User Management switch
#UMSW	= -DUSER_MANAGEMENT_SUPPORT
CPU_PUB_ROOT=$(TOPDIR_AP)/../pub
INTERFACEDIR = ../interface5.0
INTERFACELIB = $(INTERFACEDIR)/libwebinterface.a
#*******************************************************************************
# objects asp.o ejlex.o ejparse.o umui.o
#*******************************************************************************
OBJS    =		  balloc.o base64.o cgi.o default.o \
				  form.o \
				  h.o handler.o mime.o misc.o page.o  \
				  ringq.o \
				  sock.o sockGen.o \
				  sym.o uemf.o value.o \
				  webs.o websuemf.o goahead.o \
   				  ../../qrzl_app/qrzl_utils.o \

#*******************************************************************************
# include path
#*******************************************************************************
CFLAGS	+= -DWEBS -DUEMF -DOS="LINUX" -DLINUX $(UMSW) $(DASW) $(SSLSW) $(IFMODSW)
CFLAGS  += -Wall -fno-strict-aliasing -I../interface5.0/net -I.
#CFLAGS	+= -I$(ROOTDIR)/lib/libnvram -I$(ROOTDIR)/$(LINUXDIR)/drivers/char -I$(ROOTDIR)/$(LINUXDIR)/include
#CFLAGS  += -I$(ROOTDIR)/$(LINUXDIR)/drivers/flash -I$(INTERFACEDIR)

#OTHERS	= -DB_STATS -DB_FILL -DDEBUG
CFLAGS	+= -I../../include -g
#CFLAGS	+= -I../../zte_sqlite
#CFLAGS  += -I../../soft_timer
CFLAGS	+= -I$(zte_lib_path)/libsqlite
CFLAGS	+= -I$(zte_lib_path)/libsoft_timer
CFLAGS  += -I../../at_server
# qrzl
CFLAGS  += -I../../qrzl_app
CFLAGS	+= -I$(zte_lib_path)/libnvram
CFLAGS  += -I$(zte_lib_path)/libezxml
CFLAGS  += -I$(zte_lib_path)/libmxml
ifeq ($(CONFIG_USE_WEBUI_SSL),yes)
OBJS += websSSL.o
CFLAGS  += -I$(zte_lib_path)/libssl/install/include
CFLAGS  += -I$(CPU_PUB_ROOT)/project/zx297520v3/include/nv
CFLAGS	+= -DWEBS_SSL_SUPPORT -DOPENSSL
LDLIBS += -L$(zte_lib_path)/libssl/install/lib -lssl -lcrypto
LDLIBS += -ldl
endif
CFLAGS	+= -DWEBINSPECT_FIX
# Enable QRZL WiFiDog OneLink authentication server support
CFLAGS	+= -DQRZL_WIFIDOG_ONELINK
LDLIBS += -lpthread
LDLIBS += -lsoftap -L$(zte_lib_path)/libsoftap
LDLIBS  += -lsoft_timer -L$(zte_lib_path)/libsoft_timer
#LDLIBS  += -lkey -L$(zte_lib_path)/libkey
LDLIBS  += -lamt -L$(zte_lib_path)/libamt
LDLIBS  += -lcpnv -L$(zte_lib_path)/libcpnv

ifeq ($(CONFIG_USE_WEBUI_SECURITY),yes)
CFLAGS	+= -DWEBS_SECURITY
endif
#*******************************************************************************
# macro definition
#*******************************************************************************
ifeq ($(CONFIG_USE_WEBUI_ZIP),yes)
OBJS += ioapi.o unzip.o
CFLAGS	+= -D_USE_WEBUI_ZIP
CFLAGS  += -I$(zte_lib_path)/zlib/install/include
LDFLAGS += -L$(zte_lib_path)/zlib/install/lib -lz
endif

ifeq ($(CUSTOM_MODEL), MF253S2)
ifeq ($(CUSTOM_OPERATOR), CM_CN)
CFLAGS	+= -DCUSTOM_VERSION_MF253S2_CM_CN
endif
endif

##changed by huangmin10103007 for new partition, 20130116 begin
ifeq ($(FEATURE_ZTE_CPE_CFG), YES)
CFLAGS += -D_ZTE_CFG_
endif
##changed by huangmin10103007 for new partition, 20130116 end
# Digest Access switch
# DASW	= -DDIGEST_ACCESS_SUPPORT

# SSL switches
ifeq ("$(CONFIG_USER_GOAHEAD_SSL)", "y")
SSLPATCHFILE = matrix_ssl.o sslSocket.o
MATRIXDIR = $(ROOTDIR)/user/matrixssl-1.8.3
SSLINC = $(MATRIXDIR)
SSLLIB = $(MATRIXDIR)/src/libmatrixsslstatic.a
SSLSW = -DWEBS_SSL_SUPPORT -DMATRIX_SSL -I$(SSLINC)
endif

#CONF_H	= $(ROOTDIR)/$(LINUXDIR)/include/linux/autoconf.h
#CONF_H	= $(LINUX_DIR)/include/linux/autoconf.h
#UCONF_H	= $(ROOTDIR)/config/autoconf.h

#BUSYCONF_H = $(USR_DIR)/busybox-1.15.0/include/autoconf.h
#UCONF_H	= $(CFG_DIR)/autoconf.h

ifeq ($(FEATURE_DLNA), YES)
# DLNA settings(libztedlna.so)
LDLIBS += -L../../DMS -lztedlna -lpthread
endif

#*******************************************************************************
# library
#*******************************************************************************
LDLIBS	+= -lnvram -lzte_pbm -lsqlite -lwlan_interface
ifeq ($(LINUX_TYPE),uClinux)
LDLIBS	+= -lpthread
else
LDLIBS	+= -lpthread
endif


ifeq ($(FEATURE_DLNA), YES)
LDLIBS	+= -L../../DMS -lztedlna
endif

LDFLAGS	+= $(SSLLIB) $(IFMODLIB) $(INTERFACELIB) -lm -lgcc_s

#LDFLAGS += -Wl,-elf2flt=-s131072

CFLAGS += -I$(zte_lib_path)/libssl/install/include
LDLIBS += -L$(zte_lib_path)/libssl/install/lib -lcrypto

#*******************************************************************************
# library path
#*******************************************************************************
LDLIBS  += -L$(zte_lib_path)/libnvram
#LDLIBS  += -L$(zte_lib_path)/libsoft_timer
LDLIBS  += -L$(zte_lib_path)/libsqlite
LDLIBS  += -L$(zte_lib_path)/libzte_pbm
LDLIBS  += -L$(zte_lib_path)/libmxml
LDLIBS  += -L$(zte_lib_path)/libwlan_interface

#*******************************************************************************
# targets
#*******************************************************************************
all:$(EXEC)

#
#	Primary link
#
#$(CC) -o $@ $(OBJS) $(LDFLAGS) $(EXTRALIBS) $(LDLIBS)
$(EXEC): $(OBJS) $(INTERFACELIB)
	#$(LD)  -o $@ $(OBJS) $(LDFLAGS)  $(LDLIBS)
	$(CC) -o $@ $(OBJS) $(LDFLAGS) $(EXTRALIBS) $(LDLIBS)

root_fs:
	cp $(EXEC) $(EXEC).elf
	$(ROMFSINST) /bin/$(EXEC)

romfs:
	cp $(EXEC) $(EXEC).elf
	$(ROMFSINST)  /bin/$(EXEC)

clean:
	-rm -f $(EXEC) *.o *.elf

#
#	Dependencies
#
asp.o:  webs.h wsIntrn.h ej.h ejIntrn.h uemf.h

balloc.o: balloc.c uemf.h

base64.o:  base64.c webs.h wsIntrn.h  ej.h ejIntrn.h uemf.h

cgi.o:  webs.h wsIntrn.h uemf.h

default.o:  default.c webs.h wsIntrn.h ej.h ejIntrn.h uemf.h $(CONF_H)

ejlex.o:  ejlex.c ej.h ejIntrn.h uemf.h

ejparse.o:  ejparse.c ej.h ejIntrn.h uemf.h

emfdb.o:  emfdb.h wsIntrn.h uemf.h

form.o:  form.c webs.h wsIntrn.h ej.h ejIntrn.h uemf.h

h.o:  h.c uemf.h

handler.o:  handler.c webs.h wsIntrn.h ej.h ejIntrn.h uemf.h

md5c.o:  md5.h wsIntrn.h uemf.h

mime.o:  mime.c webs.h wsIntrn.h ej.h ejIntrn.h uemf.h

misc.o:  misc.c uemf.h

page.o:  page.c webs.h wsIntrn.h ej.h ejIntrn.h uemf.h

ringq.o:  ringq.c uemf.h

rom.o:  rom.c webs.h wsIntrn.h ej.h ejIntrn.h uemf.h

security.o:  security.c webs.h wsIntrn.h ej.h ejIntrn.h uemf.h

sock.o:  sock.c uemf.h

sockGen.o:  sockGen.c uemf.h $(CONF_H)

sym.o:  sym.c uemf.h

uemf.o:  uemf.c uemf.h

um.o:  webs.h wsIntrn.h um.h uemf.h

umui.o:  webs.h wsIntrn.h um.h uemf.h

url.o:  url.c webs.h wsIntrn.h ej.h ejIntrn.h uemf.h

value.o:  value.c uemf.h

webrom.o:  webrom.c webs.h wsIntrn.h uemf.h

webs.o:  webs.c webs.h wsIntrn.h ej.h ejIntrn.h uemf.h $(CONF_H)

websda.o:  webs.h wsIntrn.h websda.h uemf.h

websuemf.o:  websuemf.c webs.h wsIntrn.h ej.h ejIntrn.h uemf.h

websSSL.o:  websSSL.c websSSL.h wsIntrn.h ej.h ejIntrn.h uemf.h

#goahead.o:  goahead.c wsIntrn.h webs.h ej.h ejIntrn.h uemf.h $(CONF_H) $(UCONF_H) $(BUSYCONF_H)
goahead.o:  goahead.c wsIntrn.h webs.h ej.h ejIntrn.h uemf.h $(CONF_H) $(BUSYCONF_H)
