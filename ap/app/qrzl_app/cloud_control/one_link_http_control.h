#ifndef __QRZL_ONE_LINK_HTTP_CONTROL_H_
#define __QRZL_ONE_LINK_HTTP_CONTROL_H_

void* one_link_http_control_start();

// 获取短信验证码
int get_sms_code(char *terminalMac, char *phoneNum);

#ifdef QRZL_AUTH_QC_ONE_LINK
//齐成获取token的接口
void qc_get_token_code()
#endif

/**
 * 终端登录验证
 */
int one_link_terminal_auth(char *terminalMac, char *phoneNum, char *code);

/**
 * 终端信息上报
 * push_type : 1 上报上线， 0 上报下线
 */
void one_link_push_device_info(int push_type, char *terminalMac, char *terminalIp);

/**
 * 获取已认证的列表，原生字符串
 */
void one_link_get_auth_list_origin(char *rev_response);

// 获取该设备已认证的列表
char *one_link_get_auth_list();

/**
 * 验证终端是否已认证
 * @param mac 设备MAC
 * @param terminalMac 终端MAC
 * @param servAddr 回调地址
 * @return 1: 已认证    2：未认证
 */
int one_link_terminal_is_authed(const char *mac, const char *terminalMac, const char *servAddr);

void one_link_change_last_station_mac(char *station_mac);
int one_link_check_mac_in_last_station(char *station_mac);

/**
 * 内置自定义认证页面
 */
const char *get_auth_html();

void init_onelink_authed_info();

// 获取第三方认证页面地址(可能没有)
int one_link_get_customers_page_url(char *customers_page_url, int url_len, char *mac, char *terminalMac, char *client_ip, char *servAddr);

#endif