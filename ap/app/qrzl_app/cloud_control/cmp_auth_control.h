#ifndef __QRZL_CMP_AUTH_CONTROL_H_
#define __QRZL_CMP_AUTH_CONTROL_H_

/**
 * 设备上/下线上报
 */
void device_line_type_push(const int push_type, const char *mac, const char *terminalMac, const char *client_ip);

void* cmp_http_control_start();
int cmp_check_mac_in_last_station(char *station_mac);
void cmp_change_last_station_mac(char *station_mac);

/**
 * 判断终端设备是否已经认证
 * @param mac 设备MAC
 * @param terminal_mac 终端设备MAC
 * @param servAddr 回调地址
 * @return 0 未认证； 1 已认证
 */
int cmp_terminal_is_authed(const char* mac, const char* terminal_mac, const char* servAddr);

void init_cmp_authed_info();

#ifdef QRZL_AUTH_QC_CMP
// QC获取电信相关信息封装便于轮询调用
void qc_cmp_get_token();
#endif

#endif