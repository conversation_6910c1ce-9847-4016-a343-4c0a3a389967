#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <errno.h>
#include <ctype.h>
#include <sys/time.h>

#include "../common_utils/cjson.h"
#include "../common_utils/http_client.h"
#include "../qrzl_utils.h"
#include "softap_api.h"
#include "nv_api.h"


#define KEY_ONE_LINK_GET_TOKEN_URL "ONE_LINK_customers_get_token_url"

extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

static char one_link_customers_get_token_url[256];

/**
 * 初始化配置信息
 */
static int init_customers_info()
{
    int res_url = cfg_get_item(KEY_ONE_LINK_GET_TOKEN_URL, one_link_customers_get_token_url, sizeof(one_link_customers_get_token_url));
    if (res_url != 0) {
        qrzl_log("Configuration item %s get error!", KEY_ONE_LINK_GET_TOKEN_URL);
    }
}


// =============================== 移动认证相关接口 =============================================

/**
 * @brief 获取短信验证码
 * @param mac          设备mac地址
 * @param terminalMac  终端mac地址
 * @param phoneNum     手机号码
 * @param iccid        iccid号码
 * @return             0 成功，-1 失败
 */
int xianji_get_sms_code(const char *mac, const char *terminalMac, const char *phoneNum, const char *iccid) {

    init_customers_info();

    char *interface_str = "/SMS_verification_code";
    char request_url[512] = {0};
    char response[HTTP_RESPONSE_MAX] = {0};

    // mac 格式转换
    char mac_formatted[33] = {0};
    char terminalMac_formatted[33] = {0};
    convert_mac_format(mac, mac_formatted, sizeof(mac_formatted), '-');
    convert_mac_format(terminalMac, terminalMac_formatted, sizeof(terminalMac_formatted), '-');

    // 构建请求URL
    snprintf(request_url, sizeof(request_url), "%s%s?mac=%s&terminalMac=%s&phoneNum=%s&msisdn=%s",
            one_link_customers_get_token_url,
            interface_str,
            mac_formatted,
            terminalMac_formatted,
            phoneNum,
            iccid);
    
    // 发起http get请求
    int request_res = http_get(request_url, response, sizeof(response));
    if (request_res != HTTP_OK) {
        qrzl_log("xianji_get_sms_code http_get error");
        return -1;
    }

    // 解析响应
    cJSON *json = cJSON_Parse(response);
    if (!json) {
        qrzl_log("xianji_get_sms_code cJSON_Parse error");
        return -1;
    }

    // 获取 code 字段
    cJSON *code_item = cJSON_GetObjectItem(json, "code");
    if (code_item == NULL || code_item->type != cJSON_String) {
        qrzl_log("xianji_get_sms_code cJSON_GetObjectItem code error");
        cJSON_Delete(json);
        return -1;
    }

    if (strcmp("0", code_item->valuestring) != 0) {
        qrzl_log("xianji_get_sms_code fail, code: %s", code_item->valuestring);
        cJSON_Delete(json);
        return -1;
    } else {
        qrzl_log("xianji_get_sms_code success, code: %s", code_item->valuestring);
    }

    cJSON_Delete(json);
    return 0;
}


/**
 * 终端认证
 * @param mac          设备mac地址
 * @param terminalMac  终端mac地址
 * @param phoneNum     手机号码
 * @param iccid        iccid号码
 * @param smsCode      短信验证码
 * @return             0 成功，-1 失败
 */
int xianji_terminal_auth(const char *mac, const char *terminalMac, const char *phoneNum, const char *iccid, const char *smsCode) {

    init_customers_info();

    char *interface_str = "/terminal_login_verification";
    char request_url[512] = {0};
    char response[HTTP_RESPONSE_MAX] = {0};

    // mac 格式转换
    char mac_formatted[33] = {0};
    char terminalMac_formatted[33] = {0};
    convert_mac_format(mac, mac_formatted, sizeof(mac_formatted), '-');
    convert_mac_format(terminalMac, terminalMac_formatted, sizeof(terminalMac_formatted), '-');

    // 构建请求URL
    snprintf(request_url, sizeof(request_url), "%s%s?mac=%s&terminalMac=%s&phoneNum=%s&msisdn=%s&verifyCode=%s",
            one_link_customers_get_token_url,
            interface_str,
            mac_formatted,
            terminalMac_formatted,
            phoneNum,
            iccid,
            smsCode);
    
    // 发起http get请求
    int request_res = http_get(request_url, response, sizeof(response));
    if (request_res != HTTP_OK) {
        qrzl_log("xianji_terminal_auth http_get error");
        return -1;
    }

    // 解析响应
    cJSON *json = cJSON_Parse(response);
    if (!json) {
        qrzl_log("xianji_terminal_auth cJSON_Parse error");
        return -1;
    }

    // 获取 code 字段
    cJSON *code_item = cJSON_GetObjectItem(json, "code");
    if (code_item == NULL || code_item->type != cJSON_String) {
        qrzl_log("xianji_terminal_auth cJSON_GetObjectItem code error");
        cJSON_Delete(json);
        return -1;
    }

    if (strcmp("0", code_item->valuestring) != 0) {
        qrzl_log("xianji_terminal_auth fail, code: %s", code_item->valuestring);
        cJSON_Delete(json);
        return -1;
    } else {
        qrzl_log("xianji_terminal_auth success, code: %s", code_item->valuestring);
    }

    cJSON_Delete(json);

    return 0;
}

/**
 * 获取已认证终端列表
 * @param mac   设备mac地址
 * @param iccid iccid号码
 * @return      认证终端mac列表字符串（格式 "MAC1;MAC2;MAC3;"），需要调用者 free()，失败返回 NULL
 */
char *xianji_authed_list(const char *mac, const char *iccid) {

    init_customers_info();

    char *interface_str = "/authenticated_terminal_query";
    char request_url[512] = {0};
    char response[HTTP_RESPONSE_MAX] = {0};

    // mac 格式转换
    char mac_formatted[33] = {0};
    convert_mac_format(mac, mac_formatted, sizeof(mac_formatted), '-');

    // 构建请求URL
    snprintf(request_url, sizeof(request_url), "%s%s?mac=%s&msisdn=%s",
            one_link_customers_get_token_url,
            interface_str,
            mac_formatted,
            iccid);
    
    // 发起http get请求
    int request_res = http_get(request_url, response, sizeof(response));
    if (request_res != HTTP_OK) {
        qrzl_log("xianji_authed_list http_get error");
        return NULL;
    }

    qrzl_log("yyyyyyy xianji_authed_list response: %s", response);

    // 解析响应
    cJSON *json = cJSON_Parse(response);
    if (!json) {
        qrzl_log("xianji_authed_list cJSON_Parse error");
        return NULL;
    }

    // 获取 code 字段
    cJSON *code_item = cJSON_GetObjectItem(json, "code");
    if (!code_item || code_item->type != cJSON_String) {
        qrzl_log("xianji_authed_list cJSON_GetObjectItem code error");
        cJSON_Delete(json);
        return NULL;
    }

    qrzl_log("xianji_authed_list success, code: %d", code_item->valueint);

    // 获取 result 字段
    cJSON *result_item = cJSON_GetObjectItem(json, "result");
    if (!result_item || result_item->type != cJSON_Object) {
        qrzl_log("xianji_authed_list cJSON_GetObjectItem result error");
        cJSON_Delete(json);
        return NULL;
    }

    // 获取 terminalList 字段 （数组）
    cJSON *terminalList_item = cJSON_GetObjectItem(result_item, "terminalList");
    if (!terminalList_item || terminalList_item->type != cJSON_Array) {
        qrzl_log("xianji_authed_list cJSON_GetObjectItem terminalList error");
        cJSON_Delete(json);
        return NULL;
    }

    // 数组大小
    int terminal_count = 0;
    int i;
    // 获取时间
    char datetime[32] = {0};
    get_local_time("%Y-%m-%d %H:%M:%S", datetime, sizeof(datetime));

    // 动态字符串构造
    size_t buffer_size = 5048;
    char *authed_mac_list = malloc(buffer_size);
    if (!authed_mac_list) {
        cJSON_Delete(json);
        return NULL;
    }

    terminal_count = cJSON_GetArraySize(terminalList_item);
    qrzl_log("xianji_authed_list terminal count: %d", terminal_count);
    for (i = 0; i < terminal_count; i++) {
        cJSON *terminal = cJSON_GetArrayItem(terminalList_item, i);
        if (terminal && terminal->type == cJSON_Object) {
            cJSON *t_mac = cJSON_GetObjectItem(terminal, "terminalMac");
            cJSON *t_time = cJSON_GetObjectItem(terminal, "expireTime");
            if (t_mac && t_mac->type == cJSON_String &&
                t_time && t_time->type == cJSON_String) {
                
                if (strcmp(t_time->valuestring, datetime) < 0) {
                    continue;  // 已过期
                }
                
                // 转换 MAC 格式
                char formatted_mac[32] = {0};
                convert_mac_format(t_mac->valuestring, formatted_mac, sizeof(formatted_mac), ':');

                // 拼接
                size_t used = strlen(authed_mac_list);
                size_t needed = strlen(formatted_mac) + 2;
                if (used + needed >= buffer_size) break;  // 超出，简单跳出

                strcat(authed_mac_list, formatted_mac);
                strcat(authed_mac_list, ";");
            }
        }
    }

    cJSON_Delete(json);

    return authed_mac_list;  // 需要调用者 free()
}

/**
 * 终端上下线状态上报
 * @param push_type    上线/下线 1:上线 0:下线
 * @param mac          设备mac地址
 * @param terminalMac  终端mac地址
 * @param longitude    经度
 * @param latitude     纬度
 * @param iccid        iccid号码
 * @return             0 成功，-1 失败
 */
int xianji_terminal_line_status_reporting(int push_type, const char *mac, const char *terminalMac, const char *longitude, const char *latitude, const char *iccid) {

    init_customers_info();

    char *interface_str = push_type == 1 ? "/terminal_online_information_reporting" : "/terminal_offline_information_reporting";
    char request_url[512] = {0};
    char response_body[HTTP_RESPONSE_MAX] = {0};

    // mac 格式转换
    char mac_str[33] = {0};
    char terminalMac_str[33] = {0};
    convert_mac_format(mac, mac_str, sizeof(mac_str), '-');
    convert_mac_format(terminalMac, terminalMac_str, sizeof(terminalMac_str), '-');

    // 获取时间
    char timestamp[32] = {0};
    get_local_time("%Y-%m-%d %H:%M:%S", timestamp, sizeof(timestamp));
    // 时间url转换
    char encoded_timestamp[64] = {0};
    url_encode(timestamp, encoded_timestamp);

    // 构建请求URL
    snprintf(request_url, sizeof(request_url), "%s%s?mac=%s&terminalMac=%s&longitude=%s&latitude=%s&msisdn=%s&imei=%s&busiTime=%s",
            one_link_customers_get_token_url,
            interface_str,
            mac_str,
            terminalMac_str,
            longitude,
            latitude,
            iccid,
            g_qrzl_device_static_data.imei,
            encoded_timestamp
        );

    // 发起http get请求
    int request_res = http_get(request_url, response_body, sizeof(response_body));
    if (request_res != HTTP_OK) {
        qrzl_log("terminal_line_status_reporting http_get error");
        return HTTP_ERROR;
    }

    // 解析响应
    cJSON *json_response = cJSON_Parse(response_body);
    if (!json_response) {
        qrzl_log("terminal_line_status_reporting cJSON_Parse error");
        return HTTP_ERROR;
    }

    // 获取 code 字段
    cJSON *code_item = cJSON_GetObjectItem(json_response, "code");
    if (code_item == NULL || code_item->type != cJSON_String) {
        qrzl_log("terminal_line_status_reporting cJSON_GetObjectItem code error");
        cJSON_Delete(json_response);
        return -1;
    }

    if (strcmp("0", code_item->valuestring) != 0) {
        qrzl_log("terminal_line_status_reporting fail, code: %s", code_item->valuestring);
        cJSON_Delete(json_response);
        return -1;
    } else {
        qrzl_log("terminal_line_status_reporting success, code: %s", code_item->valuestring);
    }

    cJSON_Delete(json_response);

    return HTTP_OK;
}